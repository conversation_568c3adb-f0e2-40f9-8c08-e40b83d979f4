import 'package:db_eats/bloc/support_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/cateringrequest.dart';
import 'package:db_eats/ui/messages/chat_screen.dart';
import 'package:db_eats/ui/orders/confirmed_orders_view.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void main() {
  runApp(const MessagesApp());
}

double getResponsiveSize(BuildContext context) {
  final width = MediaQuery.of(context).size.width;
  if (width < 360) return 12;
  if (width < 600) return 14;
  if (width < 900) return 16;
  return 18;
}

class MessagesApp extends StatelessWidget {
  const MessagesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        fontFamily: 'Inter',
        scaffoldBackgroundColor: const Color(0xFFF6F3EC),
      ),
      home: const MessagesPage(),
    );
  }
}

class MessagesPage extends StatefulWidget {
  const MessagesPage({super.key});

  @override
  State<MessagesPage> createState() => _MessagesPageState();
}

class _MessagesPageState extends State<MessagesPage> {
  final PageController _pageController = PageController();
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['Chats', 'Notifications'];

  void _openCart() {
    print('Opening cart');
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPage(),
        ));
  }
   late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }


  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    const double baseTextSize = 16.0;
    double responsiveTextSize = getResponsiveSize(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Messages',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w700,
            fontSize: twenty,
          ),
        ),
        backgroundColor: const Color(0xFFF6F3EC),
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Container(
        color: const Color(0xFFF6F3EC),
        child: Column(
          children: [
            SizedBox(
              height: size.height * 0.05,
              child: Row(
                children: List.generate(_tabs.length, (index) {
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedTabIndex = index;
                        });
                        _pageController.animateToPage(
                          index,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(
                          horizontal: size.width * 0.001,
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: _selectedTabIndex == index
                                  ? const Color(0xFFFFBE16)
                                  : Colors.transparent,
                              width: 2,
                            ),
                          ),
                        ),
                        child: Text(
                          _tabs[index],
                          style: TextStyle(
                            color: _selectedTabIndex == index
                                ? const Color(0xFF1F2122)
                                : const Color(0xFF66696D),
                            fontWeight: FontWeight.w600,
                            fontSize: baseTextSize,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
            SizedBox(height: size.height * 0.016),
            Expanded(
              child: Container(
                color: const Color(0xFFF6F3EC),
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _selectedTabIndex = index;
                    });
                  },
                  children: const [
                    ChatsTab(),
                    NotificationsTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: Initializer.cartCount ?? 0,
        onPressed: _openCart,
      ),
    );
  }
}

class ChatsTab extends StatefulWidget {
  const ChatsTab({super.key});

  @override
  State<ChatsTab> createState() => _ChatsTabState();
}

class _ChatsTabState extends State<ChatsTab> {
  int? _selectedIndex;
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;
  List<dynamic> _allIssues = [];
  late SupportBloc _supportBloc;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      print('Scroll position: ${_scrollController.position.pixels}');
      print('Max scroll extent: ${_scrollController.position.maxScrollExtent}');
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        print('Near bottom, loading more...');
        _loadMore();
      }
    });
    _supportBloc = SupportBloc();
    _supportBloc.add(ListIssuesEvent(page: 1, limit: 5, loadMore: false));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _supportBloc.close();
    super.dispose();
  }

  void _loadMore() {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    _supportBloc.add(ListIssuesEvent(
      page: _currentPage,
      limit: 5,
      loadMore: true,
    ));
  }

  void _resetPagination() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _isLoadingMore = false;
      _allIssues.clear();
    });
    _supportBloc.add(ListIssuesEvent(page: 1, limit: 5, loadMore: false));
  }

  @override
  Widget build(BuildContext context) {
    double responsiveTextSize = getResponsiveSize(context);
    final Size size = MediaQuery.of(context).size;

    return BlocProvider.value(
      value: _supportBloc,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: size.width * 0.032),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (scrollInfo is ScrollEndNotification) {
                if (scrollInfo.metrics.pixels >=
                        scrollInfo.metrics.maxScrollExtent - 200 &&
                    !_isLoadingMore &&
                    _hasMoreData) {
                  print('Loading more from scroll notification');
                  _loadMore();
                }
              }
              return true;
            },
            child: BlocConsumer<SupportBloc, SupportState>(
              listener: (context, state) {
                if (state is ListIssuesSuccess) {
                  setState(() {
                    _isLoadingMore = false;

                    if (_currentPage == 1) {
                      _allIssues = List.from(state.data?.issues ?? []);
                    } else {
                      final newIssues = state.data?.issues ?? [];
                      _allIssues.addAll(newIssues.where((newIssue) =>
                          !_allIssues
                              .any((existing) => existing.id == newIssue.id)));
                    }

                    final pagination = state.data?.pagination;
                    if (pagination != null) {
                      _hasMoreData = (pagination.page * pagination.limit) <
                          pagination.totalLength;
                    } else {
                      _hasMoreData = false;
                    }
                  });
                } else if (state is ListIssuesFailed) {
                  setState(() {
                    _isLoadingMore = false;
                    if (_currentPage > 1) _currentPage--;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to load data: ${state.message}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is ListIssuesLoading && _allIssues.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                            const Color(0xFFFFBE16)),
                      ),
                    ),
                  );
                }

                if (_allIssues.isEmpty) {
                  if (state is ListIssuesFailed) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline,
                                size: 48, color: Color(0xFF66696D)),
                            const SizedBox(height: 16),
                            Text(
                              'Failed to load issues',
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2122)),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              state.message,
                              style: const TextStyle(
                                  fontSize: 14, color: Color(0xFF66696D)),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                _resetPagination();
                                _supportBloc
                                    .add(ListIssuesEvent(page: 1, limit: 5));
                              },
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Text('No issues found'),
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  itemCount: _allIssues.length +
                      (_isLoadingMore || !_hasMoreData ? 1 : 0),
                  padding: EdgeInsets.all(size.width * 0.026),
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == _allIssues.length) {
                      if (_isLoadingMore) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                              child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                const Color(0xFFFFBE16)),
                          )),
                        );
                      }
                      if (!_hasMoreData) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: Text('No more issues to load',
                                style: TextStyle(
                                    color: Color(0xFF66696D), fontSize: 14)),
                          ),
                        );
                      }
                    }

                    final issue = _allIssues[index];
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedIndex =
                              _selectedIndex == index ? null : index;
                        });
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ChatScreen(
                              issueId: issue.id,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: _selectedIndex == index
                              ? const Color(0xFFF1F2F3)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: size.height * 0.013,
                                  horizontal: size.width * 0.026),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          issue.categoryName ?? 'General Issue',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w600,
                                            fontSize: 1 * responsiveTextSize,
                                          ),
                                        ),
                                        const SizedBox(height: 3),
                                        Text(
                                          issue.description ??
                                              'No description available',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w400,
                                            fontSize: 0.86 * responsiveTextSize,
                                            color: const Color(0xFF414346),
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 3),
                                        Text(
                                          "Order ID: ${issue.orderNumber ?? 'N/A'}",
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w400,
                                            fontSize: 0.86 * responsiveTextSize,
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                        const SizedBox(height: 1),
                                        Text(
                                          _formatTimestamp(issue.createdAt),
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w400,
                                            fontSize: 0.86 * responsiveTextSize,
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 6, vertical: 0),
                                    decoration: BoxDecoration(
                                      color: _getStatusColor(issue.status),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      issue.status ?? 'Unknown',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 0.86 * responsiveTextSize,
                                        color:
                                            _getStatusTextColor(issue.status),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (index < _allIssues.length - 1)
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 4.0),
                                child: const Divider(
                                  height: 1,
                                  thickness: 1,
                                  color: Color(0xFFE1E3E6),
                                  indent: 0,
                                  endIndent: 0,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  String _formatTimestamp(String? createdAt) {
    if (createdAt == null) return 'Unknown';
    try {
      final dateTime = DateTime.parse(createdAt);
      final hour = dateTime.hour > 12
          ? dateTime.hour - 12
          : dateTime.hour == 0
              ? 12
              : dateTime.hour;
      final period = dateTime.hour >= 12 ? 'PM' : 'AM';
      return '${dateTime.month}/${dateTime.day}/${dateTime.year}, '
          '${hour}:${dateTime.minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return createdAt;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return const Color(0xFFE1E3E6);
      case 'closed':
        return const Color(0xFFE1E3E6);
      case 'pending':
        return const Color(0xFFE1E3E6);
      case 'in_progress':
        return const Color(0xFFE1E3E6);
      default:
        return const Color(0xFFE1E3E6);
    }
  }

  Color _getStatusTextColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return const Color(0xFF1F2122);
      case 'closed':
        return const Color(0xFF1F2122);
      case 'pending':
        return const Color(0xFF1F2122);
      case 'in_progress':
        return const Color(0xFF1F2122);
      default:
        return const Color(0xFF1F2122);
    }
  }
}

class NotificationItem {
  final int id;
  final String title;
  final String message;
  final String timestamp;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
  });
}

class NotificationsTab extends StatefulWidget {
  const NotificationsTab({super.key});

  @override
  State<NotificationsTab> createState() => _NotificationsTabState();
}

class _NotificationsTabState extends State<NotificationsTab> {
  int? _selectedIndex;
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;
  List<dynamic> _allNotifications = [];
  late SupportBloc _supportBloc;

  Map<int, bool> _loadingStates = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _supportBloc = SupportBloc();
    _loadInitialData();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadInitialData() {
    _supportBloc.add(ListNotificationsEvent());
  }

  void _loadMore() {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    _supportBloc.add(ListNotificationsEvent(
      page: _currentPage,
      limit: 10,
    ));
  }

  void _resetPagination() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _isLoadingMore = false;
      _allNotifications.clear();
    });
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double responsiveTextSize = getResponsiveSize(context);
    final Size size = MediaQuery.of(context).size;

    return BlocProvider.value(
      value: _supportBloc,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: size.width * 0.032),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: BlocConsumer<SupportBloc, SupportState>(
            listener: (context, state) {
              if (state is ListNotificationsSuccess) {
                setState(() {
                  _isLoadingMore = false;

                  if (_currentPage == 1) {
                    _allNotifications =
                        List.from(state.data?.notifications ?? []);
                  } else {
                    final newNotifications = state.data?.notifications ?? [];
                    _allNotifications.addAll(newNotifications);
                  }

                  if (state.data != null) {
                    final total = state.data?.total ?? 0;
                    final page = state.data?.page ?? 1;
                    final limit = state.data?.limit ?? 10;
                    _hasMoreData = (page * limit) < total;
                  } else {
                    _hasMoreData = false;
                  }
                });
              } else if (state is MarkNotificationAsReadSuccess) {
                setState(() {
                  _loadingStates.remove(state.notificationId);

                  final index = _allNotifications.indexWhere((notification) =>
                      notification.id == state.notificationId);
                  if (index != -1) {
                    final updatedNotification =
                        _allNotifications[index].copyWith(status: 'READ');
                    _allNotifications[index] = updatedNotification;
                  }
                });
              } else if (state is MarkNotificationAsReadLoading) {
                setState(() {
                  _loadingStates[state.notificationId] = true;
                });
              } else if (state is MarkNotificationAsReadFailed) {
                setState(() {
                  _loadingStates.remove(state.notificationId);
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Failed to mark notification as read: ${state.message}'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              } else if (state is ListNotificationsFailed) {
                setState(() {
                  _isLoadingMore = false;
                  if (_currentPage > 1) _currentPage--;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('Failed to load notifications: ${state.message}'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
            builder: (context, state) {
              if (state is ListNotificationsLoading &&
                  _allNotifications.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFFFFBE16)),
                  ),
                );
              }

              if (_allNotifications.isEmpty) {
                if (state is ListNotificationsFailed) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 48, color: Color(0xFF66696D)),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load notifications',
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2122)),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: _resetPagination,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                return const Center(
                  child: Text('No notifications found'),
                );
              }

              return ListView.builder(
                controller: _scrollController,
                // Only add extra item if we're loading more
                itemCount: _allNotifications.length + (_isLoadingMore ? 1 : 0),
                padding: EdgeInsets.all(size.width * 0.026),
                physics: const AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  if (index == _allNotifications.length) {
                    // Only show loading indicator when actually loading more
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFFFFBE16),
                          ),
                        ),
                      ),
                    );
                  }

                  final notification = _allNotifications[index];
                  return GestureDetector(
                    onTap: () {
                      if (notification.status?.toUpperCase() == 'UNREAD') {
                        setState(() {
                          _loadingStates[notification.id] = true;
                        });
                        _supportBloc
                            .add(MarkNotificationAsReadEvent(notification.id));
                      }

                      setState(() {
                        _selectedIndex = _selectedIndex == index ? null : index;
                      });

                      final int? notificationType = notification.type;

                      if (notificationType == 2) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ConfirmedOrdersView(
                              orderId: notification.targetId ?? 0,
                            ),
                          ),
                        );
                      } else if (notificationType == 13) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CateringRequestsPage(
                              initialTab: 1,
                            ),
                          ),
                        );
                      } else if (notificationType == 14) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CateringRequestsPage(
                              initialTab: 2,
                            ),
                          ),
                        );
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getNotificationColor(
                            notification.status, _selectedIndex == index),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Stack(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: size.height * 0.014,
                                  horizontal: size.width * 0.026,
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(
                                        top: size.height * 0.0025,
                                      ),
                                      child: Image.asset(
                                        'assets/icons/notifications.png',
                                        width: 24,
                                        height: 24,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return const Icon(
                                            Icons.notifications,
                                            size: 24,
                                          );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: size.width * 0.040),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                              height: size.height * 0.0025),
                                          Text(
                                            notification.title ?? 'No Title',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w600,
                                              fontSize: 1 * responsiveTextSize,
                                            ),
                                          ),
                                          SizedBox(height: size.height * 0.003),
                                          Text(
                                            notification.description ??
                                                'No Message',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              fontSize:
                                                  0.86 * responsiveTextSize,
                                              color: const Color(0xFF1F2122),
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: size.height * 0.002),
                                          Text(
                                            _formatTimestamp(
                                                notification.createdAt),
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              fontSize:
                                                  0.86 * responsiveTextSize,
                                              color: const Color(0xFF66696D),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (index < _allNotifications.length - 1)
                                const Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 4.0),
                                  child: Divider(
                                    height: 1,
                                    thickness: 1,
                                    color: Color(0xFFE1E3E6),
                                  ),
                                ),
                            ],
                          ),
                          if (_loadingStates[notification.id] == true)
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.7),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: CupertinoActivityIndicator(),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  String _formatTimestamp(String? timestamp) {
    if (timestamp == null) return 'Unknown';
    try {
      final dateTime = DateTime.parse(timestamp);
      final hour = dateTime.hour > 12
          ? dateTime.hour - 12
          : dateTime.hour == 0
              ? 12
              : dateTime.hour;
      final period = dateTime.hour >= 12 ? 'PM' : 'AM';
      return '${hour}:${dateTime.minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return timestamp;
    }
  }

  Color _getNotificationColor(String? status, bool isSelected) {
    if (isSelected) {
      return const Color(0xFFF1F2F3);
    }
    if (status?.toUpperCase() == 'UNREAD') {
      return const Color(0xFFF6F6F6);
    }
    return Colors.white;
  }
}
