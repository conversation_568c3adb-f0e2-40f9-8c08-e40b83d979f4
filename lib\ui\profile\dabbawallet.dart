import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DabbaWalletPage extends StatefulWidget {
  const DabbaWalletPage({super.key});

  @override
  State<DabbaWalletPage> createState() => _DabbaWalletPageState();
}

class _DabbaWalletPageState extends State<DabbaWalletPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  @override
  void initState() {
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _loadInitialData();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
    super.initState();
  }

       late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _scrollListener() {
    if (!_isLoadingMore &&
        _hasMoreData &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadInitialData() {
    context.read<AccountBloc>().add(WalletTransactionListEvent('', page: 1));
  }

  void _loadMore() {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    context.read<AccountBloc>().add(
          WalletTransactionListEvent(
            '',
            page: _currentPage + 1,
          ),
        );
  }

  void _resetPagination() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _isLoadingMore = false;
    });
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        scrolledUnderElevation: 0,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is WalletTransactionLoadingSuccess) {
            setState(() {
              _isLoadingMore = false;
              _currentPage = Initializer.walletTransactionListModel.data
                      ?.pagination?.currentPage ??
                  _currentPage;

              // Update hasMoreData based on pagination data
              final pagination =
                  Initializer.walletTransactionListModel.data?.pagination;
              if (pagination != null) {
                _hasMoreData = pagination.currentPage! < pagination.totalPages!;
              } else {
                _hasMoreData = false;
              }
            });
          } else if (state is WalletTransactionLoadingAFailed) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        },
        builder: (context, state) {
          if (state is WalletTransactionListLoading) {
            // return buildShimmer();
          } else if (state is WalletTransactionLoadingAFailed) {
            return Center(
              child: Text(
                'Error loading transactions',
                style: TextStyle(color: Colors.red),
              ),
            );
          } else if (state is WalletTransactionLoadingSuccess) {}
          return Initializer.walletTransactionListModel == null
              ? buildShimmer()
              : Initializer.walletTransactionListModel.data == null
                  ? buildShimmer()
                  : SingleChildScrollView(
                      controller: _scrollController,
                      child: Padding(
                        padding:  EdgeInsets.only(
                            top: 0, bottom: sixteen, left: eighteen, right: eighteen),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Payments heading
                             Text(
                              'Payments',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: eighteen,
                                height: 1.24,
                                letterSpacing: -1,
                                color: Colors.black,
                              ),
                            ),

                             SizedBox(height: eighteen),

                            // White container
                            Container(
                              width: double.infinity,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Padding(
                                padding:  EdgeInsets.only(
                                    top: sixteen, bottom: sixteen, right: sixteen, left: sixteen),
                                child: Column(
                                  children: [
                                    // Image with overlay text
                                    Stack(
                                      children: [
                                        Container(
                                          height: ten*20.6,
                                          width: double.infinity,
                                          decoration: const BoxDecoration(
                                            image: DecorationImage(
                                              image:
                                                  AssetImage('assets/dbbg.png'),
                                              fit: BoxFit.fill,
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          top: sixteen,
                                          left: sixteen,
                                          child:  Text(
                                            'Dabba Wallet',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w600,
                                              fontSize: sixteen,
                                              height: 1.24,
                                              letterSpacing: 0,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          bottom: sixteen,
                                          left: sixteen,
                                          child: Text(
                                            '\$${Initializer.walletTransactionListModel.data!.balance?.toStringAsFixed(2) ?? '0.00'}',
                                            style: TextStyle(
                                              fontFamily: 'Suisse Intl',
                                              fontWeight: FontWeight.w600,
                                              fontSize:twentyFour,
                                              height: 1.20,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                     SizedBox(height: twelve),

                                    // Cash in button
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0.0),
                                      child: SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton(
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                double? selectedAmount;
                                                final amounts = [
                                                  100.0,
                                                  200.0,
                                                  500.0
                                                ];
                                                final TextEditingController
                                                    controller =
                                                    TextEditingController();
                                                return StatefulBuilder(
                                                  builder: (context, setState) {
                                                    return AlertDialog(
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(twenty),
                                                      ),
                                                      title: Row(
                                                        children: [
                                                          const Icon(
                                                              Icons
                                                                  .account_balance_wallet_rounded,
                                                              color: Colors
                                                                  .black87),
                                                           SizedBox(
                                                              width: sixteen/2),
                                                           Text(
                                                            'Add Money',
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: twenty,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      content: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Container(
                                                            decoration:
                                                                BoxDecoration(
                                                              color: const Color(
                                                                  0xFFF6F3EC),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          twelve),
                                                            ),
                                                            child: TextField(
                                                              controller:
                                                                  controller,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              style:
                                                                   TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize: sixteen,
                                                              ),
                                                              decoration:
                                                                   InputDecoration(
                                                                prefixIcon:
                                                                    Icon(Icons
                                                                        .attach_money),
                                                                border:
                                                                    InputBorder
                                                                        .none,
                                                                contentPadding:
                                                                    EdgeInsets.symmetric(
                                                                        horizontal:
                                                                            sixteen,
                                                                        vertical:
                                                                            forteen),
                                                              ),
                                                              onChanged:
                                                                  (value) {
                                                                final val = double
                                                                    .tryParse(
                                                                        value);
                                                                setState(() {
                                                                  selectedAmount =
                                                                      val;
                                                                });
                                                              },
                                                            ),
                                                          ),
                                                           SizedBox(
                                                              height: eighteen),
                                                          Align(
                                                            alignment: Alignment
                                                                .centerLeft,
                                                            child: Text(
                                                              'Quick Amounts',
                                                              style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                color: Colors
                                                                    .grey[700],
                                                                fontSize: forteen,
                                                              ),
                                                            ),
                                                          ),
                                                           SizedBox(
                                                              height: sixteen/2),
                                                          Wrap(
                                                            spacing: twelve,
                                                            children: amounts
                                                                .map((amount) {
                                                              final isSelected =
                                                                  selectedAmount ==
                                                                      amount;
                                                              return ChoiceChip(
                                                                label: Text(
                                                                    '\$${amount.toInt()}'),
                                                                selected:
                                                                    isSelected,
                                                                selectedColor:
                                                                    Colors
                                                                        .black,
                                                                labelStyle:
                                                                    TextStyle(
                                                                  color: isSelected
                                                                      ? Colors
                                                                          .white
                                                                      : Colors
                                                                          .black,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                                backgroundColor:
                                                                    const Color(
                                                                        0xFFF6F3EC),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              ten),
                                                                  side:
                                                                      BorderSide(
                                                                    color: isSelected
                                                                        ? Colors
                                                                            .black
                                                                        : Colors
                                                                            .grey
                                                                            .shade300,
                                                                    width: 1,
                                                                  ),
                                                                ),
                                                                checkmarkColor:
                                                                    Colors
                                                                        .white,
                                                                onSelected:
                                                                    (_) {
                                                                  setState(() {
                                                                    selectedAmount =
                                                                        amount;
                                                                    controller
                                                                            .text =
                                                                        amount.toStringAsFixed(
                                                                            2);
                                                                  });
                                                                },
                                                              );
                                                            }).toList(),
                                                          ),
                                                        ],
                                                      ),
                                                      actions: [
                                                        TextButton(
                                                          onPressed: () =>
                                                              Navigator.pop(
                                                                  context),
                                                          style: TextButton
                                                              .styleFrom(
                                                            foregroundColor:
                                                                Colors.black,
                                                          ),
                                                          child: const Text(
                                                              'Cancel'),
                                                        ),
                                                        BlocListener<
                                                            AccountBloc,
                                                            AccountState>(
                                                          listener:
                                                              (context, state) {
                                                            if (state
                                                                is WalletDepositSuccess) {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                              ScaffoldMessenger
                                                                      .of(context)
                                                                  .showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                      'Deposit successful!'),
                                                                  duration:
                                                                      Duration(
                                                                          seconds:
                                                                              2),
                                                                ),
                                                              );
                                                            }
                                                          },
                                                          child: BlocBuilder<
                                                              AccountBloc,
                                                              AccountState>(
                                                            builder: (context,
                                                                state) {
                                                              final isLoading =
                                                                  state
                                                                      is WalletDepositLoading;
                                                              return ElevatedButton
                                                                  .icon(
                                                                icon:  Icon(
                                                                    Icons
                                                                        .arrow_upward,
                                                                    size: eighteen),
                                                                label: isLoading
                                                                    ?  SizedBox(
                                                                        width:
                                                                            eighteen,
                                                                        height:
                                                                            eighteen,
                                                                        child:
                                                                            Padding(
                                                                          padding:
                                                                              EdgeInsets.all(2.0),
                                                                          child:
                                                                              CircularProgressIndicator(
                                                                            strokeWidth:
                                                                                2,
                                                                            color:
                                                                                Colors.white,
                                                                          ),
                                                                        ),
                                                                      )
                                                                    : const Text(
                                                                        'Deposit'),
                                                                style: ElevatedButton
                                                                    .styleFrom(
                                                                  backgroundColor:
                                                                      Colors
                                                                          .black,
                                                                  foregroundColor:
                                                                      Colors
                                                                          .white,
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            twelve),
                                                                  ),
                                                                  padding:  EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          eighteen,
                                                                      vertical:
                                                                          twelve),
                                                                ),
                                                                onPressed: (selectedAmount !=
                                                                            null &&
                                                                        selectedAmount! >
                                                                            0 &&
                                                                        !isLoading)
                                                                    ? () {
                                                                        context
                                                                            .read<AccountBloc>()
                                                                            .add(WalletDepositEvent(selectedAmount!));
                                                                        // Don't pop here, wait for success
                                                                      }
                                                                    : null,
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.black,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(twenty*2),
                                            ),
                                            padding:  EdgeInsets.symmetric(
                                                vertical: forteen),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                               Icon(
                                                Icons.add,
                                                color: Colors.white,
                                                size: sixteen,
                                              ),
                                               SizedBox(width: sixteen/2),
                                               Text(
                                                'Cash in',
                                                style: TextStyle(
                                                  fontFamily: 'Suisse Int\'l',
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: twelve,
                                                  height: 1.0,
                                                  letterSpacing: 0.24,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),

                                     SizedBox(height: forteen),

                                    // Withdraw button
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0.0),
                                      child: SizedBox(
                                        width: double.infinity,
                                        child: OutlinedButton(
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                double? selectedAmount;
                                                final amounts = [
                                                  100.0,
                                                  200.0,
                                                  500.0
                                                ];
                                                final TextEditingController
                                                    controller =
                                                    TextEditingController();
                                                return StatefulBuilder(
                                                  builder: (context, setState) {
                                                    return AlertDialog(
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(twenty),
                                                      ),
                                                      title: Row(
                                                        children: [
                                                          const Icon(
                                                              Icons
                                                                  .account_balance_wallet_rounded,
                                                              color: Colors
                                                                  .black87),
                                                           SizedBox(
                                                              width: sixteen/2),
                                                           Text(
                                                            'Withdraw Money',
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: twenty,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      content: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Container(
                                                            decoration:
                                                                BoxDecoration(
                                                              color: const Color(
                                                                  0xFFF6F3EC),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          twelve),
                                                            ),
                                                            child: TextField(
                                                              controller:
                                                                  controller,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              style:
                                                                   TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize: sixteen,
                                                              ),
                                                              decoration:
                                                                   InputDecoration(
                                                                prefixIcon:
                                                                    Icon(Icons
                                                                        .attach_money),
                                                                border:
                                                                    InputBorder
                                                                        .none,
                                                                contentPadding:
                                                                    EdgeInsets.symmetric(
                                                                        horizontal:
                                                                            sixteen,
                                                                        vertical:
                                                                            forteen),
                                                              ),
                                                              onChanged:
                                                                  (value) {
                                                                final val = double
                                                                    .tryParse(
                                                                        value);
                                                                setState(() {
                                                                  selectedAmount =
                                                                      val;
                                                                });
                                                              },
                                                            ),
                                                          ),
                                                           SizedBox(
                                                              height: eighteen),
                                                          Align(
                                                            alignment: Alignment
                                                                .centerLeft,
                                                            child: Text(
                                                              'Quick Amounts',
                                                              style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                color: Colors
                                                                    .grey[700],
                                                                fontSize: forteen,
                                                              ),
                                                            ),
                                                          ),
                                                         SizedBox(
                                                              height: sixteen/2),
                                                          Wrap(
                                                            spacing: twelve,
                                                            children: amounts
                                                                .map((amount) {
                                                              final isSelected =
                                                                  selectedAmount ==
                                                                      amount;
                                                              return ChoiceChip(
                                                                label: Text(
                                                                    '\$${amount.toInt()}'),
                                                                selected:
                                                                    isSelected,
                                                                selectedColor:
                                                                    Colors
                                                                        .black,
                                                                labelStyle:
                                                                    TextStyle(
                                                                  color: isSelected
                                                                      ? Colors
                                                                          .white
                                                                      : Colors
                                                                          .black,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                                backgroundColor:
                                                                    const Color(
                                                                        0xFFF6F3EC),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              ten),
                                                                  side:
                                                                      BorderSide(
                                                                    color: isSelected
                                                                        ? Colors
                                                                            .black
                                                                        : Colors
                                                                            .grey
                                                                            .shade300,
                                                                    width: 1,
                                                                  ),
                                                                ),
                                                                checkmarkColor:
                                                                    Colors
                                                                        .white,
                                                                onSelected:
                                                                    (_) {
                                                                  setState(() {
                                                                    selectedAmount =
                                                                        amount;
                                                                    controller
                                                                            .text =
                                                                        amount.toStringAsFixed(
                                                                            2);
                                                                  });
                                                                },
                                                              );
                                                            }).toList(),
                                                          ),
                                                        ],
                                                      ),
                                                      actions: [
                                                        TextButton(
                                                          onPressed: () =>
                                                              Navigator.pop(
                                                                  context),
                                                          style: TextButton
                                                              .styleFrom(
                                                            foregroundColor:
                                                                Colors.black,
                                                          ),
                                                          child: const Text(
                                                              'Cancel'),
                                                        ),
                                                        BlocListener<
                                                            AccountBloc,
                                                            AccountState>(
                                                          listener:
                                                              (context, state) {
                                                            if (state
                                                                is WalletWithdrawSuccess) {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                              ScaffoldMessenger
                                                                      .of(context)
                                                                  .showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                      'Withdrawn successfully!'),
                                                                  duration:
                                                                      Duration(
                                                                          seconds:
                                                                              2),
                                                                ),
                                                              );
                                                            }
                                                          },
                                                          child: BlocBuilder<
                                                              AccountBloc,
                                                              AccountState>(
                                                            builder: (context,
                                                                state) {
                                                              final isLoading =
                                                                  state
                                                                      is WalletWithdrawLoading;
                                                              return ElevatedButton
                                                                  .icon(
                                                                icon:  Icon(
                                                                    Icons
                                                                        .arrow_downward,
                                                                    size: eighteen),
                                                                label: isLoading
                                                                    ?  SizedBox(
                                                                        width:
                                                                            eighteen,
                                                                        height:
                                                                            eighteen,
                                                                        child:
                                                                            Padding(
                                                                          padding:
                                                                              EdgeInsets.all(2.0),
                                                                          child:
                                                                              CircularProgressIndicator(
                                                                            strokeWidth:
                                                                                2,
                                                                            color:
                                                                                Colors.white,
                                                                          ),
                                                                        ),
                                                                      )
                                                                    : const Text(
                                                                        'Withdraw'),
                                                                style: ElevatedButton
                                                                    .styleFrom(
                                                                  backgroundColor:
                                                                      Colors
                                                                          .black,
                                                                  foregroundColor:
                                                                      Colors
                                                                          .white,
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            twelve),
                                                                  ),
                                                                  padding:  EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          eighteen,
                                                                      vertical:
                                                                          twelve),
                                                                ),
                                                                onPressed: (selectedAmount !=
                                                                            null &&
                                                                        selectedAmount! >
                                                                            0 &&
                                                                        !isLoading)
                                                                    ? () {
                                                                        if (selectedAmount! <=
                                                                            (Initializer.walletTransactionListModel.data!.balance ??
                                                                                0)) {
                                                                          context
                                                                              .read<AccountBloc>()
                                                                              .add(WalletWithdrawEvent(selectedAmount!));
                                                                        } else {
                                                                          ScaffoldMessenger.of(context)
                                                                              .showSnackBar(
                                                                            const SnackBar(
                                                                              content: Text('Insufficient balance for withdrawal'),
                                                                              duration: Duration(seconds: 2),
                                                                            ),
                                                                          );
                                                                        }
                                                                        // Don't pop here, wait for success
                                                                      }
                                                                    : null,
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          },
                                          style: OutlinedButton.styleFrom(
                                            backgroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(twenty*2),
                                            ),
                                            side: const BorderSide(
                                                color: Colors.black, width: 1),
                                            padding:  EdgeInsets.symmetric(
                                                vertical: forteen),
                                          ),
                                          child:  Text(
                                            'Withdraw',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: twelve,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),

                                     SizedBox(height:twentyFour),

                                    // Transactions heading
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0.0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child:  Text(
                                          'Transactions',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w600,
                                            fontSize: sixteen,
                                            height: 1.24,
                                            letterSpacing: 0,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),

                                     SizedBox(height: ten),
                                    if (Initializer.walletTransactionListModel
                                                .data?.transactions !=
                                            null &&
                                        Initializer.walletTransactionListModel
                                            .data!.transactions!.isNotEmpty)
                                      ListView.separated(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: Initializer
                                            .walletTransactionListModel
                                            .data!
                                            .transactions!
                                            .length,
                                        separatorBuilder: (context, index) =>
                                            const Divider(
                                          color: Color(0xFFE1E3E6),
                                          thickness: 1,
                                          height: 1,
                                        ),
                                        itemBuilder: (context, index) {
                                          final tx = Initializer
                                              .walletTransactionListModel
                                              .data!
                                              .transactions![index];
                                          // You may need to adjust these fields based on your model
                                          final mainText =
                                              tx.title ?? 'Transaction';
                                          final timeText = tx.createdAt ?? '';
                                          final type = tx.transactionType ?? '';
                                          final price = tx.transactionType !=
                                                  'DEBIT'
                                              ? '+\$${(tx.amount ?? 0).toStringAsFixed(2)}'
                                              : '-\$${(tx.amount ?? 0).abs().toStringAsFixed(2)}';

                                          String formattedTime =
                                              _formatTransactionTime(timeText);
                                          return _buildTransactionRow(mainText,
                                              formattedTime, price, type);
                                        },
                                      )
                                    else
                                       Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical:twentyFour),
                                        child: Center(
                                          child: Text(
                                            'No transactions found.',
                                            style:
                                                TextStyle(color: Colors.grey),
                                          ),
                                        ),
                                      ),

                                    // Add loading indicator at the bottom when loading more
                                    if (_isLoadingMore)
                                       Padding(
                                        padding: EdgeInsets.all(sixteen),
                                        child: Center(
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Color.fromARGB(
                                                        255, 26, 26, 26)),
                                          ),
                                        ),
                                      ),

                                    // Show "No more transactions" text when no more data
                                    if (!_hasMoreData &&
                                        !_isLoadingMore &&
                                        Initializer
                                                .walletTransactionListModel
                                                .data
                                                ?.transactions
                                                ?.isNotEmpty ==
                                            true)
                                       Padding(
                                        padding: EdgeInsets.all(sixteen),
                                        child: Center(
                                          child: Text(
                                            'No more transactions to load',
                                            style: TextStyle(
                                                color: Color(0xFF66696D),
                                                fontSize: forteen),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
        },
      ),
    );
  }

  Widget buildShimmer() {
    return SingleChildScrollView(
      child: Padding(
        padding:  EdgeInsets.only(top: 0, bottom: sixteen, left: eighteen, right: eighteen),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payments heading shimmer
            _buildShimmerBox(
              width: 80,
              height: 22,
              borderRadius: 4,
            ),

             SizedBox(height: eighteen),

            // White container with shimmer content
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Padding(
                padding:  EdgeInsets.only(
                    top: sixteen, bottom: sixteen, right: sixteen, left: sixteen),
                child: Column(
                  children: [
                    // Wallet card shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: 206,
                      borderRadius: sixteen/2,
                    ),

                     SizedBox(height: twelve),

                    // Cash in button shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: ten*4.8,
                      borderRadius: twenty*2,
                    ),

                     SizedBox(height: forteen),

                    // Withdraw button shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: ten*4.8,
                      borderRadius: twenty*2,
                    ),

                     SizedBox(height:twentyFour),

                    // Transactions heading shimmer
                    Align(
                      alignment: Alignment.centerLeft,
                      child: _buildShimmerBox(
                        width: ten*1.2,
                        height: twenty,
                        borderRadius: 4,
                      ),
                    ),

                     SizedBox(height: ten),

                    // Transaction rows shimmer
                    ...List.generate(
                        4,
                        (index) => Column(
                              children: [
                                _buildTransactionShimmerRow(),
                                if (index < 3)
                                  const Divider(
                                    color: Color(0xFFE1E3E6),
                                    thickness: 1,
                                    height: 1,
                                  ),
                              ],
                            )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerBox({
    required double width,
    required double height,
    double borderRadius = 8,
  }) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
              colors: const [
                Color(0xFFE0E0E0),
                Color(0xFFF5F5F5),
                Color(0xFFE0E0E0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTransactionShimmerRow() {
    return Padding(
      padding:  EdgeInsets.symmetric(vertical: sixteen),
      child: Row(
        children: [
          // Transaction icon shimmer
          _buildShimmerBox(
            width:twentyFour,
            height:twentyFour,
            borderRadius: twelve,
          ),
           SizedBox(width: twelve),
          // Transaction details shimmer
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(
                  width: double.infinity,
                  height: forteen,
                  borderRadius: 4,
                ),
                const SizedBox(height: 4),
                _buildShimmerBox(
                  width: 150,
                  height: twelve,
                  borderRadius: 4,
                ),
              ],
            ),
          ),
           SizedBox(width: twelve),
          // Amount shimmer
          _buildShimmerBox(
            width: 60,
            height: sixteen,
            borderRadius: 4,
          ),
        ],
      ),
    );
  }

  String _formatTransactionTime(String timeText) {
    // Try to parse ISO8601 or fallback to original string
    try {
      final dateTime = DateTime.parse(timeText);
      // Format as "MMM d, yyyy, h:mm a"
      return "${_monthName(dateTime.month)} ${dateTime.day}, ${dateTime.year}, ${_formatHourMinute(dateTime)}";
    } catch (e) {
      return timeText;
    }
  }

  String _monthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return (month >= 1 && month <= 12) ? months[month - 1] : '';
  }

  String _formatHourMinute(DateTime dt) {
    int hour = dt.hour;
    final minute = dt.minute.toString().padLeft(2, '0');
    final ampm = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 == 0 ? 12 : hour % 12;
    return "$hour:$minute $ampm";
  }

  Widget _buildTransactionRow(
    String mainText, String timeText, String price, String type) {

      
  Color priceColor =
      price.startsWith('+') ? const Color(0xFF007A4D) : const Color(0xFFD31510);

  return Padding(
    padding:  EdgeInsets.symmetric(horizontal: 0.0, vertical: sixteen),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Cash icon
        Image.asset(
          'assets/cash.png',
          width: ten*3.2,
          height: ten*3.2,
        ),

        SizedBox(width: sixteen/2),

        // Main text and time
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                mainText.length > 25
                    ? '${mainText.substring(0, 20)}..'
                    : mainText,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: forteen,
                  height: 1.71,
                  letterSpacing: 0,
                  color: Color(0xFF414346),
                ),
              ),
              SizedBox(
                height: 4,
              ),
              Text(
                timeText,
                style:  TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: twelve,
                  height: 1.67,
                  letterSpacing: 0,
                  color: Color(0xFF414346),
                ),
              ),
            ],
          ),
        ),

        // Price
        Text(
          price,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
            fontSize: forteen,
            height: 1.43,
            letterSpacing: 0,
            color: priceColor,
          ),
        ),

         SizedBox(width: twelve),

        // Right arrow icon
        // Image.asset(
        //   'assets/right.png',
        //   width:twentyFour,
        //   height:twentyFour,
        // ),
      ],
    ),
  );
}

}

