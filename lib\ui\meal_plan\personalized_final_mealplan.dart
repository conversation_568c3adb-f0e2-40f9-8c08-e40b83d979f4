import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/data/models/meal_plan/mealplanprogressmodel.dart';
import 'package:db_eats/ui/meal_plan/checkout_page.dart';
import 'package:db_eats/ui/meal_plan/personalized_checkout.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/services.dart';

class PersonailizedMealplanFinal extends StatefulWidget {
  final int mealPlanId;

  const PersonailizedMealplanFinal({
    Key? key,
    required this.mealPlanId,
  }) : super(key: key);

  @override
  _PersonailizedMealplanFinalState createState() =>
      _PersonailizedMealplanFinalState();
}

class _PersonailizedMealplanFinalState
    extends State<PersonailizedMealplanFinal> {
  int currentday = 0;
  Map<String, Map<String, dynamic>> mealdata = {};
  List<String> dates = [];

  String _startDate = '';
  String _endDate = '';
  int _maxDishesPerDay = 0;
  String _timeSlot = '';

  @override
  void initState() {
    super.initState();
    context.read<MealplanBloc>().add(MealPlanProgressEvent(widget.mealPlanId));
  }

  String formatDate(String date) {
    DateTime parsedDate = DateTime.parse(date);
    String formattedDate = DateFormat('EEE, MMM dd, yyyy').format(parsedDate);
    return formattedDate;
  }

  String _formatTimeWithAMPM(String time) {
    final parts = time.split(':');
    int hours = int.parse(parts[0]);
    final minutes = parts[1];

    final period = hours >= 12 ? 'PM' : 'AM';

    hours = hours > 12 ? hours - 12 : hours;
    hours = hours == 0 ? 12 : hours;

    return '$hours:$minutes$period';
  }

  void processData(Data mealPlanData) {
    setState(() {
      dates = mealPlanData.personalizedDays
              ?.where((day) => day.date != null)
              ?.map((day) => day.date!)
              ?.toList() ??
          [];

      mealdata = {
        for (var day in (mealPlanData.personalizedDays ?? []))
          if (day.date != null)
            day.date!: {
              'selectedDishes': (day.items ?? [])
                  .map((item) => {
                        'name': item.menuItem?.name ?? '',
                        'price': double.tryParse(item.price ?? '0') ?? 0.0,
                        'photo': item.menuItem?.photo ?? '',
                        'servings': mealPlanData.servingSize?.serves ?? 1,
                      })
                  .toList(),
              'chefDetails': {
                'name':
                    '${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}',
                'photo': day.chef?.profilePhoto ?? '',
              },
              'dayTotal': day.dayTotal,
              'discount': day.discount,
            }
      };

      _startDate = mealPlanData.startDate ?? '';
      _endDate = mealPlanData.endDate ?? '';

      // Format the time slot with AM/PM
      final startTime =
          mealPlanData.timeSlot?.startTime?.substring(0, 5) ?? '12:00';
      final endTime =
          mealPlanData.timeSlot?.endTime?.substring(0, 5) ?? '15:00';
      _timeSlot =
          '${_formatTimeWithAMPM(startTime)}-${_formatTimeWithAMPM(endTime)}';
    });
  }

  List<Map<String, dynamic>> _getCurrentDayMeals(String currentDate) {
    return List<Map<String, dynamic>>.from(
        mealdata[currentDate]?['selectedDishes'] ?? []);
  }

  void _showCannotGoBackDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "Cannot Go Back",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  "You cannot go back from this page. Please continue to checkout.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF414346),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                    minimumSize: const Size.fromHeight(48),
                  ),
                  child: const Text(
                    "OK",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _showCannotGoBackDialog();
        return false;
      },
      child: BlocConsumer<MealplanBloc, MealPlanState>(
        listener: (context, state) {
          if (state is MealPlanProgressSuccess) {
            if (state.data != null) {
              processData(state.data);
            }
          }
        },
        builder: (context, state) {
          if (state is MealPlanProgressLoading) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          // Check if data is ready
          if (mealdata.isEmpty || dates.isEmpty) {
            return const Scaffold(
              body: Center(child: Text('No meal plan data available')),
            );
          }

          // Rest of the build method
          String currentDate = dates[currentday];
          List<Map<String, dynamic>> currentDayMeals =
              _getCurrentDayMeals(currentDate);
          double subtotal = currentDayMeals.fold(
              0.0, (sum, meal) => sum + (meal['price'] as num).toDouble());

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 0,
              centerTitle: false,
              leading: IconButton(
                icon: Image.asset('assets/icons/close.png',
                    width: 24, height: 24),
                onPressed: () => _showCannotGoBackDialog(),
                padding: EdgeInsets.zero,
              ),
              title: const Padding(
                padding: EdgeInsets.only(left: 8.0),
                child: Text(
                  "Meal Plan",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122),
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(1),
                child: Container(
                  color: const Color(0xFFE1E3E6),
                  height: 1,
                ),
              ),
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        formatDate(dates[currentday]),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      Row(
                        children: [
                          IconButton(
                            icon: currentday == 0
                                ? Image.asset('assets/icons/arrow_left.png',
                                    width: 32, height: 32)
                                : Transform.rotate(
                                    angle: 3.1416,
                                    child: Image.asset(
                                        'assets/icons/arrow_right.png',
                                        width: 32,
                                        height: 32),
                                  ),
                            onPressed: () {
                              setState(() {
                                if (currentday > 0) currentday--;
                              });
                            },
                            constraints: const BoxConstraints(),
                            padding: EdgeInsets.zero,
                          ),
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.access_time, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  _timeSlot,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF414346),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: currentday == dates.length - 1
                                ? Transform.rotate(
                                    angle: 3.1416,
                                    child: Image.asset(
                                        'assets/icons/arrow_left.png',
                                        width: 32,
                                        height: 32),
                                  )
                                : Image.asset('assets/icons/arrow_right.png',
                                    width: 32, height: 32),
                            onPressed: () {
                              setState(() {
                                if (currentday < dates.length - 1) currentday++;
                              });
                            },
                            constraints: const BoxConstraints(),
                            padding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildChefAvatar(
                              mealdata[currentDate]?['chefDetails']['photo']),
                          const SizedBox(width: 8),
                          Text(
                            mealdata[currentDate]?['chefDetails']['name'] ?? '',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Divider(
                    height: 1, thickness: 1, color: const Color(0xFFE1E3E6)),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Meals (${mealdata[dates[currentday]]?['selectedDishes']?.length ?? 0})",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      const SizedBox(height: 12),
                      ...List<Map<String, dynamic>>.from(
                              mealdata[dates[currentday]]?['selectedDishes'] ??
                                  [])
                          .map((meal) => _buildMealItem(meal)),
                    ],
                  ),
                ),
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      ...currentDayMeals.map((meal) => _buildPriceRow(
                            meal['name'],
                            (meal['price'] as num).toDouble(),
                          )),
                      if (currentDayMeals.isNotEmpty)
                        const SizedBox(height: 16),
                      _buildPriceRow("Subtotal", subtotal, isTotal: true),
                    ],
                  ),
                ),
              ],
            ),
            bottomNavigationBar: Container(
              padding: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CheckoutPage(
                        mealPlanId: widget.mealPlanId,
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1F2122),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  padding: const EdgeInsets.symmetric(
                      vertical: 16), // Increased height
                  minimumSize:
                      const Size.fromHeight(56), // Ensures minimum height
                ),
                child: const Text(
                  "Continue to Checkout",
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Prevent the default back navigation
    SystemChannels.platform.setMethodCallHandler((call) async {
      if (call.method == "SystemNavigator.pop") {
        _showCannotGoBackDialog();
        return Future.value(false);
      }
      return null;
    });
  }

  Widget _buildChefAvatar(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return const CircleAvatar(
        backgroundColor: Color(0xFFE0E0E0),
        radius: 10,
        child: Icon(Icons.person, size: 10, color: Color(0xFF9E9E9E)),
      );
    }

    return CircleAvatar(
      backgroundImage: NetworkImage(ServerHelper.imageUrl + imagePath),
      backgroundColor: Colors.grey[200],
      radius: 10,
      onBackgroundImageError: (_, __) {
        debugPrint(
            "Error loading chef image: ${ServerHelper.imageUrl + imagePath}");
      },
    );
  }

  Widget _buildMealItem(Map<String, dynamic> meal) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE1E3E6)),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              ServerHelper.imageUrl + (meal['photo'] ?? ''),
              width: 48,
              height: 48,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 48,
                height: 48,
                color: Colors.grey[200],
                child:
                    const Icon(Icons.restaurant_menu, color: Color(0xFF9E9E9E)),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meal['name'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122),
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      "\$${meal['price'].toStringAsFixed(2)}",
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF414346),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      "• ${meal['servings']} Servings",
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF414346),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String title, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
          Text(
            "\$${amount.toStringAsFixed(2)}",
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }
}
