class CateringDishDetailsModel {
  bool? status;
  String? message;
  int? statusCode;
  CateringDishDetailsData? data;

  CateringDishDetailsModel(
      {this.status, this.message, this.statusCode, this.data});

  CateringDishDetailsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? CateringDishDetailsData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CateringDishDetailsData {
  CateringDish? dish;

  CateringDishDetailsData({this.dish});

  CateringDishDetailsData.fromJson(Map<String, dynamic> json) {
    dish = json['dish'] != null ? CateringDish.fromJson(json['dish']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (dish != null) {
      data['dish'] = dish!.toJson();
    }
    return data;
  }
}

class CateringDish {
  int? id;
  int? orderType;
  String? title;
  int? dishtypeId;
  int? servingSize;
  String? price;
  int? chefCategoryId;
  int? cuisineId;
  int? subCuisineId;
  int? localCuisineId;
  String? photo;
  String? description;
  String? ingredients;
  int? dietaryId;
  int? spiceLevelId;
  int? packagingTypeId;
  String? status;
  Dishtype? dishtype;
  Dishtype? chefCategory;
  Dishtype? cuisine;
  Dishtype? subCuisine;
  Dishtype? localCuisine;
  Dishtype? spiceLevel;
  Dishtype? dietary;
  Dishtype? packagingType;
  List<CateringDishItem>? cateringDishItems;

  CateringDish({
    this.id,
    this.orderType,
    this.title,
    this.dishtypeId,
    this.servingSize,
    this.price,
    this.chefCategoryId,
    this.cuisineId,
    this.subCuisineId,
    this.localCuisineId,
    this.photo,
    this.description,
    this.ingredients,
    this.dietaryId,
    this.spiceLevelId,
    this.packagingTypeId,
    this.status,
    this.dishtype,
    this.chefCategory,
    this.cuisine,
    this.subCuisine,
    this.localCuisine,
    this.spiceLevel,
    this.dietary,
    this.packagingType,
    this.cateringDishItems,
  });

  CateringDish.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderType = json['order_type'];
    title = json['title'];
    dishtypeId = json['dishtype_id'];
    servingSize = json['serving_size'];
    price = json['price'];
    chefCategoryId = json['chef_category_id'];
    cuisineId = json['cuisine_id'];
    subCuisineId = json['sub_cuisine_id'];
    localCuisineId = json['local_cuisine_id'];
    photo = json['photo'];
    description = json['description'];
    ingredients = json['ingredients'];
    dietaryId = json['dietary_id'];
    spiceLevelId = json['spice_level_id'];
    packagingTypeId = json['packaging_type_id'];
    status = json['status'];

    dishtype =
        json['dishtype'] != null ? Dishtype.fromJson(json['dishtype']) : null;
    chefCategory = json['chef_category'] != null
        ? Dishtype.fromJson(json['chef_category'])
        : null;
    cuisine =
        json['cuisine'] != null ? Dishtype.fromJson(json['cuisine']) : null;
    subCuisine = json['sub_cuisine'] != null
        ? Dishtype.fromJson(json['sub_cuisine'])
        : null;
    localCuisine = json['local_cuisine'] != null
        ? Dishtype.fromJson(json['local_cuisine'])
        : null;
    spiceLevel = json['spice_level'] != null
        ? Dishtype.fromJson(json['spice_level'])
        : null;
    dietary =
        json['dietary'] != null ? Dishtype.fromJson(json['dietary']) : null;
    packagingType = json['packaging_type'] != null
        ? Dishtype.fromJson(json['packaging_type'])
        : null;

    if (json['catering_dish_items'] != null) {
      cateringDishItems = <CateringDishItem>[];
      json['catering_dish_items'].forEach((v) {
        cateringDishItems!.add(CateringDishItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_type'] = orderType;
    data['title'] = title;
    data['dishtype_id'] = dishtypeId;
    data['serving_size'] = servingSize;
    data['price'] = price;
    data['chef_category_id'] = chefCategoryId;
    data['cuisine_id'] = cuisineId;
    data['sub_cuisine_id'] = subCuisineId;
    data['local_cuisine_id'] = localCuisineId;
    data['photo'] = photo;
    data['description'] = description;
    data['ingredients'] = ingredients;
    data['dietary_id'] = dietaryId;
    data['spice_level_id'] = spiceLevelId;
    data['packaging_type_id'] = packagingTypeId;
    data['status'] = status;

    if (dishtype != null) {
      data['dishtype'] = dishtype!.toJson();
    }
    if (chefCategory != null) {
      data['chef_category'] = chefCategory!.toJson();
    }
    if (cuisine != null) {
      data['cuisine'] = cuisine!.toJson();
    }
    if (subCuisine != null) {
      data['sub_cuisine'] = subCuisine!.toJson();
    }
    if (localCuisine != null) {
      data['local_cuisine'] = localCuisine!.toJson();
    }
    if (spiceLevel != null) {
      data['spice_level'] = spiceLevel!.toJson();
    }
    if (dietary != null) {
      data['dietary'] = dietary!.toJson();
    }
    if (packagingType != null) {
      data['packaging_type'] = packagingType!.toJson();
    }
    if (cateringDishItems != null) {
      data['catering_dish_items'] =
          cateringDishItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Dishtype {
  int? id;
  String? name;

  Dishtype({this.id, this.name});

  Dishtype.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

// Added the missing CateringDishItem class
class CateringDishItem {
  int? id;
  String? name;
  String? description;
  int? quantity;
  String? price;

  CateringDishItem({
    this.id,
    this.name,
    this.description,
    this.quantity,
    this.price,
  });

  CateringDishItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    quantity = json['quantity'];
    price = json['price'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['quantity'] = quantity;
    data['price'] = price;
    return data;
  }
}
