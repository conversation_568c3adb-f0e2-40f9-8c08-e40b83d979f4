import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/orders/ongoing_mealplandetail.dart';
import 'package:db_eats/ui/rating/chef_review_page.dart';
import 'package:db_eats/ui/rating/driver_rating_page.dart';
import 'package:db_eats/ui/support/customer_support.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';

class Ongoinglist extends StatefulWidget {
  const Ongoinglist({super.key});

  @override
  State<Ongoinglist> createState() => _OngoinglistState();
}

class _OngoinglistState extends State<Ongoinglist> {
  // Tab selection
  int _selectedTabIndex = 0; // Set to Past Orders by default for testing
  final List<String> _tabs = ['Ongoing', 'Meal Plans', 'Past Orders'];

  late PageController _pageController;

  // Update the selectedOrder type to include id
  Map<String, dynamic>? _selectedOrder;
  int? _selectedOrderId;

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(
        BookingListEvent(_tabs[_selectedTabIndex].toLowerCase() == 'ongoing'
            ? 'ongoing'
            : _tabs[_selectedTabIndex].toLowerCase() == 'past orders'
                ? 'past'
                : 'past'));
    _pageController = PageController(initialPage: _selectedTabIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  // Add responsive helper methods
  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final isLandscape = width > height;

    if (width < 360) {
      return EdgeInsets.all(width * 0.03);
    } else if (width < 600) {
      return EdgeInsets.all(width * 0.04);
    } else {
      return EdgeInsets.all(isLandscape ? width * 0.03 : width * 0.05);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final maxContentWidth = size.width > 900
        ? 800.0
        : size.width > 600
            ? 600.0
            : size.width;
    final baseTextSize = getResponsiveSize(context,
        small: 12, medium: 14, large: 16, xlarge: 18);
    final headingTextSize = baseTextSize * 2;
    final itemSpacing = size.height * 0.013;

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            InkWell(
              onTap: () {
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //     builder: (context) => Orderconfiemationmain(),
                //   ),
                // );
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.05,
                  vertical: size.height * 0.015,
                ),
                child: Text(
                  'My Orders',
                  style: TextStyle(
                    fontSize: twenty,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Inter',
                  ),
                ),
              ),
            ),
            // Tabs
            SizedBox(
              height: size.height * 0.05,
              child: Row(
                children: List.generate(_tabs.length, (index) {
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedOrder = null; // Clear selected order
                          _selectedTabIndex = index; // Update selected tab
                          _pageController.animateToPage(
                            index,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        });
                        if (index == 1) {
                          // Trigger MealPlanListEvent when switching to "Meal Plans" tab
                          context.read<OrderBloc>().add(MealPlanListEvent());
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: size.width * 0.001),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: _selectedTabIndex == index
                                  ? const Color(0xFFFFBE16)
                                  : Colors.transparent,
                              width: 2,
                            ),
                          ),
                        ),
                        child: Text(
                          _tabs[index],
                          style: TextStyle(
                            color: _selectedTabIndex == index
                                ? const Color(0xFF1F2122)
                                : const Color(0xFF66696D),
                            fontWeight: FontWeight.w600,
                            fontSize: baseTextSize,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
            SizedBox(height: itemSpacing),
            // Content
            Expanded(
              child: Stack(
                children: [
                  IgnorePointer(
                    ignoring: _selectedOrder != null,
                    child: PageView(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _selectedTabIndex = index;
                          _selectedOrder = null;
                        });
                        if (index == 1) {
                          // Meal Plans tab
                          context.read<OrderBloc>().add(MealPlanListEvent());
                        } else if (index == 2) {
                          // Past Orders tab
                          context
                              .read<OrderBloc>()
                              .add(BookingListEvent('past'));
                        } else {
                          // Ongoing Orders tab
                          context
                              .read<OrderBloc>()
                              .add(BookingListEvent('ongoing'));
                        }
                      },
                      children: [
                        _buildOngoingOrders(),
                        _buildMealPlans(),
                        _buildPastOrders(),
                      ],
                    ),
                  ),
                  if (_selectedOrder != null)
                    Container(
                      color: const Color(0xFFF6F3EC),
                      child: _buildExpandedOrderView(_selectedOrderId!),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: Initializer.cartCount ?? 0,
        onPressed: _openCart,
      ),
    );
  }

  void _openCart() {
    // Handle cart open action
    print('Opening cart');
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPage(),
        ));
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  // Helper method to format time only (12-hour format, e.g., 8:30 AM)
  String _formatTimeOnly(String time) {
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      final minute = parts.length > 1 ? int.parse(parts[1]) : 0;
      final period = hour >= 12 ? 'PM' : 'AM';
      if (hour == 0) hour = 12;
      if (hour > 12) hour -= 12;
      return '$hour:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  String formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return 'Unknown date';

    try {
      final DateTime dateTime = DateTime.parse(dateTimeStr);
      final String month = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ][dateTime.month - 1];

      final String day = dateTime.day.toString();
      final String year = dateTime.year.toString();
      final String hour = dateTime.hour > 12
          ? (dateTime.hour - 12).toString()
          : dateTime.hour == 0
              ? '12'
              : dateTime.hour.toString();
      final String minute = dateTime.minute.toString().padLeft(2, '0');
      final String period = dateTime.hour >= 12 ? 'PM' : 'AM';

      return '$month $day, $year, $hour:$minute$period';
    } catch (e) {
      return dateTimeStr;
    }
  }

  Widget _buildExpandedOrderView(int orderId) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    // Add responsive container sizing
    final cookingImageSize =
        isLandscape ? size.height * 0.30 : size.width * 0.40;
    final driverContainerWidth =
        isLandscape ? size.width * 0.2 : size.width * 0.45;
    final progressBarWidth =
        isLandscape ? size.width * 0.08 : size.width * 0.12;

    // Trigger the ViewOrederdetailsEvent with the orderId
    context.read<OrderBloc>().add(ViewOrederdetailsEvent(orderId));

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        // Handle side effects if needed, e.g., show error messages
        if (state is ViewOrederdetailsFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      buildWhen: (previous, current) {
        return current is ViewOrederdetailsLoading ||
            current is ViewOrederdetailsSuccess ||
            current is ViewOrederdetailsFailed;
      },
      builder: (context, state) {
        if (state is ViewOrederdetailsLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is ViewOrederdetailsFailed ||
            Initializer.viewOrderDetailsModel.data == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "Failed to Load Order Details",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  state is ViewOrederdetailsFailed
                      ? state.message
                      : "No order details available",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // Use data from Initializer.viewOrderDetailsModel
        final orderData = Initializer.viewOrderDetailsModel.data!;
        final chefName = orderData.chef?.name ?? 'Unknown Chef';
        final chefAvatar = orderData.chef?.photo != null
            ? ServerHelper.imageUrl + orderData.chef!.photo!
            : 'assets/images/chef_placeholder.png';
        final itemsCount = orderData.items?.length ?? 0;
        final deliveryAddress =
            orderData.address?.addressText ?? 'Unknown Address';

        // Updated to use the new delivery time structure
        final deliveryTimeDescription =
            orderData.deliveryTimes?.description ?? 'Unknown Time';
        final deliveryTime = orderData.deliveryTime ?? '';

        // Format delivery time for display (time only)
        String formattedDeliveryTime = '';
        if (deliveryTime.isNotEmpty) {
          formattedDeliveryTime = _formatTimeOnly(deliveryTime);
        } else {
          formattedDeliveryTime = deliveryTimeDescription;
        }

        final subtotal = orderData.subtotal?.toStringAsFixed(2) ?? '0.00';
        final deliveryFee = orderData.deliveryFee?.toStringAsFixed(2) ?? '0.00';
        final serviceFee = orderData.serviceFee?.toStringAsFixed(2) ?? '0.00';
        final discount = orderData.discount?.toStringAsFixed(2) ?? '0.00';
        final walletCredits =
            orderData.walletCredits?.toStringAsFixed(2) ?? '0.00';
        final taxesAndFees =
            orderData.taxesAndFees?.toStringAsFixed(2) ?? '0.00';
        final total = orderData.total?.toStringAsFixed(2) ?? '0.00';
        final status = orderData.status ?? 'Unknown';

        return Container(
          color: const Color(0xFFF6F3EC), // Match scaffold background color
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: GestureDetector(
                  onTap: () => setState(() => _selectedOrder = null),
                  child: Row(
                    children: [
                      Icon(
                        Icons.chevron_left,
                        size: isLandscape ? size.height * 0.04 : twentyFour,
                        color: const Color(0xFF1F2122),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Back',
                        style: TextStyle(
                          fontSize: isLandscape ? size.height * 0.03 : sixteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          decoration: TextDecoration.underline,
                          decorationThickness: 1.5,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    'assets/images/cooking.png',
                                    height: cookingImageSize,
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    '$chefName is\npreparing your order',
                                    style: TextStyle(
                                      fontSize: isLandscape
                                          ? size.height * 0.04
                                          : twenty, // ≈ 20,

                                      fontWeight: FontWeight.w600,
                                      color: Colors.black, height: 1.26,
                                      fontFamily: 'Inter',
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    'Estimated arrival $formattedDeliveryTime',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(5, (index) {
                                      return Container(
                                        width: progressBarWidth,
                                        height: size.height * 0.01,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.019),
                                        decoration: BoxDecoration(
                                          color: index < 3
                                              ? Colors.black
                                              : const Color(0xFFD9D9D9),
                                          borderRadius: BorderRadius.circular(
                                              size.width * 0.02),
                                        ),
                                      );
                                    }),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Your order will be delivered to $deliveryAddress at $formattedDeliveryTime',
                                    style: TextStyle(
                                      fontSize: twelve, // ≈ 12
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Container(
                                    width: driverContainerWidth,
                                    padding: EdgeInsets.all(size.width * 0.02),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF1F2F3),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Your Driver',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                        SizedBox(height: itemSpacing * 0.2),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            CircleAvatar(
                                              radius: isLandscape
                                                  ? size.height * 0.025
                                                  : baseTextSize,
                                              backgroundImage: const AssetImage(
                                                  'assets/images/driver.png'),
                                            ),
                                            SizedBox(width: size.width * 0.01),
                                            Text(
                                              'John Hancock', // This could be dynamic if driver data is available
                                              style: TextStyle(
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w600,
                                                fontFamily: 'Inter',
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: itemSpacing * 0.2),
                                        Text(
                                          '(910) 799-0420', // This could be dynamic if driver data is available
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: forteen),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Need help? ',
                                        style: TextStyle(
                                          fontSize: twelve,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  CustomerSupport(
                                                orderId: Initializer
                                                        .viewOrderDetailsModel
                                                        .data
                                                        ?.id ??
                                                    0,
                                              ), // Navigate to CustomerSupport
                                            ),
                                          );
                                        },
                                        child: Text(
                                          'Contact support.',
                                          style: TextStyle(
                                            fontSize: twelve,
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w700,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Order Summary card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Order Summary',
                                style: TextStyle(
                                  fontSize: isLandscape
                                      ? size.height * 0.04
                                      : eighteen, // ≈ 18
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF000000),
                                ),
                              ),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Ordered From',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: isLandscape
                                        ? size.height * 0.015
                                        : twelve,
                                    backgroundImage: NetworkImage(chefAvatar),
                                  ),
                                  SizedBox(width: size.width * 0.02),
                                  Text(
                                    chefName,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Order Details ($itemsCount Items)',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              // Display order items dynamically
                              if (orderData.items != null)
                                ...orderData.items!.map((item) {
                                  return Column(
                                    children: [
                                      _buildOrderItem(
                                        '${item.quantity}x',
                                        item.dish?.name ?? 'Unknown Dish',
                                        '\$${item.totalPrice?.toStringAsFixed(2) ?? '0.00'}',
                                      ),
                                      SizedBox(height: itemSpacing * 0.5),
                                    ],
                                  );
                                }),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Promotions',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                'DBEATS50', // This could be dynamic if promotion data is available
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Order Total',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              Column(
                                children: [
                                  _buildTotalItem('Subtotal', '\$$subtotal'),
                                  // SizedBox(height: itemSpacing * 0.1),
                                  _buildTotalItem(
                                      'Delivery fee', '\$$deliveryFee'),
                                  _buildTotalItem(
                                      'Service fee', '\$$serviceFee'),
                                  //   SizedBox(height: itemSpacing * 0.4),
                                  _buildTotalItem('Discounts', '-\$$discount'),
                                  //   SizedBox(height: itemSpacing * 0.4),
                                  _buildTotalItem(
                                      'DB Wallet Credits', '-\$$walletCredits'),
                                  //     SizedBox(height: itemSpacing * 0.4),
                                  _buildTotalItem(
                                      'Sales Tax', '\$$taxesAndFees'),
                                  DottedDivider(),
                                  _buildTotalItem('Total', '\$$total',
                                      isTotal: true),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Delivery Details card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Delivery Details',
                                style: TextStyle(
                                  fontSize: isLandscape
                                      ? size.height * 0.04
                                      : eighteen,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF000000),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Address',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                children: [
                                  Icon(Icons.location_on_outlined,
                                      size: baseTextSize * 1.2,
                                      color: const Color(0xFF414346)),
                                  SizedBox(width: size.width * 0.01),
                                  Text(
                                    deliveryAddress,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Delivery',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                children: [
                                  Icon(Icons.access_time,
                                      size: baseTextSize * 1.0,
                                      color: const Color(0xFF414346)),
                                  SizedBox(width: size.width * 0.01),
                                  Text(
                                    formattedDeliveryTime,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Divider(
                                height: 0,
                                thickness: 1,
                                color: const Color(0xFFE1E3E6),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Drop-Off Options',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter-medium',
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                orderData.dropOffOption?.name ?? 'Unknown',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Drop-Off Instructions',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter-medium',
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                orderData.dropOffInstructions ?? 'None',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Divider(
                                height: 0,
                                thickness: 1,
                                color: const Color(0xFFE1E3E6),
                              ),
                              SizedBox(height: itemSpacing),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Delivery Time',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    (orderData.deliveryTimes?.description ??
                                            '30-60 Minutes')
                                        .replaceFirst('Delivery between ', ''),
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Image.asset(
                                        'assets/icons/star_2.png',
                                        width: baseTextSize * 1.0,
                                        height: baseTextSize * 1.0,
                                        color: const Color(0xFF414346),
                                      ),
                                      SizedBox(width: size.width * 0.02),
                                      Text(
                                        orderData.deliveryTimes?.name ??
                                            'Priority',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    '+\$${orderData.deliveryTimes?.cost ?? '0.00'}',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                ("     ${orderData.deliveryTimes?.description} " ??
                                        '30-60 Minutes')
                                    .replaceFirst('Delivery between ', ''),
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF414346),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ), // Payment card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.04),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Payment',
                                    style: TextStyle(
                                      fontSize: forteen * 1.0,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE8F5E9),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          'assets/icons/tick.png',
                                          width:
                                              12, // You can adjust width and height as needed
                                          height: 12,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          'Paid',
                                          style: TextStyle(
                                            fontSize: twelve,
                                            color: const Color(0xFF2E7D32),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.width * 0.03),
                              Row(
                                children: [
                                  Image.asset(
                                    'assets/icons/Visa.png',
                                    height: baseTextSize * 1.2,
                                    fit: BoxFit.contain,
                                  ),
                                  SizedBox(width: size.width * 0.03),
                                  Expanded(
                                    child: Text(
                                      'Paid through card ending with 0001',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.width * 0.01),
                              Padding(
                                padding:
                                    EdgeInsets.only(left: size.width * 0.02),
                                child: Text(
                                  '      Expires 03/2028',
                                  style: TextStyle(
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w400,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Email notification
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(size.width * 0.02),
                            decoration: BoxDecoration(
                              color: const Color(0xFFE1DDD5),
                              borderRadius: BorderRadius.circular(6),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Icon(
                                    Icons.mail_outline,
                                    size: baseTextSize * 1.25,
                                    color: const Color(0xFF414346),
                                  ),
                                ),
                                SizedBox(width: size.width * 0.02),
                                Expanded(
                                  child: Text(
                                    'A copy of your invoice is on its way to your inbox. Please check your email.',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ),
                              ],
                            )),
                      ),
                      SizedBox(
                        height: 40,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper method for order items
  Widget _buildOrderItem(String quantity, String title, String price) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;

    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.005,
          ),
          decoration: BoxDecoration(
            color: Color(0xFFE1E3E6),
            borderRadius: BorderRadius.circular(size.width * 0.04),
          ),
          child: Text(
            quantity,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.022 : forteen,
              // fontWeight: FontWeight.w500,
              fontFamily: 'Inter-medium',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        SizedBox(width: size.width * 0.03),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.025 : forteen,
              // fontWeight: FontWeight.w600,
              fontFamily: 'Inter-Semibold',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        Text(
          price,
          style: TextStyle(
            fontSize: isLandscape ? size.height * 0.022 : forteen,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: Color(0xFF414346),
          ),
        ),
      ],
    );
  }

  // Helper method for total items
  Widget _buildTotalItem(String label, String amount, {bool isTotal = false}) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;
    final isDiscount = label == 'Discounts' || label == 'DB Wallet Credits';

    return Padding(
      padding: EdgeInsets.only(
        bottom: isTotal ? 0 : size.height * 0.01,
        left: size.width * 0.01,
        right: size.width * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.025 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: Color(0xFF1F2122),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.022 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: isDiscount ? Color(0xFFD31510) : Color(0xFF414346),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOngoingOrders() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.015;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {},
      buildWhen: (context, state) {
        return state is BookingListLoading ||
            state is BookingListSuccess ||
            state is BookingListFailed;
      },
      builder: (context, state) {
        if (state is BookingListLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (Initializer.ongoingBookinglistmodel.data == null ||
            Initializer.ongoingBookinglistmodel.data!.bookings!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Ongoing Orders Right Now!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "Looks like you haven’t placed any orders yet.\nStart exploring delicious meals!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
            padding: EdgeInsets.only(
                left: size.width * 0.05,
                right: size.width * 0.05,
                bottom: size.width * 0.05),
            child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  children: List.generate(
                      Initializer.ongoingBookinglistmodel.data!.bookings!
                          .length, (index) {
                    // final Map<String, dynamic> order = Initializer
                    //     .ongoingBookinglistmodel
                    //     .data!
                    //     .bookings![index] as Map<String, dynamic>;
                    return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedOrderId = Initializer
                                .ongoingBookinglistmodel
                                .data!
                                .bookings![index]
                                .id;
                            _selectedOrder = {};
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(
                            size.width * 0.04,
                            index == 0 ? size.width * 0.075 : size.width * 0.04,
                            size.width * 0.04,
                            index <
                                    Initializer.ongoingBookinglistmodel.data!
                                            .bookings!.length -
                                        1
                                ? size.width * 0.002
                                : size.width * 0.025,
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Avatar
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(
                                        size.width * 0.06),
                                    child: Image.network(
                                      ServerHelper.imageUrl +
                                          Initializer
                                              .ongoingBookinglistmodel
                                              .data!
                                              .bookings![index]
                                              .chef!
                                              .photo
                                              .toString(),
                                      width: size.width * 0.11,
                                      height: size.width * 0.11,
                                      fit: BoxFit.cover,
                                      errorBuilder: (_, __, ___) => Icon(
                                          Icons.person,
                                          size: size.width * 0.06),
                                    ),
                                  ),
                                  SizedBox(width: size.width * 0.03),

                                  // Info Block
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Row with chef name + item count
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                Initializer
                                                    .ongoingBookinglistmodel
                                                    .data!
                                                    .bookings![index]
                                                    .chef!
                                                    .name
                                                    .toString()
                                                    .split(' ')
                                                    .map((word) => word
                                                            .isNotEmpty
                                                        ? word[0]
                                                                .toUpperCase() +
                                                            (word.length > 1
                                                                ? word
                                                                    .substring(
                                                                        1)
                                                                    .toLowerCase()
                                                                : '')
                                                        : '')
                                                    .join(' '),
                                                style: TextStyle(
                                                  fontSize: baseTextSize * 1.1,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: 'Inter-Semibold',
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  _selectedOrderId = Initializer
                                                      .ongoingBookinglistmodel
                                                      .data!
                                                      .bookings![index]
                                                      .id;
                                                  _selectedOrder = {
                                                    'chefName': Initializer
                                                        .ongoingBookinglistmodel
                                                        .data!
                                                        .bookings![index]
                                                        .chef!
                                                        .name,
                                                    'avatar': ServerHelper
                                                            .imageUrl +
                                                        Initializer
                                                            .ongoingBookinglistmodel
                                                            .data!
                                                            .bookings![index]
                                                            .chef!
                                                            .photo
                                                            .toString(),
                                                    'id': _selectedOrderId,
                                                  };
                                                });
                                              },
                                              child: Row(
                                                children: [
                                                  Text(
                                                    '${Initializer.ongoingBookinglistmodel.data!.bookings![index].itemsCount ?? 0} items',
                                                    style: TextStyle(
                                                      fontSize:
                                                          baseTextSize * 0.8,
                                                      fontFamily:
                                                          'Inter-Semibold',
                                                      decoration: TextDecoration
                                                          .underline,
                                                    ),
                                                  ),
                                                  SizedBox(width: 4),
                                                  Image.asset(
                                                    'assets/icons/right_arrow_black.png',
                                                    width: baseTextSize,
                                                    height: baseTextSize,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),

                                        SizedBox(height: itemSpacing * 0.4),

                                        // Row with calendar icon and hours
                                        Row(
                                          children: [
                                            Image.asset(
                                              'assets/icons/calender_2.png',
                                              width: 13,
                                              height: 13,
                                              color: Color(0xFF414346),
                                            ),
                                            SizedBox(width: 4),
                                            Text(
                                              // Show only first char of each day, joined by space
                                              'Open ${Initializer.ongoingBookinglistmodel.data!.bookings![index].chef!.chefOperationTime != null ? '${to12HourFormat(Initializer.ongoingBookinglistmodel.data!.bookings![index].chef!.chefOperationTime!.startTime ?? "")} - ${to12HourFormat(Initializer.ongoingBookinglistmodel.data!.bookings![index].chef!.chefOperationTime!.endTime ?? "")}' : ""}',
                                              style: TextStyle(
                                                fontSize: baseTextSize * 0.7,
                                                fontFamily: 'Inter',
                                                color: Color(0xFF414346),
                                              ),
                                            ),
                                          ],
                                        ),

                                        SizedBox(height: itemSpacing * 0.3),

                                        // Row with days + status chip
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                (Initializer
                                                                .ongoingBookinglistmodel
                                                                .data!
                                                                .bookings![index]
                                                                .chef!
                                                                .chefOperationDays
                                                            as List<dynamic>?)
                                                        ?.map((day) => day
                                                                .toString()
                                                                .isNotEmpty
                                                            ? day.toString()[0]
                                                            : '')
                                                        .join(', ') ??
                                                    '',
                                                style: TextStyle(
                                                  fontSize: baseTextSize * 0.7,
                                                  fontFamily: 'Inter',
                                                  color: Color(0xFF414346),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: size.width * 0.020,
                                                vertical: size.height * 0.001,
                                              ),
                                              decoration: BoxDecoration(
                                                color: const Color(0xFFE1E3E6),
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                              child: Text(
                                                Initializer
                                                    .ongoingBookinglistmodel
                                                    .data!
                                                    .bookings![index]
                                                    .status
                                                    .toString(),
                                                style: TextStyle(
                                                  fontSize: baseTextSize * 0.7,
                                                  fontFamily: 'Inter-medium',
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              // if (index <
                              //     Initializer.ongoingBookinglistmodel.data!
                              //             .bookings!.length -
                              //         1)
                              Padding(
                                padding:
                                    EdgeInsets.only(top: itemSpacing * 1.5),
                                child: Divider(
                                  height: 0,
                                  thickness: 1,
                                  color: Color(0xFFE1E3E6),
                                ),
                              ),
                            ],
                          ),
                        ));
                  }),
                )));
      },
    );
  }

  String _formatDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return 'Unknown Date Range';

    try {
      final DateTime start = DateTime.parse(startDate);
      final DateTime end = DateTime.parse(endDate);
      final DateFormat formatter = DateFormat('MMMM d');
      final String startFormatted = formatter.format(start);
      final String endFormatted = formatter.format(end);
      return '$startFormatted to $endFormatted';
    } catch (e) {
      return '$startDate to $endDate'; // Fallback to raw strings if parsing fails
    }
  }

  String _formatStatusText(String status) {
    if (status.isEmpty) return status;
    return status[0].toUpperCase() + status.substring(1).toLowerCase();
  }

  Widget _buildMealPlans() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final contentPadding = getResponsivePadding(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    // Calculate responsive card padding
    final cardPadding = EdgeInsets.symmetric(
      horizontal: size.width * 0.06,
      vertical: size.width * 0.03,
    );

    // Calculate spacing between elements
    final elementSpacing = size.height * 0.01;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        // Handle any side effects if needed
      },
      buildWhen: (previous, current) {
        return current is MealPlanListLoading ||
            current is MealPlanListSuccess ||
            current is MealPlanListFailed;
      },
      builder: (context, state) {
        if (state is MealPlanListLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is MealPlanListFailed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "Failed to Load Meal Plans",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  state.message,
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // Check if meal plan data is available and not empty
        if (Initializer.mealPlanListModel.data == null ||
            Initializer.mealPlanListModel.data!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Meal Plans Available",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "Looks like you haven't subscribed to any meal plans yet.\nStart exploring now!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // Display meal plans from Initializer
        return SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: List.generate(
                Initializer.mealPlanListModel.data!.length,
                (index) {
                  final plan = Initializer.mealPlanListModel.data![index];
                  final String title =
                      _formatDateRange(plan.startDate, plan.endDate);
                  final String deliveryProgress =
                      '${plan.deliveredDays ?? 0} / ${plan.totalDays ?? 0} Delivered';
                  final String dishesPerDay =
                      '${plan.dishesPerDay ?? 0} ${(plan.dishesPerDay ?? 0) == 1 ? 'meal' : 'meals'} a day';

                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => WithNavBar(
                            currentIndex: 1,
                            child: OrdersPage(id: plan.id ?? 0),
                          ),
                        ),
                      );
                    },
                    child: Column(
                      children: [
                        Container(
                          padding: cardPadding,
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // Title with flexible width
                                  Expanded(
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        title,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: sixteen,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: size.width * 0.02),

                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: size.width * 0.02,
                                      vertical: size.height * 0.001,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          _getStatusColor(plan.status ?? '')[0],
                                      borderRadius: BorderRadius.circular(
                                          size.width * 0.04),
                                    ),
                                    child: Text(
                                      _formatStatusText(
                                          plan.status ?? 'Unknown'),
                                      style: TextStyle(
                                        color: _getStatusColor(
                                            plan.status ?? '')[1],
                                        fontSize: twelve,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ),

                                  // More options icon
                                  //   IconButton(  padding: EdgeInsets.zero,
                                  //  //   padding: EdgeInsets.all(size.width * 0.01),
                                  //     constraints: BoxConstraints(
                                  //       minWidth: forteen,
                                  //       minHeight: forteen,
                                  //     ),
                                  //     icon: Icon(
                                  //       Icons.more_vert,
                                  //       size: sixteen,
                                  //       color: const Color(0xFF1F2122),
                                  //     ),
                                  //     onPressed: () {
                                  //       // Handle more options
                                  //     },
                                  //   ),

                                  SizedBox(
                                    width: twenty + twenty,
                                    height: eighteen,
                                    child: IconButton(
                                      padding: EdgeInsets.zero,
                                      constraints:
                                          const BoxConstraints(), // Removes default constraints
                                      icon: Icon(
                                        Icons.more_vert,
                                        size: sixteen,
                                        color: const Color(0xFF1F2122),
                                      ),
                                      onPressed: () {
                                        // Handle more options
                                      },
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: sixteen / 2,
                              ),
                              // Meals per day and delivery progress info
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // SizedBox(height: elementSpacing * 0.8),
                                    Text(
                                      dishesPerDay,
                                      style: TextStyle(
                                        color: const Color(0xFF414346),
                                        fontSize: forteen,
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    SizedBox(height: elementSpacing * 0.8),
                                    Text(
                                      deliveryProgress,
                                      style: TextStyle(
                                        color: const Color(0xFF1F2122),
                                        fontSize: forteen,
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Add divider if not the last item
                        if (index <
                            Initializer.mealPlanListModel.data!.length - 1)
                          Divider(
                            height: 1,
                            thickness: 1,
                            color: const Color(0xFFE1E3E6),
                            indent: size.width * 0.04,
                            endIndent: size.width * 0.04,
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  // Updated Past Orders implementation to match the image with a single container
  Widget _buildPastOrders() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.015;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {},
      buildWhen: (context, state) {
        return state is BookingListLoading ||
            state is BookingListSuccess ||
            state is BookingListFailed;
      },
      builder: (context, state) {
        if (state is BookingListLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (Initializer.ongoingBookinglistmodel.data == null ||
            Initializer.ongoingBookinglistmodel.data!.bookings!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Past Orders",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "You haven't placed any orders yet",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: List.generate(
                Initializer.ongoingBookinglistmodel.data!.bookings!.length,
                (index) {
                  final booking = Initializer
                      .ongoingBookinglistmodel.data!.bookings![index];
                  return Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.04,
                          vertical: index == 0
                              ? size.height * 0.019
                              : size.height * 0.03,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // Chef Avatar
                                ClipRRect(
                                  borderRadius:
                                      BorderRadius.circular(size.width * 0.06),
                                  child: Image.network(
                                    ServerHelper.imageUrl +
                                        (booking.chef?.photo ?? ''),
                                    width: ten * 4,
                                    height: ten * 4,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) => Icon(
                                      Icons.person,
                                      size: size.width * 0.06,
                                    ),
                                  ),
                                ),
                                SizedBox(width: size.width * 0.03),

                                // Chef name and total
                                Expanded(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Text(
                                          booking.chef?.name ?? 'Unknown Chef',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: sixteen,
                                            fontFamily: 'Inter',
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      Text(
                                        '\$${booking.total?.toStringAsFixed(2) ?? '0.00'}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: forteen,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: itemSpacing * 0.6),

                            // Order details
                            Padding(
                              padding: EdgeInsets.only(left: size.width * 0.01),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${booking.itemsCount ?? 0} items',
                                    style: TextStyle(
                                      color: const Color(0xFF414346),
                                      fontSize: forteen,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.007),
                                  Text(
                                    formatDateTime(booking.createdAt),
                                    style: TextStyle(
                                      color: const Color(0xFF414346),
                                      fontSize: forteen,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),

                                  // Actions
                                  Wrap(
                                    spacing: size.width * 0.03,
                                    runSpacing: size.height * 0.01,
                                    children: [
                                      _buildActionButton(
                                        'Rate Driver',
                                        // (){},
                                        () => Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                DriverRatingPage(
                                              driverName: booking.chef?.name ??
                                                  'Unknown Chef',
                                              driverImage:
                                                  booking.chef?.photo ?? '',
                                            ),
                                          ),
                                        ),

                                        baseTextSize,
                                      ),
                                      _buildActionButton(
                                        'Leave chef a review',
                                        () => Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                ChefReviewPage(
                                              chefName: booking.chef?.name ??
                                                  'Unknown Chef',
                                              chefImage:
                                                  booking.chef?.photo ?? '',
                                              orderId: booking.id ?? 0,
                                              chefId: booking.chef?.id ?? 0,
                                            ),
                                          ),
                                        ),
                                        baseTextSize,
                                      ),
                                      _buildActionButton(
                                        'Reorder',
                                        () {
                                          // Handle reorder action
                                        },
                                        baseTextSize,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (index <
                          Initializer.ongoingBookinglistmodel.data!.bookings!
                                  .length -
                              1)
                        const Divider(
                          height: 1,
                          thickness: 1,
                          color: Color(0xFFE1E3E6),
                        ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton(
      String text, VoidCallback onTap, double baseTextSize) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            text,
            style: TextStyle(
              color: const Color(0xFF414346),
              fontSize: twelve,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.underline,
              decorationThickness: 1,
            ),
          ),
          SizedBox(width: 4),
          Image.asset(
            'assets/icons/black_right_arrow.png',
            width: baseTextSize,
            height: baseTextSize,
          ),
        ],
      ),
    );
  }

  // Helper method to get status color
  List<Color> _getStatusColor(String status) {
    switch (status) {
      case 'Action Required':
        return [const Color(0xFFFFEBE7), const Color(0xFFD31510)];
      case 'Ongoing':
        return [const Color(0xFFCEF8E0), const Color(0xFF007A4D)];
      case 'Scheduled':
        return [const Color(0xFFE1E3E6), const Color(0xFF1F2122)];
      default:
        return [Colors.grey[200]!, Colors.black87];
    }
  }

  String to12HourFormat(String? time) {
    if (time == null || time.isEmpty) return "";
    try {
      // Parse the time (assuming format like "14:00")
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      String minute = parts[1];

      // Determine AM/PM
      String period = hour >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      if (hour == 0) {
        hour = 12; // Midnight
      } else if (hour > 12) {
        hour -= 12;
      }

      // Pad hour with leading zero if less than 10
      final hourStr = hour < 10 ? '0$hour' : '$hour';
      return '$hourStr $period';
    } catch (e) {
      return time; // Return original time if parsing fails
    }
  }
}
