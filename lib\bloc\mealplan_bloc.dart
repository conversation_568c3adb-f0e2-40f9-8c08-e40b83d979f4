import 'dart:convert';
import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/data/addedtimepreferencemodel.dart';
import 'package:db_eats/data/models/meal_plan/cateringtypemodel.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/data/models/meal_plan/existingmealplanmodel.dart';
import 'package:db_eats/data/models/meal_plan/filterchefsmodel.dart';
import 'package:db_eats/data/models/meal_plan/filtereddishmodel.dart';
import 'package:db_eats/data/models/catering/findchefmodel.dart';
import 'package:db_eats/data/models/meal_plan/listdiatarymodel.dart';
import 'package:db_eats/data/models/meal_plan/mealplanprogressmodel.dart';
import 'package:db_eats/data/models/meal_plan/servinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/spicelevellistmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/typeofpackagingmodel.dart';
import 'package:db_eats/server/serverhelper.dart';

class MealplanBloc extends Bloc<MealPlanEvent, MealPlanState> {
  MealplanBloc() : super(MealPlanState()) {
    on<ListTimingEvent>(_listTiming);
    on<ListServingEvent>(_listServing);
    on<ListCuisineEvent>(_listCuisine);
    on<ListDietaryEvent>(_listDietary);
    on<ListDietary2>(_listDietary2);
    on<ListPackagingTimeEvent>(_listPackagingTime);
    on<CateringTypeEvent>(_listCateringType);
    on<FindChefEvent>(_findChef);
    on<ListSpiceLevelEvent>(_listSpiceLevel);
    on<ListDropoffOptionEvent>(_listDropoffOption);
    on<ListDeliveryTimeEvent>(_listDeliveryTime);
    on<MealPlanProgressEvent>(_listMealPlanProgress);
    on<FilterChefsEvent>(_filterchef);
    on<Step1MealPlanEvent>(_step1MealPlan);
    on<Step2MealPlanEvent>(_step2MealPlan);
    on<Step3MealPlanEvent>(_step3MealPlan);
    on<Step4MealPlanEvent>(_step4MealPlan);
    on<Step5MealPlanEvent>(_step5MealPlan);
    on<Step6MealPlanEvent>(_step6MealPlan);
    on<Step7MealPlanEvent>(_step7MealPlan);
    on<ListFilterdDishes>(_listFilteredDishes);
    on<AddTimePreferences>(_addTimePreferences);
    on<CheckExistingMealPlanEvent>(_checkExistingMealPlan);
    on<ListCuisines2>(_listCuisines2);
    on<GetAddedTimePreferences>(_getAddedTimePreferences);
    on<AddEditMealPlanEvent>(_addEditMealPlan);
    on<ViewDayEvent>(_viewDay);
  }

  Future<void> _listTiming(
      ListTimingEvent event, Emitter<MealPlanState> emit) async {
    emit(ListTimingLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/timing/list');
      log('Time Listing Response: $response');

      TimingListModel timingListModel = TimingListModel.fromJson(response);

      if (timingListModel.status == true) {
        emit(ListTimingSuccess(timingListModel));
      } else {
        emit(ListTimingFailed(
            timingListModel.message ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListTimingFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listServing(
      ListServingEvent event, Emitter<MealPlanState> emit) async {
    emit(ListServingLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/serving-size/list');
      log('Serving Listing Response: $response');

      ServingsListModel servingListModel = ServingsListModel.fromJson(response);

      if (servingListModel.status == true) {
        emit(ListServingSuccess(servingListModel));
      } else {
        emit(ListServingFailed(
            servingListModel.message ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListServingFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listCuisine(
      ListCuisineEvent event, Emitter<MealPlanState> emit) async {
    emit(ListCuisineLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/cuisine/all-list');
      log('Cuisine Listing Response: $response');

      CuisinesListModel cuisinesListModel =
          CuisinesListModel.fromJson(response);
      if (cuisinesListModel.status == true) {
        emit(ListCuisineSuccess(cuisinesListModel));
      } else {
        emit(
            ListCuisineFailed(response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListCuisineFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listCuisines2(
      ListCuisines2 event, Emitter<MealPlanState> emit) async {
    emit(ListCuisines2Loading());
    try {
      final response = await ServerHelper.get1('/v1/common/cuisine/all-list');
      log('Cuisine Listing Response: $response');

      Initializer.cuisinesListModel = CuisinesListModel.fromJson(response);

      if (Initializer.cuisinesListModel.status == true) {
        emit(ListCuisines2Success(Initializer.cuisinesListModel.data));
      } else {
        emit(ListCuisines2Failed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListCuisines2Failed('Error occurred while loading profile'));
    }
  }

  Future<void> _getAddedTimePreferences(
      GetAddedTimePreferences event, Emitter<MealPlanState> emit) async {
    emit(GettingAddedTimePreferencesLoading());
    try {
      final response =
          await ServerHelper.get1('/v1/customer/address/time-preference');
      log('Get Added Time Preferences Response: $response');

      Initializer.addedTimePreferenceModel =
          AddedTimePreferenceModel.fromJson(response);

      if (Initializer.addedTimePreferenceModel.status == true) {
        emit(GettingAddedTimePreferencesSuccess(
            Initializer.addedTimePreferenceModel.data));
      } else {
        emit(GettingAddedTimePreferencesFailed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(GettingAddedTimePreferencesFailed(
          'Error occurred while loading profile'));
    }
  }

  Future<void> _listDietary(
      ListDietaryEvent event, Emitter<MealPlanState> emit) async {
    emit(ListDietaryLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/dietary/list');
      log('Dietary Listing Response: $response');

      DietaryListModel dietaryListModel = DietaryListModel.fromJson(response);
      if (dietaryListModel.status == true) {
        emit(ListDietarySuccess(dietaryListModel));
      } else {
        emit(
            ListDietaryFailed(response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDietaryFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listDietary2(
      ListDietary2 event, Emitter<MealPlanState> emit) async {
    emit(ListDietary2Loading());
    try {
      final response = await ServerHelper.get1('/v1/common/dietary/list');
      log('Dietary Listing Response: $response');

      Initializer.dietaryListModel = DietaryListModel.fromJson(response);

      if (Initializer.dietaryListModel.status == true) {
        emit(ListDietary2Success(Initializer.dietaryListModel.data));
      } else {
        emit(ListDietary2Failed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDietary2Failed('Error occurred while loading profile'));
    }
  }

  Future<void> _listPackagingTime(
      ListPackagingTimeEvent event, Emitter<MealPlanState> emit) async {
    emit(ListPackagingTimeLoading());
    try {
      final response =
          await ServerHelper.get1('/v1/common/packaging-type/list');
      log('ListPackagingTime  Response: $response');

      TypeOfPackagingModel typeOfPackagingModel =
          TypeOfPackagingModel.fromJson(response);
      if (typeOfPackagingModel.status == true) {
        emit(ListPackagingTimeSucess(typeOfPackagingModel));
      } else {
        emit(ListPackagingTimeFailed(
            response['message'] ?? 'Failed to load ListPackagingTimeLoading'));
      }
    } catch (e) {
      log('Error viewing ListPackagingTimeLoading: $e');
      emit(ListPackagingTimeFailed(
          'Error occurred while loading ListPackagingTimeLoading'));
    }
  }

  Future<void> _listCateringType(
      CateringTypeEvent event, Emitter<MealPlanState> emit) async {
    emit(CateringTypeLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/catering-type/list');
      log('CateringTypeLoading  Response: $response');

      CateringTypeModel cateringTypeModel =
          CateringTypeModel.fromJson(response);
      if (cateringTypeModel.status == true) {
        emit(CateringTypeSuccess(cateringTypeModel));
      } else {
        emit(CateringTypeFailed(
            response['message'] ?? 'Failed to load CateringTypeLoading'));
      }
    } catch (e) {
      log('Error viewing CateringTypeLoading: $e');
      emit(CateringTypeFailed(
          'Error occurred while loading CateringTypeLoading'));
    }
  }

  Future<void> _findChef(
      FindChefEvent event, Emitter<MealPlanState> emit) async {
    emit(FindChefEventLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/catering/filter-chefs',
        event.data,
      );

      log('chef find  Response: $response');

      FindChefModel findChefModel = FindChefModel.fromJson(response);
      if (findChefModel.status == true) {
        log(findChefModel.data.toString());
        emit(FindChefEventSuccess(findChefModel.data?.toJson()));
      } else {
        emit(FindChefEventFailed(
            response['message'] ?? 'Failed to load ListPackagingTimeLoading'));
      }
    } catch (e) {
      log('Error viewing ListPackagingTimeLoading: $e');
      emit(FindChefEventFailed(
          'Error occurred while loading ListPackagingTimeLoading'));
    }
  }

  Future<void> _listSpiceLevel(
      ListSpiceLevelEvent event, Emitter<MealPlanState> emit) async {
    emit(ListSpiceLevelLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/spice-level/list');
      log('Spice Level Listing Response: $response');

      SpiceLevelListModel spiceLevelListModel =
          SpiceLevelListModel.fromJson(response);
      if (spiceLevelListModel.status == true) {
        emit(ListSpiceLevelSuccess(spiceLevelListModel));
      } else {
        emit(ListSpiceLevelFailed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListSpiceLevelFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listDropoffOption(
      ListDropoffOptionEvent event, Emitter<MealPlanState> emit) async {
    emit(ListDropoffOptionLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/drop_off_option');
      log('Dropoff Option Listing Response: $response');

      DropoffOptionsModel dropoffOptionsModel =
          DropoffOptionsModel.fromJson(response);
      if (dropoffOptionsModel.status == true) {
        emit(ListDropoffOptionSuccess(dropoffOptionsModel));
      } else {
        emit(ListDropoffOptionFailed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDropoffOptionFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listDeliveryTime(
      ListDeliveryTimeEvent event, Emitter<MealPlanState> emit) async {
    emit(ListDeliveryTimeLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/delivery_time');
      log('Delivery Time Listing Response: $response');

      DeliveryTimeModel deliveryTimeModel =
          DeliveryTimeModel.fromJson(response);
      if (deliveryTimeModel.status == true) {
        emit(ListDeliveryTimeSuccess(deliveryTimeModel));
      } else {
        emit(ListDeliveryTimeFailed(
            response['message'] ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDeliveryTimeFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _listMealPlanProgress(
      MealPlanProgressEvent event, Emitter<MealPlanState> emit) async {
    emit(MealPlanProgressLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal-plan/progress',
          {"meal_plan_id": event.mealPlanId});
      log('Meal Plan Progress Response: $response');

      MealPlanProgressModel mealPlanProgressModel =
          MealPlanProgressModel.fromJson(response);
      if (mealPlanProgressModel.status == true) {
        emit(MealPlanProgressSuccess(mealPlanProgressModel.data));
      } else {
        emit(MealPlanProgressFailed(
            response['message'] ?? 'Failed to load meal plan progress'));
      }
    } catch (e) {
      log('Error loading meal plan progress: $e');
      emit(MealPlanProgressFailed('Error occurred while loading meal plan'));
    }
  }

  Future<void> _filterchef(
      FilterChefsEvent event, Emitter<MealPlanState> emit) async {
    emit(FilterChefsLoading());
    try {
      log('Filter Request Data: ${event.filter}');
      final response = await ServerHelper.post1(
          '/v1/customer/meal-plan/filter-chefs', event.filter);
      log('Filter Chefs Response: $response');

      if (response == null) {
        throw ServerException('Received null response from server');
      }

      if (response is! Map<String, dynamic>) {
        throw ServerException('Invalid response format: Expected Map');
      }

      FilterChefModel filterChefModel = FilterChefModel.fromJson(response);
      if (filterChefModel.status == true) {
        if (filterChefModel.data?.chefs == null) {
          emit(FilterChefsFailed('No chefs data available'));
          return;
        }
        emit(FilterChefsSuccess(filterChefModel.data));
      } else {
        emit(FilterChefsFailed(
            response['message'] ?? 'Failed to load filtered chefs'));
      }
    } catch (e, stackTrace) {
      log('Error loading filtered chefs: $e');
      log('Stack trace: $stackTrace');
      emit(FilterChefsFailed(
          'Error occurred while loading filtered chefs: ${e.toString()}'));
    }
  }

  Future<void> _step1MealPlan(
      Step1MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step1MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step1', event.data);
      log('Step 1 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step1MealPlanSuccess(response['data'])); // Pass full data object
      } else {
        emit(Step1MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step1MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step2MealPlan(
      Step2MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step2MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step2', event.data);
      log('Step 2 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step2MealPlanSuccess(response['data']));
      } else {
        emit(Step2MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step2MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step3MealPlan(
      Step3MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step3MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step3', event.data);
      log('Step 3 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step3MealPlanSuccess(response['data']));
      } else {
        emit(Step3MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step3MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step4MealPlan(
      Step4MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step4MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step4', event.data);
      log('Step 4 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step4MealPlanSuccess(response['data']));
      } else {
        emit(Step4MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step4MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step5MealPlan(
      Step5MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step5MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step5', event.data);
      log('Step 5 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step5MealPlanSuccess(response['data']));
      } else {
        emit(Step5MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step5MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step6MealPlan(
      Step6MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step6MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step6', event.data);
      log('Step 6 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step6MealPlanSuccess(response['data']));
      } else {
        emit(Step6MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step6MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _step7MealPlan(
      Step7MealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(Step7MealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/step7', event.data);
      log('Step 7 Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(Step7MealPlanSuccess(response['data']));
      } else {
        emit(Step7MealPlanFailed(
            response['message'] ?? 'Failed to save meal plan'));
      }
    } catch (e) {
      log('Error saving meal plan: $e');
      emit(Step7MealPlanFailed('Error occurred while saving meal plan'));
    }
  }

  Future<void> _listFilteredDishes(
      ListFilterdDishes event, Emitter<MealPlanState> emit) async {
    emit(ListFilterdDishesLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal-plan/filter-dishes', event.data);
      log('Filtered Dishes Response: $response');

      if (response != null && response is Map<String, dynamic>) {
        final FilteredDishesModel filteredDishesModel =
            FilteredDishesModel.fromJson(response);
        if (filteredDishesModel.status == true) {
          emit(ListFilterdDishesSuccess(filteredDishesModel));
        } else {
          emit(ListFilterdDishesFailed(
              filteredDishesModel.message ?? 'Failed to load filtered dishes'));
        }
      } else {
        emit(ListFilterdDishesFailed('Invalid response format'));
      }
    } catch (e) {
      log('Error loading filtered dishes: $e');
      emit(ListFilterdDishesFailed('Error occurred while loading dishes'));
    }
  }

  Future<void> _addTimePreferences(
      AddTimePreferences event, Emitter<MealPlanState> emit) async {
    emit(AddTimePreferencesLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/address/time-preference', event.data);
      log('Add Time Preferences Response: $response');

      if (response['status'] == true) {
        emit(AddTimePreferencesSuccess(response['data']));
      } else {
        emit(AddTimePreferencesFailed(
            response['message'] ?? 'Failed to add time preferences'));
      }
    } catch (e) {
      log('Error adding time preferences: $e');
      emit(AddTimePreferencesFailed(
          'Error occurred while adding time preferences'));
    }
  }

  Future<void> _checkExistingMealPlan(
      CheckExistingMealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(CheckExistingMealPlanLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/meal-plan/progress', {});
      log('Check Existing Meal Plan Response: $response');

      ExistingMealPlanModel existingMealPlanModel =
          ExistingMealPlanModel.fromJson(response);

      if (existingMealPlanModel.status == true) {
        emit(CheckExistingMealPlanSuccess(existingMealPlanModel.data));
      } else {
        emit(CheckExistingMealPlanFailed(
            response['message'] ?? 'Failed to check existing meal plan'));
      }
    } catch (e) {
      log('Error checking existing meal plan: $e');
      emit(CheckExistingMealPlanFailed(
          'Error occurred while checking existing meal plan'));
    }
  }

  Future<void> _addEditMealPlan(
      AddEditMealPlanEvent event, Emitter<MealPlanState> emit) async {
    emit(AddEditMealPlanLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal-plan/add-edit-days', event.data);
      log('Add/Edit Meal Plan Response: $response');

      if (response['status'] == true) {
        emit(AddEditMealPlanSuccess(response['data']));
      } else {
        emit(AddEditMealPlanFailed(
            response['message'] ?? 'Failed to add/edit meal plan'));
      }
    } catch (e) {
      log('Error adding/editing meal plan: $e');
      emit(AddEditMealPlanFailed(
          'Error occurred while adding/editing meal plan'));
    }
  }

  Future<void> _viewDay(ViewDayEvent event, Emitter<MealPlanState> emit) async {
    emit(ViewDayLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal-plan/view-day', {"id": event.day_id});
      log('View Day Response: $response');

      if (response['status'] == true) {
        emit(ViewDaySuccess(response['data']));
      } else {
        emit(ViewDayFailed(response['message'] ?? 'Failed to view day'));
      }
    } catch (e) {
      log('Error viewing day: $e');
      emit(ViewDayFailed('Error occurred while viewing day'));
    }
  }
}

// Events
class MealPlanEvent {}

class ListTimingEvent extends MealPlanEvent {}

class ListServingEvent extends MealPlanEvent {}

class ListCuisineEvent extends MealPlanEvent {}

class ListCuisines2 extends MealPlanEvent {}

class GetAddedTimePreferences extends MealPlanEvent {}

class ListDietaryEvent extends MealPlanEvent {}

class ListDietary2 extends MealPlanEvent {}

class ListSpiceLevelEvent extends MealPlanEvent {}

class ListDropoffOptionEvent extends MealPlanEvent {}

class ListDeliveryTimeEvent extends MealPlanEvent {}

class ListPackagingTimeEvent extends MealPlanEvent {}

class CateringTypeEvent extends MealPlanEvent {}

class FindChefEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  FindChefEvent(this.data);
}

class ListFilterdDishes extends MealPlanEvent {
  final Map<String, dynamic> data;
  ListFilterdDishes(this.data);
}

class MealPlanProgressEvent extends MealPlanEvent {
  final int mealPlanId;
  MealPlanProgressEvent(this.mealPlanId);
}

class FilterChefsEvent extends MealPlanEvent {
  final Map<String, dynamic> filter;
  FilterChefsEvent(this.filter);
}

class Step1MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step1MealPlanEvent(this.data);
}

class Step2MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step2MealPlanEvent(this.data);
}

class Step3MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step3MealPlanEvent(this.data);
}

class Step4MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step4MealPlanEvent(this.data);
}

class Step5MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step5MealPlanEvent(this.data);
}

class Step6MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step6MealPlanEvent(this.data);
}

class Step7MealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  Step7MealPlanEvent(this.data);
}

class AddTimePreferences extends MealPlanEvent {
  final Map<String, dynamic> data;
  AddTimePreferences(this.data);
}

class CheckExistingMealPlanEvent extends MealPlanEvent {}

class AddEditMealPlanEvent extends MealPlanEvent {
  final Map<String, dynamic> data;
  AddEditMealPlanEvent(this.data);
}

class ViewDayEvent extends MealPlanEvent {
  final int day_id;
  ViewDayEvent(this.day_id);
}

// States
class MealPlanState {}

class ListTimingLoading extends MealPlanState {}

class ListTimingSuccess extends MealPlanState {
  final dynamic data;

  ListTimingSuccess(this.data);
}

class ListTimingFailed extends MealPlanState {
  final String message;

  ListTimingFailed(this.message);
}

class ListServingLoading extends MealPlanState {}

class ListServingSuccess extends MealPlanState {
  final dynamic data;

  ListServingSuccess(this.data);
}

class ListServingFailed extends MealPlanState {
  final String message;

  ListServingFailed(this.message);
}

class ListCuisineLoading extends MealPlanState {}

class ListCuisineSuccess extends MealPlanState {
  final dynamic data;

  ListCuisineSuccess(this.data);
}

class ListCuisineFailed extends MealPlanState {
  final String message;

  ListCuisineFailed(this.message);
}

class ListDietaryLoading extends MealPlanState {}

class ListDietarySuccess extends MealPlanState {
  final dynamic data;

  ListDietarySuccess(this.data);
}

class ListDietaryFailed extends MealPlanState {
  final String message;

  ListDietaryFailed(this.message);
}

class ListSpiceLevelLoading extends MealPlanState {}

class ListSpiceLevelSuccess extends MealPlanState {
  final dynamic data;

  ListSpiceLevelSuccess(this.data);
}

class ListDropoffOptionLoading extends MealPlanState {}

class ListDropoffOptionSuccess extends MealPlanState {
  final dynamic data;

  ListDropoffOptionSuccess(this.data);
}

class ListDropoffOptionFailed extends MealPlanState {
  final String message;

  ListDropoffOptionFailed(this.message);
}

class ListDeliveryTimeLoading extends MealPlanState {}

class ListDeliveryTimeSuccess extends MealPlanState {
  final dynamic data;

  ListDeliveryTimeSuccess(this.data);
}

class ListDeliveryTimeFailed extends MealPlanState {
  final String message;

  ListDeliveryTimeFailed(this.message);
}

class ListSpiceLevelFailed extends MealPlanState {
  final String message;

  ListSpiceLevelFailed(this.message);
}

class MealPlanProgressLoading extends MealPlanState {}

class MealPlanProgressSuccess extends MealPlanState {
  final dynamic data;

  MealPlanProgressSuccess(this.data);
}

class MealPlanProgressFailed extends MealPlanState {
  final String message;

  MealPlanProgressFailed(this.message);
}

class FilterChefsLoading extends MealPlanState {}

class FilterChefsSuccess extends MealPlanState {
  final dynamic data;

  FilterChefsSuccess(this.data);
}

class FilterChefsFailed extends MealPlanState {
  final String message;

  FilterChefsFailed(this.message);
}

class Step1MealPlanLoading extends MealPlanState {}

class Step1MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step1MealPlanSuccess(this.data);
}

class Step1MealPlanFailed extends MealPlanState {
  final String message;

  Step1MealPlanFailed(this.message);
}

class Step2MealPlanLoading extends MealPlanState {}

class Step2MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step2MealPlanSuccess(this.data);
}

class Step2MealPlanFailed extends MealPlanState {
  final String message;

  Step2MealPlanFailed(this.message);
}

class Step3MealPlanLoading extends MealPlanState {}

class Step3MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step3MealPlanSuccess(this.data);
}

class Step3MealPlanFailed extends MealPlanState {
  final String message;

  Step3MealPlanFailed(this.message);
}

class Step4MealPlanLoading extends MealPlanState {}

class Step4MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step4MealPlanSuccess(this.data);
}

class Step4MealPlanFailed extends MealPlanState {
  final String message;

  Step4MealPlanFailed(this.message);
}

class Step5MealPlanLoading extends MealPlanState {}

class Step5MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step5MealPlanSuccess(this.data);
}

class Step5MealPlanFailed extends MealPlanState {
  final String message;

  Step5MealPlanFailed(this.message);
}

class Step6MealPlanLoading extends MealPlanState {}

class Step6MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step6MealPlanSuccess(this.data);
}

class Step6MealPlanFailed extends MealPlanState {
  final String message;

  Step6MealPlanFailed(this.message);
}

class Step7MealPlanLoading extends MealPlanState {}

class Step7MealPlanSuccess extends MealPlanState {
  final dynamic data;
  Step7MealPlanSuccess(this.data);
}

class Step7MealPlanFailed extends MealPlanState {
  final String message;

  Step7MealPlanFailed(this.message);
}

class ListFilterdDishesLoading extends MealPlanState {}

class ListFilterdDishesSuccess extends MealPlanState {
  final FilteredDishesModel data;

  ListFilterdDishesSuccess(this.data);
}

class ListFilterdDishesFailed extends MealPlanState {
  final String message;

  ListFilterdDishesFailed(this.message);
}

class AddTimePreferencesLoading extends MealPlanState {}

class AddTimePreferencesSuccess extends MealPlanState {
  final dynamic data;
  AddTimePreferencesSuccess(this.data);
}

class AddTimePreferencesFailed extends MealPlanState {
  final String message;

  AddTimePreferencesFailed(this.message);
}

class CheckExistingMealPlanLoading extends MealPlanState {}

class CheckExistingMealPlanSuccess extends MealPlanState {
  final dynamic data;
  CheckExistingMealPlanSuccess(this.data);
}

class CheckExistingMealPlanFailed extends MealPlanState {
  final String message;

  CheckExistingMealPlanFailed(this.message);
}

class ListPackagingTimeLoading extends MealPlanState {}

class ListPackagingTimeSucess extends MealPlanState {
  final dynamic data;
  ListPackagingTimeSucess(this.data);
}

class ListPackagingTimeFailed extends MealPlanState {
  final String message;

  ListPackagingTimeFailed(this.message);
}

class FindChefEventLoading extends MealPlanState {}

class FindChefEventSuccess extends MealPlanState {
  final dynamic data;
  FindChefEventSuccess(this.data);
}

class FindChefEventFailed extends MealPlanState {
  final String message;

  FindChefEventFailed(this.message);
}

class CateringTypeLoading extends MealPlanState {}

class CateringTypeSuccess extends MealPlanState {
  final dynamic data;
  CateringTypeSuccess(this.data);
}

class CateringTypeFailed extends MealPlanState {
  final String message;

  CateringTypeFailed(this.message);
}

class ListCuisines2Loading extends MealPlanState {}

class ListCuisines2Success extends MealPlanState {
  final dynamic data;
  ListCuisines2Success(this.data);
}

class ListCuisines2Failed extends MealPlanState {
  final String message;

  ListCuisines2Failed(this.message);
}

class ListDietary2Loading extends MealPlanState {}

class ListDietary2Success extends MealPlanState {
  final dynamic data;
  ListDietary2Success(this.data);
}

class ListDietary2Failed extends MealPlanState {
  final String message;

  ListDietary2Failed(this.message);
}

class GettingAddedTimePreferencesLoading extends MealPlanState {}

class GettingAddedTimePreferencesSuccess extends MealPlanState {
  final dynamic data;
  GettingAddedTimePreferencesSuccess(this.data);
}

class GettingAddedTimePreferencesFailed extends MealPlanState {
  final String message;

  GettingAddedTimePreferencesFailed(this.message);
}

class AddEditMealPlanLoading extends MealPlanState {}

class AddEditMealPlanSuccess extends MealPlanState {
  final dynamic data;
  AddEditMealPlanSuccess(this.data);
}

class AddEditMealPlanFailed extends MealPlanState {
  final String message;

  AddEditMealPlanFailed(this.message);
}

class ViewDayLoading extends MealPlanState {}

class ViewDaySuccess extends MealPlanState {
  final dynamic data;
  ViewDaySuccess(this.data);
}

class ViewDayFailed extends MealPlanState {
  final String message;

  ViewDayFailed(this.message);
}
