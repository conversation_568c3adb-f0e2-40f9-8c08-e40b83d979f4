import 'package:db_eats/ui/profile/dabbawallet.dart';
import 'package:flutter/material.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({super.key});

  @override
  State<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff6f3ec),
      appBar: AppBar(
        backgroundColor: const Color(0xfff6f3ec),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding:  EdgeInsets.symmetric(horizontal: sixteen),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             Text(
              'Payments',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: eighteen,
                height: 1.24,
                letterSpacing: -1,
                color: Colors.black,
              ),
            ),
             SizedBox(height: ten * 3.4),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(twelve),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05), // light shadow color
                    blurRadius: 6, // how soft the shadow is
                    offset: const Offset(0, 2), // vertical position
                  ),
                ],
              ),
              child: Padding(
                padding:  EdgeInsets.only(
                    left: sixteen,
                    right: sixteen,
                    top: twentyFour,
                    bottom: twentyFour),
                child: Column(
                  children: [
                    _paymentRow(
                      imagePath: 'assets/db.png',
                      title: '\$ 200.00',
                      hasExpiry: false,
                    ),
                    const Divider(color: Color(0xffE1E3E6), thickness: 1),
                    _paymentRow(
                      imagePath: 'assets/Visa.png',
                      title: 'Card ending with 0001',
                      expiry: 'Expires 03/2028',
                    ),
                    const Divider(color: Color(0xffE1E3E6), thickness: 1),
                    _paymentRow(
                      imagePath: 'assets/Visa.png',
                      title: 'Card ending with 0001x',
                      expiry: 'Expires 03/2028',
                    ),
                     SizedBox(height: twenty),

                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding:  EdgeInsets.only(
                            left: twelve), // minimal left padding
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => DabbaWalletPage()),
                            );
                          },
                          icon:  Icon(Icons.add,
                              size: eighteen, color: Colors.white),
                          label:  Text(
                            'Add Payment Method',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize: twelve,
                              height: 1,
                              letterSpacing: 0.02,
                              color: Colors.white,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(twenty),
                            ),
                            padding:  EdgeInsets.symmetric(
                                horizontal: forteen,
                                vertical:
                                    sixteen / 4), // tighter inside padding
                            tapTargetSize: MaterialTapTargetSize
                                .shrinkWrap, // reduces button size
                            visualDensity: VisualDensity
                                .compact, // further shrinks padding
                          ),
                        ),
                      ),
                    ),

                    //  const SizedBox(height: sixteen),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _paymentRow({
    required String imagePath,
    required String title,
    String? expiry,
    bool hasExpiry = true,
  }) {
    return Padding(
      padding:  EdgeInsets.symmetric(vertical: ten, horizontal: twelve),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(imagePath, width: twentyFour, height: twentyFour),
           SizedBox(width: twelve),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style:  TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: forteen,
                        height: 1.42,
                        letterSpacing: 0,
                        color: Colors.black,
                      ),
                    ),
                    if (!hasExpiry) ...[
                      SizedBox(width: sixteen / 4),
                      Container(
                        padding:  EdgeInsets.symmetric(
                            horizontal: sixteen / 4, vertical: sixteen / 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFFCEF8E0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child:  Text(
                          'Default',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: twelve,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF007A4D),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(
                  height: 2,
                ),
                if (hasExpiry && expiry != null)
                  Text(
                    expiry,
                    style:  TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: forteen,
                      height: 1.42,
                      letterSpacing: 0,
                      color: Colors.black,
                    ),
                  ),
              ],
            ),
          ),
          Image.asset('assets/more.png', width: sixteen, height: sixteen),
        ],
      ),
    );
  }
}
