// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/common/helper.dart';
import 'package:db_eats/data/models/account/listfavouritedishesmodel.dart';
import 'package:db_eats/data/models/booking/bookinglistmodel.dart';
import 'package:db_eats/data/models/booking/listdishtypemodel.dart';
import 'package:db_eats/data/models/booking/mealplandetailsmodel.dart';
import 'package:db_eats/data/models/booking/mealplanlistmodel.dart';
import 'package:db_eats/data/models/booking/vieworderdetailsmodel.dart';
import 'package:db_eats/data/models/catering/cateringacceptedlistmodel.dart';
import 'package:db_eats/data/models/catering/cateringdishdetailsmodel.dart';
import 'package:db_eats/data/models/catering/cateringdishlistmodel.dart';
import 'package:db_eats/data/models/catering/viewcateringrequestsmodel.dart';
import 'package:db_eats/data/models/data/addedtimepreferencemodel.dart';
import 'package:db_eats/data/models/data/filterdatamodel.dart';
import 'package:db_eats/data/models/data/recentpopularsearchmodel.dart';
import 'package:db_eats/data/models/data/wallet/wallettransactionlistmodel.dart';
import 'package:db_eats/data/models/data/wallet/walletwithdrawdepositmodel.dart';
import 'package:db_eats/data/models/deals/dealslistmodel.dart';
import 'package:db_eats/data/models/dish/dishdetailmodel.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/data/models/meal_plan/listdiatarymodel.dart';
import 'package:db_eats/data/models/notification/notificationlistmodel.dart';
import 'package:db_eats/data/models/settings/settinginfomodel.dart';
import 'package:db_eats/data/models/settings/settingschangemodel.dart';
import 'package:db_eats/data/models/support/issuecategorymodel.dart';
import 'package:db_eats/data/models/support/listissuesmessagesmodel.dart';
import 'package:db_eats/data/models/support/listissuesmodel.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Initializer {
  static TextEditingController username = TextEditingController();
  static TextEditingController password = TextEditingController();
  static TextEditingController email = TextEditingController();
  static String? latitude = "";
  static String? longitude = " ";
  static String? accuracy = "";
  static String date = '';
  static String time = '';
  static String? placeName = '';
  static int? cartCount = 0;

  static DealslistModel dealslistmodel = DealslistModel();
  static OngoingBookinglistModel ongoingBookinglistmodel =
      OngoingBookinglistModel();
  static Listdishtypemodel listDishTypeModel = Listdishtypemodel();
  static DishDetailModel dishDetailModel = DishDetailModel();
  static ListFavouriteDishesModel listFavouriteDishesModel =
      ListFavouriteDishesModel();
  static MealPlanListModel mealPlanListModel = MealPlanListModel();
  static ViewOrderDetailsModel viewOrderDetailsModel = ViewOrderDetailsModel();
  static MealPlanDetailsModel mealPlanDetailsModel = MealPlanDetailsModel();
  static CateringAcceptedListModel cateringAcceptedListModel =
      CateringAcceptedListModel();
  static CateringDishListModel cateringDishListModel = CateringDishListModel();
  static CateringDishDetailsModel cateringDishDetailModel =
      CateringDishDetailsModel();
  static ViewCateringRequestModel viewCateringRequestModel =
      ViewCateringRequestModel();
  static IssueCatogoryModel issueCatogoryModel = IssueCatogoryModel();
  static ListIssuesModel listIssuesModel = ListIssuesModel();
  static ListIssueMessagesModel listIssueMessagesModel =
      ListIssueMessagesModel();
  static NotificationListModel notificationListModel = NotificationListModel();
  static CuisinesListModel cuisinesListModel = CuisinesListModel();
  static DietaryListModel dietaryListModel = DietaryListModel();
  static FilterdataModel filterdataModel = FilterdataModel();
  static AddedTimePreferenceModel addedTimePreferenceModel =
      AddedTimePreferenceModel();
  static RecentPopularSearchModel recentPopularSearchModel =
      RecentPopularSearchModel();
  static SettingsInfoModel settingsInfoModel = SettingsInfoModel();
  static SettingsChangeModel settingsChangeModel = SettingsChangeModel();
  static WalletWithdrowDepositModel walletWithdrowDepositModel =
      WalletWithdrowDepositModel();
  static WalletTransactionListModel walletTransactionListModel =
      WalletTransactionListModel();

  // List to store location history
  static List<Map<String, String>> locationHistory = [];

  // Static variable to track location status
  static bool isLocationEnabled = false;

  // Check and request location permissions
  static Future<bool> checkLocationPermission(BuildContext context) async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      isLocationEnabled = false;
      await Helper.requestLocationServices(context);
      return false;
    }

    // Check location permission
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        isLocationEnabled = false;
        await Helper.requestLocationServices(context);
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      isLocationEnabled = false;
      await Helper.showLocationPermissionDeniedDialog(context);
      return false;
    }

    isLocationEnabled = true;
    return true;
  }

  // Method to start continuous location tracking and store data in the history
  static Future<void> startLocationTracking(BuildContext context) async {
    try {
      // Check and request permissions
      bool isPermissionGranted = await checkLocationPermission(context);
      if (!isPermissionGranted) {
        log('Location permission denied');
        return;
      }

      // Start tracking location continuously
      Geolocator.getPositionStream(
          locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high, // High accuracy
        distanceFilter: 10, // Minimum movement in meters before update
      )).listen((Position position) {
        // Each time we get a new position, we update location
        log('New position: ${position.latitude}, ${position.longitude}');

        // Add this position to locationHistory
        locationHistory.add({
          "latitude": position.latitude.toString(),
          "longitude": position.longitude.toString(),
          "accuracy": position.accuracy.toString(),
          "date": DateTime.now().toIso8601String(),
          "time": DateTime.now().toIso8601String().substring(11, 19),
        });
      });
    } catch (e) {
      log('Error starting location tracking: $e');
    }
  }

  // Method to stop continuous location tracking
  static Future<void> stopLocationTracking() async {
    try {
      // Logic to stop location tracking can be implemented here
      // Currently, there is no subscription to cancel but can be added if needed
      log('Location tracking stopped');
    } catch (e) {
      log('Error stopping location tracking: $e');
    }
  }

  // Method to get location history
  static List<Map<String, String>> getLocationHistory() {
    return locationHistory;
  }

  // Method to clear location history
  static void clearLocationHistory() {
    locationHistory.clear();
    log('Location history cleared');
  }

  static Future<void> saveZipCode(String zipCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('zip_code', zipCode);
  }

  static Future<String?> getZipCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('zip_code');
  }

  static Future<void> saveAddress(String address) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('address', address);
  }

  static Future<String?> getAddress() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('address');
  }

  static Future<String?> getSavedAddress() async {
    return getAddress();
  }

  Future<bool> hasAddress() async {
    final prefs = await SharedPreferences.getInstance();
    final address = prefs.getString('address');
    return address != null && address.isNotEmpty;
  }

  void setCoordinates(double lat, double long) {
    latitude = lat.toString();
    longitude = long.toString();
  }

  double? get getLatitude => latitude != null && latitude!.isNotEmpty
      ? double.tryParse(latitude!)
      : null;
  double? get getLongitude => longitude != null && longitude!.isNotEmpty
      ? double.tryParse(longitude!)
      : null;

  static Map<String, dynamic>? _appliedFilters;
  static const String _filterKey = 'applied_filters';

  static Future<void> setAppliedFilters(Map<String, dynamic>? filters) async {
    _appliedFilters = filters;
    final prefs = await SharedPreferences.getInstance();
    if (filters != null) {
      await prefs.setString(_filterKey, json.encode(filters));
    } else {
      await prefs.remove(_filterKey);
    }
  }

  static Future<Map<String, dynamic>?> getAppliedFilters() async {
    if (_appliedFilters != null) {
      return _appliedFilters;
    }

    final prefs = await SharedPreferences.getInstance();
    final String? storedFilters = prefs.getString(_filterKey);
    if (storedFilters != null) {
      _appliedFilters = json.decode(storedFilters) as Map<String, dynamic>;
      return _appliedFilters;
    }
    return null;
  }

  // Add these new static properties for dish filters
  static Map<String, dynamic>? _dishFilters;
  static Map<String, dynamic>? get dishFilters => _dishFilters;

  static Future<void> setDishFilters(Map<String, dynamic>? filters) async {
    _dishFilters = filters;
  }

  static Future<Map<String, dynamic>?> getDishFilters() async {
    return _dishFilters;
  }

  static void clearDishFilters() {
    _dishFilters = null;
  }

  // Add these new static properties
  static Map<String, dynamic> _dishFilterItems = {};
  static Map<String, dynamic> get dishFilterItems => _dishFilterItems;

  // Add these new methods
  static void setDishFilterItems(Map<String, dynamic> items) {
    _dishFilterItems = items;
  }

  static void clearDishFilterItems() {
    _dishFilterItems = {};
  }

  static bool hasDishFilters() {
    return _dishFilterItems.isNotEmpty;
  }

  void init() {
    // ...existing initialization code...
    clearDishFilterItems(); // Add this line to initialize dish filters
    clearDishFilters();
  }
}
