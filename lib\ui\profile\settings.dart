import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool pushNotifications = true;
  bool emailOffers = false;
  bool smsOffers = true;
  bool language = true;

  bool accountEmailOffers = true;
  bool accountSmsOffers = false;

  String? selectedLanguage = 'English (Default)';
  final List<String> languages = [
    'English (Default)',
    'Hindi',
    'French',
    'Spanish'
  ];
  @override
  void initState() {
    super.initState();
    context.read<MainBloc>().add(GetSettingsInfoEvent());
    _valuchabnge();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Widget _buildShimmerEffect() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: twenty),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: ListView(
          children: [
            Container(
              width: double.infinity,
              height: twentyFour,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            SizedBox(height: twenty),
            Container(
              padding: EdgeInsets.all(sixteen),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(twelve),
              ),
              child: Column(
                children: List.generate(
                  4,
                  (index) => Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: twentyFour,
                            height: twentyFour,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: twelve),
                          Container(
                            width: screenWidth * 0.4,
                            height: sixteen,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: sixteen),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf6f3ec),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf6f3ec),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<MainBloc, MainState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          if (state is GetSettingsInfoLoadingState) {
            return _buildShimmerEffect();
          }
          return Initializer.settingsInfoModel.data == null
              ? const Center(
                  child: CircularProgressIndicator(
                  color: Colors.black,
                ))
              : Initializer.settingsInfoModel.data!.pushNotification == null
                  ? const Center(
                      child: CircularProgressIndicator(
                      color: Colors.black,
                    ))
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: twenty),
                      child: ListView(
                        children: [
                          Text(
                            'Settings',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize: eighteen,
                            ),
                          ),
                          SizedBox(height: ten),
                          Container(
                            padding: EdgeInsets.only(
                                top: forteen,
                                left: sixteen / 2,
                                right: sixteen / 2,
                                bottom: forteen),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(twelve),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: Text(
                                    'Category',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                    ),
                                  ),
                                ),
                                SizedBox(height: ten * 1.5),
                                _buildToggleRow(
                                  'Push Notifications',
                                  pushNotifications,
                                  (val) {
                                    setState(() => pushNotifications = val);
                                    final data = {
                                      'push_notification': val,
                                    };
                                    context.read<MainBloc>().add(
                                          SettingsChangeEvent(data: data),
                                        );
                                  },
                                ),
                                SizedBox(height: ten),
                                _divider(),
                                SizedBox(height: twelve / 2),
                                _buildToggleRow('Email Offers', emailOffers,
                                    (val) {
                                  setState(() => emailOffers = val);
                                  final data = {'email_offers': val};
                                  context.read<MainBloc>().add(
                                        SettingsChangeEvent(data: data),
                                      );
                                }),
                                SizedBox(height: sixteen / 2),
                                _divider(),
                                SizedBox(height: twelve / 2),
                                _buildToggleRow('SMS Offers', smsOffers, (val) {
                                  setState(() => smsOffers = val);
                                  final data = {'sms_offers': val};
                                  context.read<MainBloc>().add(
                                        SettingsChangeEvent(data: data),
                                      );
                                }),
                                // const SizedBox(height: twenty),
                                SizedBox(height: sixteen / 4),
                                _divider(), SizedBox(height: ten),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: Text(
                                    'Language',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                    ),
                                  ),
                                ),
                                SizedBox(height: eighteen),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: _buildDropdown(
                                    selectedLanguage,
                                    languages,
                                    (value) {
                                      setState(() {
                                        selectedLanguage = value;
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(height: sixteen / 4),
                                // Padding(
                                //   padding: const EdgeInsets.symmetric(
                                //       horizontal: twelve),
                                //   child: const Text(
                                //     'Account Security',
                                //     style: TextStyle(
                                //       fontFamily: 'Inter',
                                //       fontWeight: FontWeight.w600,
                                //       fontSize: forteen,
                                //     ),
                                //   ),
                                // ),
                                // const SizedBox(height: eighteen),
                                // _buildToggleRow('Language', language,
                                //     (val) => setState(() => language = val)),
                                // const SizedBox(height: sixteen/4),
                                // _divider(), const SizedBox(height: ten),
                                // _buildToggleRow(
                                //     'Email Offers',
                                //     accountEmailOffers,
                                //     (val) => setState(
                                //         () => accountEmailOffers = val)),
                                // const SizedBox(height: sixteen/4),
                                // _divider(), const SizedBox(height: ten),
                                // _buildToggleRow(
                                //     'SMS Offers',
                                //     accountSmsOffers,
                                //     (val) =>
                                //         setState(() => accountSmsOffers = val)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
        },
      ),
    );
  }

  Widget _buildToggleRow(
      String label, bool value, ValueChanged<bool> onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Transform.scale(
          // scaleX: 1,
          // scaleY: 0.9,
          scale: 0.6, // Reduce size of the switch
          child: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.white, // Thumb color
            inactiveThumbColor: Colors.white, // Thumb color when off
            activeTrackColor: Colors.black, // Background when ON
            inactiveTrackColor: const Color(0xFFAAADB1), // Background when OFF
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        // SizedBox(
        //   width: ten,
        // ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: forteen,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _divider() {
    return Divider(
      color: Color(0xFFE1E3E6),
      thickness: 1,
      height: twentyFour,
    );
  }

  Widget _buildDropdown(
    // String label,
    String? value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   label,
        //   style: const TextStyle(
        //     fontFamily: 'Inter',
        //     fontWeight: FontWeight.w500,
        //     fontSize: forteen,
        //   ),
        // ),
        // const SizedBox(height: twelve),
        Container(
          height: ten * 3.6,
          padding: EdgeInsets.symmetric(horizontal: eighteen),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            border: Border.all(color: const Color(0xFFFFFFFF)),
            borderRadius: BorderRadius.circular(35),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: sixteen / 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              hint: Text(
                'Select..',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: sixteen,
                  color: Color(0xFF66696D),
                ),
              ),
              icon: const Icon(Icons.keyboard_arrow_down,
                  color: Color(0xFF1F2122)),
              dropdownColor: Colors.white,
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: forteen,
                color: Colors.black,
              ),
              items: items.map((e) {
                return DropdownMenuItem(
                  value: e,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      e,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: forteen,
                        color: Color(0xFF66696D),
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
        SizedBox(height: twenty),
      ],
    );
  }

  void _valuchabnge() {
    if (Initializer.settingsInfoModel.data != null) {
      pushNotifications =
          Initializer.settingsInfoModel.data!.pushNotification ??
              pushNotifications;
      emailOffers =
          Initializer.settingsInfoModel.data!.emailOffers ?? emailOffers;
      smsOffers = Initializer.settingsInfoModel.data!.smsOffers ?? smsOffers;
    }
  }
}
