import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

// Model classes for Order Data
class OrderItem {
  final int quantity;
  final String title;

  OrderItem({required this.quantity, required this.title});
}

class ActionData {
  final String chefName;
  final String date;
  final String message;
  final String resumeDate;

  ActionData({
    required this.chefName,
    required this.date,
    required this.message,
    required this.resumeDate,
  });
}

class OrderData {
  final String date;
  final String status;
  final String chef;
  final List<OrderItem> items;
  final double total;
  final ActionData? action; // optional

  OrderData({
    required this.date,
    required this.status,
    required this.chef,
    required this.items,
    required this.total,
    this.action,
  });
}

class OrdersPage extends StatefulWidget {
  final int id;

  const OrdersPage({super.key, required this.id});

  @override
  _OrdersPageState createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  List<OrderData> get orderData {
    if (Initializer.mealPlanDetailsModel.data?.mealPlan?.mealPlanDays == null) {
      return [];
    }

    return Initializer.mealPlanDetailsModel.data!.mealPlan!.mealPlanDays!
        .map((day) => OrderData(
              date: day.date ?? '',
              status: day.status != null
                  ? day.status![0].toUpperCase() +
                      day.status!.substring(1).toLowerCase()
                  : '____',
              chef: "${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}",
              items: day.items
                      ?.map((item) => OrderItem(
                            quantity: item.quantity ?? 0,
                            title: item.menuItem?.name ?? 'Unknown Dish',
                          ))
                      .toList() ??
                  [],
              total: double.tryParse(day.dayTotal ?? '0') ?? 0,
              action: day.timelines?.isNotEmpty == true
                  ? ActionData(
                      chefName:
                          "${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}",
                      date: day.date ?? '',
                      message: day.timelines!.last.remarks ?? '',
                      resumeDate: '',
                    )
                  : null,
            ))
        .toList();
  }

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(ViewMealPlanDetailsEvent(widget.id));
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final isLandscape = width > height;

    if (width < 360) {
      return EdgeInsets.all(width * 0.03);
    } else if (width < 600) {
      return EdgeInsets.all(width * 0.04);
    } else {
      return EdgeInsets.all(isLandscape ? width * 0.03 : width * 0.05);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is ViewMealPlanDetailsFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        if (state is ViewMealPlanDetailsLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator(color: Colors.black)),
          );
        }

        final size = MediaQuery.of(context).size;
        final isLandscape = size.width > size.height;

        final mealPlan = Initializer.mealPlanDetailsModel.data?.mealPlan;
        final title = mealPlan != null
            // ? "My Meal Plan ${_formatDateRange(mealPlan.startDate, mealPlan.endDate)}"
            // : "My Meal Plan";

            ? "${_formatFullDateRange(mealPlan.startDate, mealPlan.endDate)}"
            : "My Meal Plan";

        // Responsive sizing
        final baseTextSize = getResponsiveSize(context);
        final contentPadding = getResponsivePadding(context);
        final itemSpacing = size.height * 0.015;
        bool hasActionData = orderData.any((order) => order.action != null);
        ActionData? actionData;

        if (hasActionData) {
          // Find the first order with action data
          actionData =
              orderData.firstWhere((order) => order.action != null).action;
        }

        return Scaffold(
          backgroundColor: const Color(0xfff6f3ec),
          appBar: AppBar(
            backgroundColor: const Color(0xfff6f3ec),
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back,
                  color: Colors.black,
                  size: isLandscape ? size.height * 0.03 : baseTextSize * 1.5),
              onPressed: () => Navigator.of(context).pop(),
            ),
            scrolledUnderElevation: 0,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.04,
                vertical: size.height * 0.01,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    hasActionData
                        ? title
                        : _formatDateRange(
                            mealPlan?.startDate, mealPlan?.endDate),
                    style: TextStyle(
                      fontFamily: "Inter",
                      fontWeight: FontWeight.w600,
                      fontSize:
                          isLandscape ? size.height * 0.04 : eighteen,
                    ),
                  ),
                  SizedBox(height: itemSpacing * 1.5),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Main Orders + Chef Notification Container
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: size.width * 0.04,
                         //   vertical: size.height * 0.02,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Embedded Chef Notification Block
                              // if (hasActionData && actionData != null) ...[
                              //   Container(
                              //     decoration: BoxDecoration(
                              //       color: const Color(0xfff1f2f3),
                              //       borderRadius: BorderRadius.circular(12),
                              //     ),
                              //     padding: EdgeInsets.only(
                              //         left: itemSpacing * 1.5,
                              //         right: itemSpacing * 1.5,
                              //         top: itemSpacing * 1.5,
                              //         bottom: itemSpacing * 1),
                              //     child: Column(
                              //       crossAxisAlignment:
                              //           CrossAxisAlignment.start,
                              //       children: [
                              //         Text(
                              //           "${actionData.chefName} has notified that they will be unavailable to accommodate your meal for ${actionData.date}:",
                              //           style: const TextStyle(
                              //             fontFamily: 'Inter',
                              //             fontWeight: FontWeight.w600,
                              //             fontSize: 16,
                              //           ),
                              //         ),
                              //         SizedBox(height: itemSpacing * 1.2),
                              //         Container(
                              //           decoration: BoxDecoration(
                              //             color: const Color(0xffe1e3e6),
                              //             borderRadius:
                              //                 BorderRadius.circular(8),
                              //           ),
                              //           padding:
                              //               EdgeInsets.all(itemSpacing * 0.9),
                              //           child: Text(
                              //             '"${actionData.message}"',
                              //             style: const TextStyle(
                              //               fontFamily: 'Inter',
                              //               fontWeight: FontWeight.w400,
                              //               fontSize: 14,
                              //               color: Color(0xff414346),
                              //             ),
                              //             textAlign: TextAlign.center,
                              //           ),
                              //         ),
                              //         SizedBox(height: itemSpacing * 0.9),
                              //         Divider(
                              //           color: const Color(0xffE1E3E6),
                              //           thickness: 1,
                              //           height: itemSpacing * 2,
                              //         ),
                              //         SizedBox(height: itemSpacing * 0.6),
                              //         Text(
                              //           "Please choose how you'd like to proceed. By default, the Meal Plan will resume on the next available date on ${actionData.resumeDate}",
                              //           style: const TextStyle(
                              //             fontFamily: 'Inter',
                              //             fontWeight: FontWeight.w600,
                              //             fontSize: 16,
                              //           ),
                              //         ),
                              //         const SizedBox(height: 16),
                              //         _buildActionButton(
                              //           "Choose Another Chef",
                              //           const Color(0xff1F2122),
                              //           () {
                              //             // Handle choose another chef action
                              //           },
                              //         ),
                              //         const SizedBox(height: 0),
                              //         _buildActionButton(
                              //           "Resume On Next Date",
                              //           const Color(0xff1F2122),
                              //           () {
                              //             // Handle resume on next date action
                              //           },
                              //         ),
                              //         const SizedBox(height: 0),
                              //         _buildActionButton(
                              //           "Cancel Meal On This Day",
                              //           const Color(0xffD31510),
                              //           () {
                              //             // Handle cancel meal action
                              //           },
                              //           borderColor: const Color(0xffD31510),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                              //   SizedBox(height: itemSpacing * 0.4),
                              // ],

                              // Order List
                              ListView.separated(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: orderData.length,
                                separatorBuilder: (context, index) => Divider(
                                  color: const Color(0xffE1E3E6),
                                  thickness: 1,
                                  height:sixteen/18,
                                ),
                                itemBuilder: (context, index) {
                                  return _buildResponsiveOrderListItem(
                                    context,
                                    orderData[index],
                                    baseTextSize,
                                    itemSpacing,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          floatingActionButton: CartFloatingActionButton(
            itemCount: Initializer.cartCount ?? 0,
            onPressed: () => _openCart(context),
          ),
        );
      },
    );
  }

String formatFullDate(String date) {
  try {
    final parsedDate = DateTime.parse(date);
    final formatted = DateFormat('MMMM d, EEEE').format(parsedDate);
    return formatted;
  } catch (e) {
    return '';
  }
}
  Widget _buildResponsiveOrderListItem(
    BuildContext context,
    OrderData order,
    double baseTextSize,
    double itemSpacing,
  ) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: itemSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                     formatFullDate(order.date),
                      style: TextStyle(
                        fontFamily: "Inter",
                        fontWeight: FontWeight.w600,
                        fontSize: isLandscape
                            ? size.height * 0.025
                            : sixteen,
                      ),
                    ),
                    SizedBox(width: size.width * 0.02),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: size.width * 0.02,
                        vertical: size.height * 0.004,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xffE1E3E6),
                        borderRadius: BorderRadius.circular(size.width * 0.03),
                      ),
                      child: Text(
                        order.status,
                        style: TextStyle(
                          fontFamily: "Inter",
                          fontWeight: FontWeight.w500,
                          fontSize: twelve,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  print("Edit tapped for ${order.date}");
                },
                child: Image.asset(
                  'assets/icons/edit_3.png',
                  width: isLandscape ? size.height * 0.04 : size.width * 0.08,
                  height: isLandscape ? size.height * 0.04 : size.width * 0.08,
                ),
              ),
            ],
          ),
          SizedBox(height: itemSpacing * 0.4),
          Text(
            order.chef,
            style: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: twelve,
              color: Color(0xff414346),
            ),
          ),
          SizedBox(height: itemSpacing * 0.5),
          Row(
            children:
                _buildResponsiveItemsList(context, order.items, baseTextSize),
          ),
          SizedBox(height: itemSpacing * 0.5),
          Text(
            "Order Total: \$${order.total.toStringAsFixed(0)}",
            style: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: isLandscape ? size.height * 0.022 :forteen,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildResponsiveItemsList(
    BuildContext context,
    List<OrderItem> items,
    double baseTextSize,
  ) {
    final size = MediaQuery.of(context).size;
    List<Widget> widgets = [];

    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      widgets.add(
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.015,
            vertical: size.height * 0.002,
          ),
          decoration: BoxDecoration(
            color: const Color(0xffE1E3E6),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Text(
            "x${item.quantity}",
            style: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: twelve ,
            ),
          ),
        ),
      );

      widgets.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.01),
          child: Text(
            item.title,
            style: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: twelve,
              color: Color(0xff66696D),
            ),
          ),
        ),
      );

      if (i < items.length - 1) {
        widgets.add(
          Text(
            ",  ",
            style: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: baseTextSize,
              color: Color(0xff66696D),
            ),
          ),
        );
      }
    }

    return widgets;
  }

  void _openCart(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CartPage(),
      ),
    );
  }

  String _formatDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return '';
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      return '${start.day} to ${end.day} ${_getMonthName(end.month)}';
    } catch (e) {
      return '';
    }
  }


}

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }
String _formatFullDateRange(String? startDate, String? endDate) {
  if (startDate == null || endDate == null) return '';
  try {
    final start = DateTime.parse(startDate);
    final end = DateTime.parse(endDate);
    final startMonth = _getMonthName(start.month);
    final endMonth = _getMonthName(end.month);

    if (startMonth == endMonth) {
      return '$startMonth ${start.day} to ${end.day}';
    } else {
      return '$startMonth ${start.day} to $endMonth ${end.day}';
    }
  } catch (e) {
    return '';
  }
}

Widget _buildActionButton(String text, Color textColor, VoidCallback onPressed,
    {Color? borderColor}) {
  return SizedBox(
    width: double.infinity,
    child: ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        foregroundColor: textColor,
        backgroundColor: Color(0xfff1f2f3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(40),
          side: BorderSide(
            color: borderColor ?? textColor,
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 4),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w600,
          fontSize: 16,
          color: textColor,
        ),
      ),
    ),
  );
}
