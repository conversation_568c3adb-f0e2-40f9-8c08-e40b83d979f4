class MealPlanProgressModel {
  bool? status;
  Data? data;
  int? statusCode;

  MealPlanProgressModel({this.status, this.data, this.statusCode});

  MealPlanProgressModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = this.statusCode;
    return data;
  }
}

class Data {
  int? id;
  int? stepProgress;
  String? createdAt;
  int? customerId;
  int? mealPlanDuration;
  String? endDate;
  String? startDate;
  int? timeSlotId;
  int? servingSizeId;
  int? dishesPerDay;
  String? mealSelectionType;
  int? dietaryPreferenceId;
  int? spiceLevelId;
  dynamic dropOffOptionId;
  dynamic dropOffInstructions;
  dynamic deliveryTimeId;
  String? subtotal;
  String? deliveryFee;
  String? discount;
  String? walletCredits;
  String? taxesAndFees;
  String? total;
  String? status;
  int? totalDiscount;
  TimeSlot? timeSlot;
  ServingSize? servingSize;
  DietaryPreference? dietaryPreference;
  DietaryPreference? spiceLevel;
  List<Cuisine>? cuisines;
  List<Subcuisine>? subcuisines;
  List<Localcuisine>? localcuisines;
  List<MealPlanDay>? mealPlanDays;
  List<MealPlanDay>? personalizedDays;

  Data({
    this.id,
    this.stepProgress,
    this.createdAt,
    this.customerId,
    this.mealPlanDuration,
    this.endDate,
    this.startDate,
    this.timeSlotId,
    this.servingSizeId,
    this.dishesPerDay,
    this.mealSelectionType,
    this.dietaryPreferenceId,
    this.spiceLevelId,
    this.dropOffOptionId,
    this.dropOffInstructions,
    this.deliveryTimeId,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.walletCredits,
    this.taxesAndFees,
    this.total,
    this.status,
    this.totalDiscount,
    this.timeSlot,
    this.servingSize,
    this.dietaryPreference,
    this.spiceLevel,
    this.cuisines,
    this.subcuisines,
    this.localcuisines,
    this.mealPlanDays,
    this.personalizedDays,
  });

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stepProgress = json['step_progress'];
    createdAt = json['created_at'];
    customerId = json['customer_id'];
    mealPlanDuration = json['meal_plan_duration'];
    endDate = json['end_date'];
    startDate = json['start_date'];
    timeSlotId = json['time_slot_id'];
    servingSizeId = json['serving_size_id'];
    dishesPerDay = json['dishes_per_day'];
    mealSelectionType = json['meal_selection_type'];
    dietaryPreferenceId = json['dietary_preference_id'];
    spiceLevelId = json['spice_level_id'];
    dropOffOptionId = json['drop_off_option_id'];
    dropOffInstructions = json['drop_off_instructions'];
    deliveryTimeId = json['delivery_time_id'];
    subtotal = json['subtotal']?.toString();
    deliveryFee = json['delivery_fee']?.toString();
    discount = json['discount']?.toString();
    walletCredits = json['wallet_credits']?.toString();
    taxesAndFees = json['taxes_and_fees']?.toString();
    total = json['total']?.toString();
    status = json['status'];
    totalDiscount =
        json['total_discount'] != null ? json['total_discount'].toInt() : null;
    timeSlot =
        json['timeSlot'] != null ? TimeSlot.fromJson(json['timeSlot']) : null;
    servingSize = json['servingSize'] != null
        ? ServingSize.fromJson(json['servingSize'])
        : null;
    dietaryPreference = json['dietaryPreference'] != null
        ? DietaryPreference.fromJson(json['dietaryPreference'])
        : null;
    spiceLevel = json['spiceLevel'] != null
        ? DietaryPreference.fromJson(json['spiceLevel'])
        : null;

    if (json['cuisines'] != null) {
      cuisines = <Cuisine>[];
      json['cuisines'].forEach((v) {
        cuisines!.add(Cuisine.fromJson(v));
      });
    }

    if (json['subcuisines'] != null) {
      subcuisines = <Subcuisine>[];
      json['subcuisines'].forEach((v) {
        subcuisines!.add(Subcuisine.fromJson(v));
      });
    }

    if (json['localcuisines'] != null) {
      localcuisines = <Localcuisine>[];
      json['localcuisines'].forEach((v) {
        localcuisines!.add(Localcuisine.fromJson(v));
      });
    }

    if (json['meal_plan_days'] != null) {
      mealPlanDays = <MealPlanDay>[];
      json['meal_plan_days'].forEach((v) {
        mealPlanDays!.add(MealPlanDay.fromJson(v));
      });
    }

    if (json['personalized_days'] != null) {
      personalizedDays = <MealPlanDay>[];
      json['personalized_days'].forEach((v) {
        personalizedDays!.add(MealPlanDay.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['step_progress'] = this.stepProgress;
    data['created_at'] = this.createdAt;
    data['customer_id'] = this.customerId;
    data['meal_plan_duration'] = this.mealPlanDuration;
    data['end_date'] = this.endDate;
    data['start_date'] = this.startDate;
    data['time_slot_id'] = this.timeSlotId;
    data['serving_size_id'] = this.servingSizeId;
    data['dishes_per_day'] = this.dishesPerDay;
    data['meal_selection_type'] = this.mealSelectionType;
    data['dietary_preference_id'] = this.dietaryPreferenceId;
    data['spice_level_id'] = this.spiceLevelId;
    data['drop_off_option_id'] = this.dropOffOptionId;
    data['drop_off_instructions'] = this.dropOffInstructions;
    data['delivery_time_id'] = this.deliveryTimeId;
    data['subtotal'] = this.subtotal;
    data['delivery_fee'] = this.deliveryFee;
    data['discount'] = this.discount;
    data['wallet_credits'] = this.walletCredits;
    data['taxes_and_fees'] = this.taxesAndFees;
    data['total'] = this.total;
    data['status'] = this.status;
    data['total_discount'] = this.totalDiscount;

    if (this.timeSlot != null) {
      data['timeSlot'] = this.timeSlot!.toJson();
    }
    if (this.servingSize != null) {
      data['servingSize'] = this.servingSize!.toJson();
    }
    if (this.dietaryPreference != null) {
      data['dietaryPreference'] = this.dietaryPreference!.toJson();
    }
    if (this.spiceLevel != null) {
      data['spiceLevel'] = this.spiceLevel!.toJson();
    }
    if (this.cuisines != null) {
      data['cuisines'] = this.cuisines!.map((v) => v.toJson()).toList();
    }
    if (this.subcuisines != null) {
      data['subcuisines'] = this.subcuisines!.map((v) => v.toJson()).toList();
    }
    if (this.localcuisines != null) {
      data['localcuisines'] =
          this.localcuisines!.map((v) => v.toJson()).toList();
    }
    if (this.mealPlanDays != null) {
      data['meal_plan_days'] =
          this.mealPlanDays!.map((v) => v.toJson()).toList();
    }
    if (this.personalizedDays != null) {
      data['personalized_days'] =
          this.personalizedDays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MealPlanDay {
  int? id;
  String? date;
  String? dayOfWeek;
  Chef? chef;
  String? dayTotal;
  int? dayNumber;
  num? discount;
  List<MealItem>? items;

  MealPlanDay({
    this.id,
    this.date,
    this.dayOfWeek,
    this.chef,
    this.dayTotal,
    this.dayNumber,
    this.discount,
    this.items,
  });

  MealPlanDay.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['date'];
    dayOfWeek = json['day_of_week'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    dayTotal = json['day_total'];
    dayNumber = json['day_number'];
    discount = json['discount'];

    if (json['items'] != null) {
      items = <MealItem>[];
      json['items'].forEach((v) {
        items!.add(MealItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['date'] = this.date;
    data['day_of_week'] = this.dayOfWeek;
    if (this.chef != null) {
      data['chef'] = this.chef!.toJson();
    }
    data['day_total'] = this.dayTotal;
    data['day_number'] = this.dayNumber;
    data['discount'] = this.discount;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chef {
  int? id;
  String? firstName;
  String? lastName;
  String? profilePhoto;
  List<String>? searchTags;

  Chef({
    this.id,
    this.firstName,
    this.lastName,
    this.profilePhoto,
    this.searchTags,
  });

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profilePhoto = json['profile_photo'];
    searchTags = json['search_tags']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['profile_photo'] = this.profilePhoto;
    data['search_tags'] = this.searchTags;
    return data;
  }
}

class MealItem {
  int? id;
  int? chefMenuItemId;
  int? quantity;
  String? price;
  MenuItem? menuItem;

  MealItem({
    this.id,
    this.chefMenuItemId,
    this.quantity,
    this.price,
    this.menuItem,
  });

  MealItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chefMenuItemId = json['chef_menu_item_id'];
    quantity = json['quantity'];
    price = json['price'];
    menuItem =
        json['menu_item'] != null ? MenuItem.fromJson(json['menu_item']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['chef_menu_item_id'] = this.chefMenuItemId;
    data['quantity'] = this.quantity;
    data['price'] = this.price;
    if (this.menuItem != null) {
      data['menu_item'] = this.menuItem!.toJson();
    }
    return data;
  }
}

class MenuItem {
  int? id;
  String? name;
  String? photo;

  MenuItem({this.id, this.name, this.photo});

  MenuItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['serves'] = this.serves;
    return data;
  }
}

class DietaryPreference {
  int? id;
  String? name;

  DietaryPreference({this.id, this.name});

  DietaryPreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class Cuisine {
  int? id;
  String? name;

  Cuisine({this.id, this.name});

  Cuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class Subcuisine {
  int? id;
  String? name;

  Subcuisine({this.id, this.name});

  Subcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class Localcuisine {
  int? id;
  String? name;

  Localcuisine({this.id, this.name});

  Localcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
