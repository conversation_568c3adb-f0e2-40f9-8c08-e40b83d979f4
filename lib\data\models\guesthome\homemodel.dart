typedef TopRatedChefs = ChefData;
typedef PopularChefsNear = ChefData;

class HomeModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  HomeModel({this.status, this.message, this.statusCode, this.data});

  HomeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
}

class Data {
  List<Deal>? deals;
  List<ChefData>? recommendedChefs;
  List<ChefData>? topRatedChefs;
  List<ChefData>? popularChefsNear;

  Data({
    this.deals,
    this.recommendedChefs,
    this.topRatedChefs,
    this.popularChefsNear,
  });

  Data.fromJson(Map<String, dynamic> json) {
    if (json['deals'] != null) {
      deals = <Deal>[];
      json['deals'].forEach((v) {
        deals!.add(Deal.fromJson(v));
      });
    }
    if (json['recommended_chefs'] != null) {
      recommendedChefs = <ChefData>[];
      json['recommended_chefs'].forEach((v) {
        recommendedChefs!.add(ChefData.fromJson(v));
      });
    }
    if (json['top_rated_chefs'] != null) {
      topRatedChefs = <ChefData>[];
      json['top_rated_chefs'].forEach((v) {
        topRatedChefs!.add(ChefData.fromJson(v));
      });
    }
    if (json['popular_chefs_near'] != null) {
      popularChefsNear = <ChefData>[];
      json['popular_chefs_near'].forEach((v) {
        popularChefsNear!.add(ChefData.fromJson(v));
      });
    }
  }
}

class ChefData {
  int? chefId;
  String? profilePhoto;
  String? coverPhoto;
  Location? location;
  List<String>? searchTags;
  double? distance;
  Chef? chef;
  int? ratingPercentage;
  String? averageRating;
  int? totalRatings;

  ChefData({
    this.chefId,
    this.profilePhoto,
    this.coverPhoto,
    this.location,
    this.searchTags,
    this.distance,
    this.chef,
    this.ratingPercentage,
    this.averageRating,
    this.totalRatings,
  });

  ChefData.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    searchTags = json['search_tags']?.cast<String>();
    distance = json['distance']?.toDouble();
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    ratingPercentage = json['rating_percentage'];
    averageRating = json['average_rating'];
    totalRatings = json['total_ratings'];
  }
}

class Deal {
  int? id;
  int? dealType;
  String? title;
  String? description;
  String? discountPercentage;
  String? discountAmount;
  String? minimumSpendAmount;
  String? startDate;
  String? endDate;
  String? status;
  DealChef? chef;

  Deal({
    this.id,
    this.dealType,
    this.title,
    this.description,
    this.discountPercentage,
    this.discountAmount,
    this.minimumSpendAmount,
    this.startDate,
    this.endDate,
    this.status,
    this.chef,
  });

  Deal.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    dealType = json['deal_type'];
    title = json['title'];
    description = json['description'];
    discountPercentage = json['discount_percentage'];
    discountAmount = json['discount_amount'];
    minimumSpendAmount = json['minimum_spend_amount'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    chef = json['chef'] != null ? DealChef.fromJson(json['chef']) : null;
  }
}

class DealChef {
  String? firstName;
  String? lastName;
  DealChefProfile? profile;

  DealChef({
    this.firstName,
    this.lastName,
    this.profile,
  });

  DealChef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    profile = json['profile'] != null
        ? DealChefProfile.fromJson(json['profile'])
        : null;
  }
}

class DealChefProfile {
  String? profilePhoto;
  String? coverPhoto;

  DealChefProfile({
    this.profilePhoto,
    this.coverPhoto,
  });

  DealChefProfile.fromJson(Map<String, dynamic> json) {
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates']?.cast<double>();
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }
}

class Chef {
  String? firstName;
  String? lastName;
  List<OperationDay>? operationDays;

  Chef({this.firstName, this.lastName, this.operationDays});

  Chef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    if (json['operation_days'] != null) {
      operationDays = <OperationDay>[];
      json['operation_days'].forEach((v) {
        operationDays!.add(OperationDay.fromJson(v));
      });
    }
  }
}

class OperationDay {
  int? dayId;
  Day? day;

  OperationDay({this.dayId, this.day});

  OperationDay.fromJson(Map<String, dynamic> json) {
    dayId = json['day_id'];
    day = json['day'] != null ? Day.fromJson(json['day']) : null;
  }
}

class Day {
  String? name;

  Day({this.name});

  Day.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }
}
