import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/cateringtypemodel.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/ui/catering/customdropdown.dart';
import 'package:db_eats/data/models/meal_plan/listdiatarymodel.dart';
import 'package:db_eats/data/models/meal_plan/spicelevellistmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/typeofpackagingmodel.dart';
import 'package:db_eats/ui/catering/selectchef.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class EditCateringRequestPage extends StatefulWidget {
  final int? chefid;
  final Map<String, dynamic>? cateringData; // Add this parameter

  const EditCateringRequestPage({
    super.key,
    this.chefid,
    this.cateringData, // Add this
  });

  @override
  State<EditCateringRequestPage> createState() =>
      _EditCateringRequestPageState();
}

class _EditCateringRequestPageState extends State<EditCateringRequestPage> {
  String dropOffOption = 'Meet at my door';
  // int? selectedCuisine;
  int peopleCount = 5;
  DateTime? selectedDate;

  String? selectedState;
  String? selectedCity;
  int? selectedPackaging;

  List<AddressData>? _savedAddresses;
  AddressData? _currentAddressData;
  List<String> cateringtypes = [];
  List<CateringTypes> cateringTypeList = [];
  String? selectedCateringType;
  int? selectedCateringTypeId;

  String? cuisineError;
  String? peopleCountError;
  String? dateError;
  String? timeError;
  String? streetError;
  String? aptError;
  String? zipError;
  String? stateError;
  String? cityError;
  String? cateringError;
  String? packagingError;
  String? dietaryError;
  String? spiceLevelError;
// No error for allergy (non-mandatory)

  final streetController = TextEditingController();
  final aptController = TextEditingController();
  final zipController = TextEditingController();
  final allergyController = TextEditingController();
  final cityController = TextEditingController();
  int _currentIndex = 2;

  late PageController _pageController;
  void _onTabTapped(int index) {
    _pageController.jumpToPage(index);
    setState(() => _currentIndex = index);
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);

    // Populate fields if cateringData exists
    if (widget.cateringData != null) {
      final data = widget.cateringData!;

      // Simple fields
      peopleCount = data['people_count'] ?? 5;
      streetController.text = data['address'] ?? '';
      selectedState = data['state'];
      cityController.text = data['city'] ?? '';
      zipController.text = data['zip_code'] ?? '';
      allergyController.text = data['allergy_prference_text'] ?? '';

      // Date field
      if (data['date'] != null) {
        selectedDate = DateTime.parse(data['date']);
      }
    }

    // Dispatch events to fetch data
    context.read<MealplanBloc>().add(ListCuisineEvent());
    context.read<MealplanBloc>().add(ListTimingEvent());
    context.read<MealplanBloc>().add(ListPackagingTimeEvent());
    context.read<MealplanBloc>().add(ListDietaryEvent());
    context.read<MealplanBloc>().add(ListSpiceLevelEvent());
    context.read<MealplanBloc>().add(CateringTypeEvent());
    _loadAddresses();
  }

   late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Future<void> _loadAddresses() async {
    // setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load addresses: $e')),
      );
    }
    // finally {
    //   setState(() => _loadingAddresses = false);
    // }
  }

  // Initialize lists for dropdowns
  List<Cuisines> cuisines = []; // filled from API
  List<String> cuisineNames = []; // extracted from cuisines
  String? selectedCuisine;
  int? selectedCuisineId;
  Map<String, List<int>> selectedCuisineData = {
    'cuisine_ids': [],
    'sub_cuisine_ids': [],
    'local_cuisine_ids': [],
  };

  List<Timings> times = [];
  List<String> timeLabels = [];
  List<String> packagingTypes = [];
  List<PackagingTypes> packagingTypeList = [];
  List<Map<String, dynamic>> dietaryPreferences = [];

  double? _currentLatitude;
  double? _currentLongitude;

  List<Dietaries> dietList = [];
  List<SpiceLevels> spiceLevels = [];
  String? selectedTime;
  int? selectedTimeId;
  String? selectedPackagingType;
  int? selectedPackagingTypeId;
  List<Map<String, dynamic>> spiceLevelsList = [];
  String? _currentAddress;

  int? _selectedSpiceLevelId;
  String? _selectedSpiceLevelName; // optional if you need the name too

  // Static list of US states
  final List<String> usStates = [
    'Alabama',
    'Alaska',
    'Arizona',
    'Arkansas',
    'California',
    'Kerala',
    'Colorado',
    'Connecticut',
    'Delaware',
    'Florida',
    'Georgia',
    'Hawaii',
    'Idaho',
    'Illinois',
    'Indiana',
    'Iowa',
    'Kansas',
    'Kentucky',
    'Louisiana',
    'Maine',
    'Maryland',
    'Massachusetts',
    'Michigan',
    'Minnesota',
    'Mississippi',
    'Missouri',
    'Montana',
    'Nebraska',
    'Nevada',
    'New Hampshire',
    'New Jersey',
    'New Mexico',
    'New York',
    'North Carolina',
    'North Dakota',
    'Ohio',
    'Oklahoma',
    'Oregon',
    'Pennsylvania',
    'Rhode Island',
    'South Carolina',
    'South Dakota',
    'Tennessee',
    'Texas',
    'Utah',
    'Vermont',
    'Virginia',
    'Washington',
    'West Virginia',
    'Wisconsin',
    'Wyoming',
  ];

  final List<Map<String, String>> _preferences = [
    {
      'name': 'Organic',
      'icon': 'assets/icons/organic.png',
    },
    {
      'name': 'Halal',
      'icon': 'assets/icons/halal.png',
    },
    {
      'name': 'Vegan',
      'icon': 'assets/icons/vegan.png',
    },
    {
      'name': 'Vegetarian',
      'icon': 'assets/icons/veg.png',
    },
  ];
  final List<int> _selectedPreferences = [];
  final List<String> _selectedSpiceLevel = [];

  @override
  void dispose() {
    streetController.dispose();
    aptController.dispose();
    zipController.dispose();
    allergyController.dispose();
    cityController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildDropdownShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(35),
        ),
      ),
    );
  }

  Widget _buildListItemShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: const EdgeInsets.only(bottom: 14),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(width: 20, height: 20, color: Colors.white),
            const SizedBox(width: 19),
            Container(width: 100, height: 14, color: Colors.white),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdown(
    String label,
    String? value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 18),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            border: Border.all(color: const Color(0xFFFFFFFF)),
            borderRadius: BorderRadius.circular(35),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              hint: Text(
                label == 'State'
                    ? 'Select state'
                    : label == 'City'
                        ? 'Select city'
                        : label == 'ZIP Code'
                            ? 'Enter zip code'
                            : 'Select...',
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
              icon: const Icon(Icons.keyboard_arrow_down,
                  color: Color(0xFF1F2122)),
              dropdownColor: Colors.white,
              style: const TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: 14,
                color: Colors.black,
              ),
              items: items.map((e) {
                return DropdownMenuItem<String>(
                  value: e,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      e,
                      style: const TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: Colors.black,
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
        //     const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildCounter() {
    bool canDecrease = peopleCount > 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          'Indicate Number of People',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          fontSize: forteen,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Decrement Button
            Container(
            width: sixteen*2,
              height: sixteen*2,
              decoration: BoxDecoration(
                color: canDecrease ? Colors.white : const Color(0xFFdcdee1),
                shape: BoxShape.circle,
                boxShadow: canDecrease
                    ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        )
                      ]
                    : [],
              ),
              child: IconButton(
                icon: const Icon(Icons.remove),
                color: Colors.black,
                    iconSize: sixteen,
                padding: EdgeInsets.zero,
               constraints:  BoxConstraints(minWidth: sixteen*2, minHeight:  sixteen*2),
                onPressed:
                    canDecrease ? () => setState(() => peopleCount--) : null,
              ),
            ),
            const SizedBox(width: 12),

            // Number Display
            Container(
                padding:  EdgeInsets.symmetric(horizontal: twentyFour, vertical: twelve/2),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(32),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ],
              ),
              child: Text(
                '$peopleCount',
                style: TextStyle(fontSize: sixteen),
              ),
            ),
              SizedBox(width:twelve),

            // Increment Button
            Container(
            width:  sixteen*2,
              height:  sixteen*2,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.add),
                color: Colors.black,
                 iconSize: sixteen,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                onPressed: () => setState(() => peopleCount++),
              ),
            ),
          ],
        ),
         SizedBox(height: twenty),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          'Select Date',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
      fontSize: forteen,
          ),
        ),
            SizedBox(height: twelve),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate:
                  selectedDate ?? DateTime.now().add(Duration(days: 1)),
              firstDate: DateTime.now().add(Duration(days: 1)),
              lastDate: DateTime(2100),
              builder: (context, child) {
                return Theme(
                  data: ThemeData.light().copyWith(
                    primaryColor: Color.fromARGB(255, 255, 179, 0),
                    colorScheme: const ColorScheme.light(
                      primary: Color.fromARGB(255, 255, 179, 0),
                    ),
                    dialogTheme: DialogThemeData(backgroundColor: Colors.white),
                  ),
                  child: child!,
                );
              },
            );
            if (date != null) {
              setState(() {
                selectedDate = date;
              });
            }
          },
          child: Container(
              padding:  EdgeInsets.symmetric(horizontal: sixteen, vertical: ten),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE1E3E6)),
              borderRadius: BorderRadius.circular(35),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                 blurRadius: sixteen/4,
                  offset: const Offset(0, 2),
                )
              ],
            ),
            child:Row(
  mainAxisSize: MainAxisSize.min,
  crossAxisAlignment: CrossAxisAlignment.baseline,
  textBaseline: TextBaseline.alphabetic, // Required when using baseline
  children: [
    Baseline(
      baseline: eighteen, // same as your text size
      baselineType: TextBaseline.alphabetic,
      child: Text(
        selectedDate == null
            ? 'Pick Date'
            : DateFormat('E, MMM d').format(selectedDate!),
        style: TextStyle(
          fontFamily: 'Inter',
          fontSize: sixteen,
          color: const Color(0xFF66696D),
        ),
      ),
    ),
    SizedBox(width: sixteen / 2),
    Baseline(
      baseline: twenty, // match this to align with text
      baselineType: TextBaseline.alphabetic,
      child: Image.asset(
        'assets/icons/calender.png',
        width: twenty,
        height: twenty,
      ),
    ),
  ],
)

          ),
        ),

        /// const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTextField(String hintText,
      {TextEditingController? controller, int maxLines = 1}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      padding:  EdgeInsets.symmetric(horizontal: eighteen),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE1E3E6)),
        borderRadius: BorderRadius.circular(maxLines > 2 ? 10 : 35),
      ),
      child: SizedBox(
        height: maxLines == 1 ? 40 : null,
        child: TextField(
          controller: controller,
          maxLines: maxLines,
            style: TextStyle(fontSize: sixteen, height: 1.3),
          decoration: InputDecoration(
            isDense: true,
                 contentPadding:  EdgeInsets.symmetric(vertical: ten),
            hintText: hintText,
            border: InputBorder.none,
            hintStyle:  TextStyle(
              fontFamily: 'Inter',
               fontSize: sixteen,
              height: 1.3,
              color: Color(0xFFAAADB1),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpiceIcons(int count) {
    return Row(
      children: List.generate(
          count,
          (index) => Padding(
                padding: const EdgeInsets.only(left: 4),
                child: Image.asset(
                  'assets/icons/spice.png',
                  width: eighteen,
                  height: eighteen,
                ),
              )),
    );
  }

  Future<void> _showLocationModalIfNeeded() async {
    if (_savedAddresses?.isEmpty ?? true) {
      _currentAddress = await Initializer.getAddress();
      if ((_currentAddress?.isEmpty ?? true) && mounted) {
        showDialog(
          context: context,
          builder: (_) => const ChefLocationModal(),
        );
      }
    }
  }

  void _submit() {
    // Log all fields
    // debugPrint("Cuisine: $selectedCuisineId");
    debugPrint("Cuisine: $selectedCuisineData");
    //debugPrint("Drop-Off Option: $dropOffOption");
    debugPrint("People Count: $peopleCount");
    debugPrint("Selected Date: ${selectedDate?.toIso8601String()}");
    debugPrint("Delivery Time: $selectedTimeId");
    debugPrint("Street: ${streetController.text}");
    debugPrint("Apartment: ${aptController.text}");
    debugPrint("Zip: ${zipController.text}");
    debugPrint("State: $selectedState");
    debugPrint("City: ${cityController.text}");
    debugPrint("Packaging: $selectedPackagingTypeId");
    debugPrint("Dietary Preferences: $_selectedPreferences");
    debugPrint("Spice Level: $_selectedSpiceLevelId");
    debugPrint("Allergies: ${allergyController.text}");
    debugPrint("lat: $_currentLatitude");
    debugPrint("long: $_currentLongitude");
    debugPrint("catering type: $selectedCateringType");

    // Validate all fields
    bool isValid = true;
    String errorMessage = '';

    bool hasCuisineSelection = selectedCuisineData['cuisine_ids']!.isNotEmpty ||
        selectedCuisineData['sub_cuisine_ids']!.isNotEmpty ||
        selectedCuisineData['local_cuisine_ids']!.isNotEmpty;

    if (!hasCuisineSelection) {
      isValid = false;
      errorMessage += 'Cuisine selection is missing.\n';
    }
    // if (dropOffOption == null || dropOffOption!.isEmpty) {
    //   isValid = false;
    //   errorMessage += 'Drop-Off Option is missing.\n';
    // }
    if (peopleCount <= 0) {
      isValid = false;
      errorMessage += 'People Count is invalid.\n';
    }
    if (selectedDate == null) {
      isValid = false;
      errorMessage += 'Selected Date is missing.\n';
    }
    if (selectedTimeId == null) {
      isValid = false;
      errorMessage += 'Delivery Time is missing.\n';
    }
    if (streetController.text.isEmpty) {
      isValid = false;
      errorMessage += 'Street is missing.\n';
    }
    if (selectedCateringTypeId == null) {
      isValid = false;
      errorMessage += 'Catering is missing.\n';
    }

    if (zipController.text.isEmpty) {
      isValid = false;
      errorMessage += 'Zip is missing.\n';
    }
    if (selectedState == null || selectedState!.isEmpty) {
      isValid = false;
      errorMessage += 'State is missing.\n';
    }
    if (cityController.text.isEmpty) {
      isValid = false;
      errorMessage += 'City is missing.\n';
    }
    if (selectedPackagingTypeId == null) {
      isValid = false;
      errorMessage += 'Packaging is missing.\n';
    }
    if ((_selectedPreferences.isEmpty)) {
      isValid = false;
      errorMessage += 'Dietary Preferences are missing.\n';
    }
    if (_selectedSpiceLevelId == null) {
      isValid = false;
      errorMessage += 'Spice Level is missing.\n';
    }

    setState(() {
      cuisineError = selectedCuisineData['cuisine_ids']!.isEmpty &&
              selectedCuisineData['sub_cuisine_ids']!.isEmpty &&
              selectedCuisineData['local_cuisine_ids']!.isEmpty
          ? 'Please select at least one cuisine'
          : null;

      peopleCountError = peopleCount <= 0 ? 'Enter number of people' : null;
      dateError = selectedDate == null ? 'Select a date' : null;
      timeError = selectedTimeId == null ? 'Select a delivery time' : null;
      streetError = streetController.text.isEmpty ? 'Enter street' : null;
      cateringError = selectedCateringTypeId == null ? 'Select Catering' : null;
      zipError = zipController.text.isEmpty ? 'Enter ZIP code' : null;
      stateError = selectedState == null || selectedState!.isEmpty
          ? 'Select state'
          : null;
      cityError = cityController.text.isEmpty ? 'Enter city' : null;
      packagingError =
          selectedPackagingTypeId == null ? 'Select packaging' : null;
      dietaryError =
          _selectedPreferences.isEmpty ? 'Select dietary preference' : null;
      spiceLevelError =
          _selectedSpiceLevelId == null ? 'Select spice level' : null;
    });

    // Log validation result
    if (isValid) {
      String formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate!);
      int firstPreferenceAlt = _selectedPreferences[0];
      debugPrint(
          "All fields are valid. Dispatching FindChefEvent and navigating to CateringRequestsPage.");
      BlocProvider.of<MealplanBloc>(context).add(
        FindChefEvent({
          "packaging_type_id": selectedPackagingTypeId,
          "time_slot_id": selectedTimeId,
          "date": formattedDate,
          "cuisine_ids": selectedCuisineData['cuisine_ids'],
          "sub_cuisine_ids": selectedCuisineData['sub_cuisine_ids'],
          "local_cuisine_ids": selectedCuisineData['local_cuisine_ids'],
          "dietary_preference_id": firstPreferenceAlt,
          "spice_level_id": _selectedSpiceLevelId,
          "latitude": _currentLatitude,
          "longitude": _currentLongitude,
          "catering_type_id": selectedCateringTypeId,
        }),
      );
      debugPrint("All fields are valid. Navigating to CateringRequestsPage.");
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => CateringRequestsPage(),
      //   ),
      // );
    } else {
      Fluttertoast.showToast(
        msg: "Please fill in all required fields.",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      debugPrint("Validation failed:\n$errorMessage");
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MealplanBloc>.value(
          value: context.read<MealplanBloc>(),
        ),
        BlocProvider<AccountBloc>.value(
          value: context.read<AccountBloc>(),
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFf6f3ec),
        appBar: AppBar(
          backgroundColor: const Color(0xFFf6f3ec),
          elevation: 0,
          scrolledUnderElevation: 0,
          automaticallyImplyLeading: false,
          title:  Text('Edit catering request',
              style: TextStyle(
                  fontFamily: 'Inter',
                   fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  color: Color(0xFF1F2122))),
        ),
        body: BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                _savedAddresses = state.data;
                _currentAddressData = _savedAddresses?.firstWhere(
                  (address) => address.isCurrent == true,
                  orElse: () => _savedAddresses?.isNotEmpty == true
                      ? _savedAddresses!.first
                      : AddressData(),
                );
              });
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showLocationModalIfNeeded();
              });
              if (_currentAddressData?.location?.coordinates != null) {
                final lat = _currentAddressData!.location!.coordinates![1];
                final lng = _currentAddressData!.location!.coordinates![0];

                setState(() {
                  _currentLatitude = lat;
                  _currentLongitude = lng;
                });

                Initializer().setCoordinates(lat, lng);
                context.read<HomeBloc>().add(
                      GetHomeDataEvent(
                        data: {
                          'latitude': lat,
                          'longitude': lng,
                        },
                      ),
                    );
              }
            }
          },
          child: BlocListener<MealplanBloc, MealPlanState>(
            listener: (context, state) {
              // Handle Cuisine States
              if (state is ListCuisineSuccess) {
                setState(() {
                  cuisines = state.data.data?.cuisines ?? [];
                  cuisineNames = cuisines.map((c) => c.name ?? '').toList();
                });

                // Set cuisine selections
                if (widget.cateringData != null) {
                  setState(() {
                    selectedCuisineData = {
                      'cuisine_ids': List<int>.from(
                          widget.cateringData!['cuisine_ids'] ?? []),
                      'sub_cuisine_ids': List<int>.from(
                          widget.cateringData!['sub_cuisine_ids'] ?? []),
                      'local_cuisine_ids': List<int>.from(
                          widget.cateringData!['local_cuisine_ids'] ?? []),
                    };
                  });
                }
              }
              if (state is FindChefEventSuccess) {
                String formattedDate =
                    DateFormat('yyyy-MM-dd').format(selectedDate!);
                int firstPreferenceAlt = _selectedPreferences[0];
                Map<String, dynamic> userData = {
                  "packaging_type_id": selectedPackagingTypeId,
                  "time_slot_id": selectedTimeId,
                  "date": formattedDate,
                  "people_count": peopleCount,
                  "cuisine_ids": selectedCuisineData['cuisine_ids'],
                  "sub_cuisine_ids": selectedCuisineData['sub_cuisine_ids'],
                  "local_cuisine_ids": selectedCuisineData['local_cuisine_ids'],
                  "dietary_preference_id": firstPreferenceAlt,
                  "spice_level_id": _selectedSpiceLevelId,
                  "latitude": _currentLatitude,
                  "longitude": _currentLongitude,
                  // "delivery_time_id": selectedTimeId,
                  "address": streetController.text + aptController.text,
                  "state": selectedState,
                  "city": cityController.text,
                  "zip_code": zipController.text,
                  "allergy_prference_text": allergyController.text,
                  "catering_type_id": selectedCateringTypeId,
                };

                Navigator.of(context)
                    .push(MaterialPageRoute(builder: (context) {
                  return SelectChef(
                    userData: userData,
                    catering_id: widget.cateringData?['catering_id'],
                    chefData: state
                        .data, // Assuming state.data contains the chef data
                  );
                }));
              }

              // Handle Timing States
              else if (state is ListTimingSuccess) {
                setState(() {
                  times = state.data.data?.timings ?? [];
                  timeLabels = times.map((timing) {
                    final startTime = DateFormat('h:mm a').format(
                        DateFormat('HH:mm:ss')
                            .parse(timing.startTime ?? '00:00:00'));
                    final endTime = DateFormat('h:mm a').format(
                        DateFormat('HH:mm:ss')
                            .parse(timing.endTime ?? '00:00:00'));
                    return '$startTime - $endTime';
                  }).toList();
                  log("Times updated: ${timeLabels.toString()}");
                  // Set selected time
                  if (widget.cateringData != null) {
                    final timeId = widget.cateringData!['time_slot_id'];
                    if (timeId != null) {
                      final matched = times.firstWhere(
                        (t) => t.id == timeId,
                        orElse: () => Timings(),
                      );
                      if (matched.id != null) {
                        selectedTimeId = matched.id;
                        // Format and set time string
                        final startTime = DateFormat('h:mm a').format(
                            DateFormat('HH:mm:ss')
                                .parse(matched.startTime ?? '00:00:00'));
                        final endTime = DateFormat('h:mm a').format(
                            DateFormat('HH:mm:ss')
                                .parse(matched.endTime ?? '00:00:00'));
                        selectedTime = '$startTime - $endTime';
                      }
                    }
                  }
                });
              }

              // Handle Packaging States
              else if (state is ListPackagingTimeSucess) {
                setState(() {
                  packagingTypeList = state.data.data?.packagingTypes ?? [];
                  packagingTypes =
                      packagingTypeList.map((p) => p.name ?? '').toList();

                  log("Packaging updated: ${packagingTypes.toString()}");
                  // Set packaging type
                  if (widget.cateringData != null) {
                    final packagingId =
                        widget.cateringData!['packaging_type_id'];
                    if (packagingId != null) {
                      final matched = packagingTypeList.firstWhere(
                        (p) => p.id == packagingId,
                        orElse: () => PackagingTypes(),
                      );
                      if (matched.id != null) {
                        selectedPackagingTypeId = matched.id;
                        selectedPackagingType = matched.name;
                      }
                    }
                  }
                });
              } else if (state is CateringTypeSuccess) {
                setState(() {
                  cateringTypeList = state.data.data?.cateringTypes ?? [];
                  cateringtypes =
                      cateringTypeList.map((p) => p.name ?? '').toList();

                  log("Catering updated: ${cateringtypes.toString()}");
                  // Set packaging type
                  if (widget.cateringData != null) {
                    final cateringId = widget.cateringData?['catering_type_id'];

                    if (cateringId != null) {
                      final matched = cateringTypeList.firstWhere(
                        (p) => p.id == cateringId,
                        orElse: () => CateringTypes(),
                      );
                      if (matched.id != null) {
                        selectedCateringTypeId = matched.id;
                        selectedCateringType = matched.name;
                      }
                    }
                  }
                });
              }
              // Handle Dietary Preferences States
              else if (state is ListDietarySuccess) {
                setState(() {
                  final dietList = state.data.data?.dietaries ?? [];
                  dietaryPreferences = dietList
                      .map((diet) {
                        final Map<String, String> pref =
                            _preferences.firstWhere(
                          (p) => p['name'] == diet.name,
                          orElse: () => {
                            'name': diet.name ?? '',
                            'icon': 'assets/icons/default.png',
                          },
                        );

                        return {
                          'id': diet.id,
                          'name': diet.name ?? '',
                          'icon': pref['icon'] ?? 'assets/icons/default.png',
                        };
                      })
                      .toList()
                      .cast<Map<String, dynamic>>();
                  log("Dietary preferences updated: ${dietaryPreferences.toString()}");
                  if (widget.cateringData != null) {
                    final dietaryId =
                        widget.cateringData!['dietary_preference_id'];
                    if (dietaryId != null) {
                      _selectedPreferences.clear();
                      _selectedPreferences.add(dietaryId);
                    }
                  }
                });
              }

              // Handle Spice Level States
              else if (state is ListSpiceLevelSuccess) {
                setState(() {
                  spiceLevels = state.data.data?.spiceLevels ?? [];
                  spiceLevelsList = spiceLevels
                      .asMap()
                      .entries
                      .map((entry) {
                        final index = entry.key;
                        final spice = entry.value;
                        return {
                          'id': spice.id ?? 0,
                          'level': spice.name ?? '',
                          'count': index,
                        };
                      })
                      .toList()
                      .cast<Map<String, dynamic>>();
                  log("Spice levels updated: ${spiceLevelsList.toString()}");
                  if (widget.cateringData != null) {
                    final spiceId = widget.cateringData!['spice_level_id'];
                    if (spiceId != null) {
                      _selectedSpiceLevelId = spiceId;
                    }
                  }
                });
              }
            },
            child: BlocBuilder<MealplanBloc, MealPlanState>(
              builder: (context, state) {
                // Only show loading states here, no data manipulation
                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "orem ipsum dolor sit amet, consectetur adipisci.",
                          style:  TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w500,
                           fontSize: forteen,
                              color: Color(0xFF414346)),
                        ),
                        SizedBox(height: eighteen),

                        // Packaging Dropdown with shimmer
                        cateringtypes.isEmpty
                            ? _buildDropdownShimmer()
                            : _buildDropdown(
                                "Select Catering Type or Event",
                                selectedCateringType,
                                cateringtypes,
                                (v) {
                                  setState(() {
                                    selectedCateringType = v;
                                    final matched = cateringTypeList.firstWhere(
                                      (p) => p.name == v,
                                      orElse: () => CateringTypes(),
                                    );
                                    selectedCateringTypeId = matched.id;
                                  });
                                },
                              ),
                        if (cateringError != null)
                          Padding(
                             padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              cateringError!,
                              style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                        SizedBox(
                       height: twenty,
                        ),

                        // Cuisine Dropdown with shimmer
                        cuisineNames.isEmpty
                            ? _buildDropdownShimmer()
                            : HierarchicalCuisineDropdown(
                                cuisines: cuisines,
                                label: "Select A Cuisine",
                                onSelectionChanged: (selection) {
                                  setState(() {
                                    selectedCuisineData = selection;
                                  });
                                  debugPrint("Selected cuisines: $selection");
                                },
                                initialSelection: selectedCuisineData,
                              ),
                        if (cuisineError != null)
                          Padding(
                             padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              cuisineError!,
                              style: TextStyle( color: Colors.red, fontSize: twelve),
                            ),
                          ),
                        SizedBox(
                           height: twenty,
                        ),
                        SizedBox(height: twelve),
                        _buildCounter(),
                        if (peopleCountError != null)
                          Padding(
                              padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              peopleCountError!,
                                style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            
                            ),
                          ),
                        SizedBox(height: twelve),
                        _buildDatePicker(),
                        if (dateError != null)
                          Padding(
                             padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              dateError!,
                              style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            
                            ),
                          ),
                           SizedBox(
                          height: twenty,
                        ),
                        SizedBox(height: twelve),


                        // Timing Dropdown with shimmer
                        timeLabels.isEmpty
                            ? _buildDropdownShimmer()
                            : _buildDropdown(
                                "Delivery Time",
                                selectedTime,
                                timeLabels,
                                (v) {
                                  setState(() {
                                    selectedTime = v;
                                    final start = v?.split(' - ').first.trim();
                                    final parsedStart =
                                        DateFormat('HH:mm:ss').format(
                                      DateFormat('h:mm a').parse(start!),
                                    );
                                    final matched = times.firstWhere(
                                      (t) => t.startTime == parsedStart,
                                      orElse: () => Timings(),
                                    );
                                    selectedTimeId = matched.id;
                                  });
                                },
                              ),
                        if (timeError != null)
                          Padding(
                               padding: EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              timeError!,
                              style: TextStyle(color: Colors.red, fontSize: 12),
                            ),
                          ),
                         SizedBox(
                          height: twenty,
                        ),
                        SizedBox(height: ten),

                        // Address fields (no shimmer needed)
                         Text('Address',
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                    fontSize: forteen)),
                         SizedBox(height: sixteen/2),
                        _buildTextField("Street address",
                            controller: streetController),
                        if (streetError != null)
                          Padding(
                           padding:  EdgeInsets.only(left: eighteen ,top: sixteen/8),
                            child: Text(
                              streetError!,
                             style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                        SizedBox(
                          height: twelve,
                        ),
                        _buildTextField("Apartment, suite, unit, etc.",
                            controller: aptController),
                        if (aptError != null)
                          Padding(
                            padding: const EdgeInsets.only(left: 18.0, top: 2),
                            child: Text(
                              aptError!,
                              style: TextStyle(color: Colors.red, fontSize: 12),
                            ),
                          ),
                        SizedBox(
                          height: 12,
                        ),
                        _buildDropdown("State", selectedState, usStates,
                            (v) => setState(() => selectedState = v)),
                        if (stateError != null)
                          Padding(
                          padding: EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              stateError!,
                            style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                        SizedBox(
                          height: twelve,
                        ),
                        _buildTextField("City", controller: cityController),
                        if (cityError != null)
                          Padding(
                              padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              cityError!,
                            style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                        SizedBox(
                          height: twenty,
                        ),
                         Text("Zip Code",
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: forteen)),
                       SizedBox(height: sixteen/2),
                        _buildTextField("Enter zip code",
                            controller: zipController),
                        if (zipError != null)
                          Padding(
                          padding:  EdgeInsets.only(left: eighteen, top: sixteen/8),
                            child: Text(
                              zipError!,
                              style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                          SizedBox(
                          height: twelve,
                        ),
                        SizedBox(height: sixteen),

                        // Packaging Dropdown with shimmer
                        packagingTypes.isEmpty
                            ? _buildDropdownShimmer()
                            : _buildDropdown(
                                "Select Packaging Type",
                                selectedPackagingType,
                                packagingTypes,
                                (v) {
                                  setState(() {
                                    selectedPackagingType = v;
                                    final matched =
                                        packagingTypeList.firstWhere(
                                      (p) => p.name == v,
                                      orElse: () => PackagingTypes(),
                                    );
                                    selectedPackagingTypeId = matched.id;
                                  });
                                },
                              ),
                        if (packagingError != null)
                          Padding(
                             padding: EdgeInsets.only(left:eighteen , top: sixteen/8),
                            child: Text(
                              packagingError!,
                                style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                           SizedBox(
                          height: twenty,
                        ),

                        SizedBox(height: twenty),

                        // Dietary Preferences with shimmer
                         Text("Choose dietary preferences",
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: forteen,
                                color: Color(0xFF1F2122))),
                        const SizedBox(height: 8),
                        dietaryPreferences.isEmpty
                            ? Column(
                                children: List.generate(
                                    4, (index) => _buildListItemShimmer()))
                            : ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: dietaryPreferences.length,
                                itemBuilder: (context, index) {
                                  final pref = dietaryPreferences[index];
                                  final name = pref['name'] as String;
                                  final icon = pref['icon'] as String;
                                  final id = pref['id']
                                      as int; // Assuming id is String
                                  final isSelected =
                                      _selectedPreferences.contains(id);

                                  return Padding(
                                   padding:
                                        EdgeInsets.only(bottom: forteen),
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          if (isSelected) {
                                            _selectedPreferences.remove(id);
                                          } else {
                                            _selectedPreferences
                                              ..clear()
                                              ..add(id); // Single selection
                                          }
                                        });
                                      },
                                      child: Container(
                                        padding:  EdgeInsets.all(sixteen),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? const Color(0xFFE1DDD5)
                                              : const Color(0xFFF6F3EC),
                                          border: Border.all(
                                              color: isSelected
                                                  ? const Color(0xFF1F2122)
                                                  : const Color(0xFFB9B6AD)),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          children: [
                                            Image.asset(icon,
                                                    width: twenty, height: twenty),
                                              SizedBox(width: twenty),
                                            Text(name,
                                                style: TextStyle(
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w600,
                                                    fontFamily: 'Inter')),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                        if (dietaryError != null)
                          Padding(
                              padding: EdgeInsets.only(left:eighteen , top: sixteen/8),
                          
                            child: Text(
                              dietaryError!,
                              style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                          SizedBox(
                          height: twenty,
                        ),
                        SizedBox(height: twentyFour),

                        // Spice Levels with shimmer
                        Text('Choose spice level',
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                             fontSize: forteen,
                                color: Colors.grey[900])),
                        const SizedBox(height: 8),
                        spiceLevelsList.isEmpty
                            ? Column(
                                children: List.generate(
                                    3, (index) => _buildListItemShimmer()))
                            : Column(
                                children: spiceLevelsList.map((level) {
                                  final isSelected =
                                      _selectedSpiceLevelId == level['id'];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 14),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? const Color(0xFFE1DDD5)
                                          : const Color(0xFFF6F3EC),
                                      border: Border.all(
                                          color: isSelected
                                              ? Colors.black
                                              : const Color(0xFFB9B6AD)),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: InkWell(
                                      onTap: () => setState(() {
                                        _selectedSpiceLevelId = level['id'];
                                        _selectedSpiceLevelName =
                                            level['level'];
                                      }),
                                      child: Padding(
                                        padding:  EdgeInsets.symmetric(
                                           horizontal: sixteen, vertical: sixteen),
                                        child: Row(
                                          children: [
                                            Expanded(
                                                child: Text(level['level'],
                                                    style:  TextStyle(
                                                        fontSize: forteen,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontFamily: 'Inter'))),
                                            if (level['count'] == 0)
                                              Image.asset(
                                                  'assets/icons/close_2.png',
                                                  width: twenty,
                                                  height: twenty)
                                            else
                                              _buildSpiceIcons(level['count']),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                        if (spiceLevelError != null)
                          Padding(
                           padding:  EdgeInsets.only(left: eighteen, top: 0),
                            child: Text(
                              spiceLevelError!,
                             style: TextStyle(
                                  color: Colors.red, fontSize: twelve),
                            ),
                          ),
                         SizedBox(
                          height: forteen,
                        ),
                        SizedBox(
                          height: twelve,
                        ),
                        const SizedBox(height: 20),
                         Text(
                            "Please Indicate Any Food Allergies or Preferences",
                            style: TextStyle(
                                fontFamily: 'Inter',
                                 fontWeight: FontWeight.w500,
                                fontSize: forteen)),
                          SizedBox(height: ten),
                        _buildTextField("Type in your answer",
                            controller: allergyController, maxLines: 7),
                         SizedBox(height: twentyFour),

                        SizedBox(
                       height: ten*4+forteen,
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed:
                                state is FindChefEventLoading ? null : _submit,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF1F2122),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(32),
                              ),
                            ),
                            child: state is FindChefEventLoading
                                ?  SizedBox(
                                      width: twentyFour,
                                    height: twentyFour,
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                      strokeWidth: 3,
                                    ),
                                  )
                                :  Text(
                                    'Select New Chef',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                    fontSize: sixteen,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.32,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                        ),

                       SizedBox(height: ten*4),
                      ]),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
