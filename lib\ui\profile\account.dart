import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/home.dart';
import 'package:db_eats/ui/profile/dabbawallet.dart';
import 'package:db_eats/ui/profile/favorites.dart';
import 'package:db_eats/ui/profile/monthlysaverspack.dart';
import 'package:db_eats/ui/profile/payments.dart';
import 'package:db_eats/ui/profile/profile.dart';
import 'package:db_eats/ui/profile/settings.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/material.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    void openCart() {
      // Handle cart open action
      print('Opening cart');
      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CartPage(),
          ));
    }

    // Define the background color
    const Color backgroundColor = Color(0xFFf6f3ec);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: backgroundColor,
        elevation: 0,
        automaticallyImplyLeading: false, // No back button
        centerTitle: false, // No centerTitle
        title:  Text(
          'Account',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w700,
            fontSize: twenty,
            color: Colors.black,
          ),
        ),
      ),
      body: ListView(
        children: [
          SizedBox(
            height: sixteen/4,
          ),
          _buildOptionRow(
            context,
            'Account Info',
            'assets/icons/account.png',
            () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const AccountInfoPage())),
          ),
          _buildOptionRow(
            context,
            'Favorites',
            'assets/icons/favorites.png',
            () => Navigator.push(context,
                MaterialPageRoute(builder: (context) => const FavoritesPage())),
          ),
          _buildOptionRow(
            context,
            "Monthly Saver's Pass",
            'assets/icons/tag.png',
            () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MonthlySaverPassPage())),
          ),
          _buildOptionRow(
            context,
            'Payments',
            'assets/icons/payment.png',
            () => Navigator.push(context,
                MaterialPageRoute(builder: (context) => const PaymentsPage())),
          ),
          _buildOptionRow(
            context,
            'Settings',
            'assets/icons/settings.png',
            () => Navigator.push(context,
                MaterialPageRoute(builder: (context) => const SettingsPage())),
          ),
          _buildOptionRow(
            context,
            'Db-Wallet',
            'assets/icons/payment.png',
            () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const DabbaWalletPage())),
          ),
          _buildOptionRow(
            context,
            'Help Center',
            'assets/icons/help.png',
            () => Navigator.pushNamed(context, '/helpcenter'),
          ),
          _buildOptionRow(
            context,
            'Logout',
            'assets/icons/logout.png',
            () => _handleLogout(context),
          ),
        ],
      ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: Initializer.cartCount ?? 0,
        onPressed: openCart,
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    await LocalStorage.setAccessToken('');
    await LocalStorage.setRefreshToken('');
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const Home()),
      (route) => false,
    );
  }

  Widget _buildOptionRow(
    BuildContext context,
    String title,
    String iconPath,
    VoidCallback onTap,
  ) {
    if (title == 'Logout') {
      return InkWell(
        onTap: () => _handleLogout(context),
        child: Padding(
          padding:  EdgeInsets.symmetric(horizontal: sixteen, vertical: ten*1.3),
          child: Row(
            children: [
              // Left icon
              Image.asset(
                iconPath,
                width: eighteen,
                height: eighteen,
              ),
               SizedBox(width: sixteen),
              // Text
              Expanded(
                child: Text(
                  title,
                  style:  TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                  ),
                ),
              ),
              // Right arrow icon
              Image.asset(
                'assets/icons/right.png',
                width: ten*1.1,
                height: ten*1.1,
              ),
            ],
          ),
        ),
      );
    }
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding:  EdgeInsets.symmetric(horizontal: sixteen, vertical: ten*1.3),
        child: Row(
          children: [
            // Left icon
            Image.asset(
              iconPath,
              width: eighteen,
              height: eighteen,
            ),
             SizedBox(width: sixteen),
            // Text
            Expanded(
              child: Text(
                title,
                style:  TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: forteen,
                ),
              ),
            ),
            // Right arrow icon
            Image.asset(
              'assets/icons/right.png',
              width: ten*1.1,
              height: ten*1.1,
            ),
          ],
        ),
      ),
    );
  }
}
