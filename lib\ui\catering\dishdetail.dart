import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/dish/dishdetailmodel.dart';

class DishData {
  final String title;
  final double price;
  final String description;
  final String ingredients;
  final String imageUrl;
  final int servings;
  final bool isSpicy;
  final bool isOrganic;

  DishData({
    required this.title,
    required this.price,
    required this.description,
    required this.ingredients,
    required this.imageUrl,
    required this.servings,
    required this.isSpicy,
    required this.isOrganic,
  });

  factory DishData.fromDishDetailModel(DishDetailModel model) {
    final dish = model.data?.dish;
    final servingSizePrice = dish?.servingSizePrices?.isNotEmpty == true
        ? dish!.servingSizePrices!.first
        : null;

    return DishData(
      title: dish?.name ?? 'Dish Title',
      price: double.tryParse(servingSizePrice?.price ?? '19.99') ?? 19.99,
      description: dish?.description ?? 'No description available',
      ingredients: dish?.ingredients ?? 'No ingredients listed',
      imageUrl: dish?.photo ?? 'assets/images/dish_1.png',
      servings: servingSizePrice?.servingSize?.serves ?? 2,
      isSpicy: dish?.spiceLevel?.name != null &&
          ['spicy', 'medium', 'hot']
              .contains(dish!.spiceLevel!.name!.toLowerCase()),
      isOrganic: dish?.dietary?.name != null &&
          dish!.dietary!.name!.toLowerCase().contains('organic'),
    );
  }
}

class DishDetailPage extends StatefulWidget {
  final String dishId;
  final int? chefId;

  const DishDetailPage({
    Key? key,
    required this.dishId,
    this.chefId,
  }) : super(key: key);

  @override
  State<DishDetailPage> createState() => _DishDetailPageState();
}

class _DishDetailPageState extends State<DishDetailPage> {
  int quantity = 1;
  bool isFavorite = false;
  bool _favoriteStateOverride = false;
  ServingSizePrices? selectedServingSize;

  @override
  void initState() {
    super.initState();
    final int? parsedDishId = int.tryParse(widget.dishId);
    if (parsedDishId != null) {
      context.read<OrderBloc>().add(ListDishDetails(parsedDishId));
    } else {
      context.read<OrderBloc>().add(ListDishDetails(0));
    }
  }

  void _onToggleFavorite(int dishId) {
    context.read<AccountBloc>().add(AddFavouritesEvent(dishId));
  }

  void _onAddToCart(DishData dishData, DishDetailModel model) {
    if (widget.chefId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Chef information unavailable',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          backgroundColor: Colors.black,
        ),
      );
      return;
    }

    if (model.data?.dish?.servingSizePrices?.isNotEmpty == true &&
        model.data!.dish!.servingSizePrices!.length > 1) {
      _showServingSizeDialog(context, dishData, model);
    } else {
      final int? parsedDishId = int.tryParse(widget.dishId);
      if (parsedDishId != null) {
        context.read<AccountBloc>().add(AddToCartEvent({
              'chef_id': widget.chefId!,
              'chef_dish_id': parsedDishId,
              'quantity': 1,
              'serving_size_id':
                  model.data?.dish?.servingSizePrices?.isNotEmpty == true
                      ? model.data!.dish!.servingSizePrices!.first.servingSizeId
                      : 1,
            }));
      }
    }
  }

  void _showServingSizeDialog(
      BuildContext context, DishData dishData, DishDetailModel model) {
    ServingSizePrices? tempSelectedServingSize =
        selectedServingSize ?? model.data!.dish!.servingSizePrices!.first;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              backgroundColor: Colors.white,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Responsive dimensions based on screen size
                  final double screenWidth = MediaQuery.of(context).size.width;
                  final double screenHeight =
                      MediaQuery.of(context).size.height;
                  final double dialogWidth = screenWidth > 600
                      ? 400
                      : screenWidth * 0.85; // Max 400px or 85% of screen width
                  final double padding =
                      screenWidth * 0.04; // 4% of screen width
                  final double fontSizeTitle =
                      screenWidth * 0.045; // ~18px on 400px
                  final double fontSizeText =
                      screenWidth * 0.035; // ~14px on 400px
                  final double iconSize = screenWidth * 0.06; // ~24px on 400px

                  return Container(
                    width: dialogWidth,
                    padding: EdgeInsets.all(padding),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Select Serving Size',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: fontSizeTitle,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Colors.black,
                                size: iconSize,
                              ),
                              padding: EdgeInsets.zero,
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                            ),
                          ],
                        ),
                        SizedBox(height: padding),
                        ...?model.data?.dish?.servingSizePrices?.map((size) {
                          return GestureDetector(
                            onTap: () {
                              setDialogState(() {
                                tempSelectedServingSize = size;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: padding * 0.5),
                              padding: EdgeInsets.symmetric(
                                horizontal: padding,
                                vertical: padding * 0.75,
                              ),
                              decoration: BoxDecoration(
                                color: tempSelectedServingSize?.id == size.id
                                    ? Colors.black
                                    : const Color(0xFFE1E3E6),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: tempSelectedServingSize?.id == size.id
                                      ? Colors.black
                                      : Colors.grey[300]!,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Flexible(
                                    child: Text(
                                      size.servingSize?.title ?? 'Size',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w500,
                                        fontSize: fontSizeText,
                                        color: tempSelectedServingSize?.id ==
                                                size.id
                                            ? Colors.white
                                            : const Color(0xFF1F2122),
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Text(
                                    '\$${double.tryParse(size.price ?? '0.00')?.toStringAsFixed(2) ?? '0.00'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: fontSizeText,
                                      color:
                                          tempSelectedServingSize?.id == size.id
                                              ? Colors.white
                                              : const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                        SizedBox(height: padding),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: fontSizeText,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                            ),
                            SizedBox(width: padding * 0.5),
                            ElevatedButton(
                              onPressed: () {
                                if (widget.chefId == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Chef information unavailable',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                        ),
                                      ),
                                      backgroundColor: Colors.black,
                                    ),
                                  );
                                  return;
                                }
                                setState(() {
                                  selectedServingSize = tempSelectedServingSize;
                                });
                                final int? parsedDishId =
                                    int.tryParse(widget.dishId);
                                if (parsedDishId != null) {
                                  context
                                      .read<AccountBloc>()
                                      .add(AddToCartEvent({
                                        'chef_id': widget.chefId!,
                                        'chef_dish_id': parsedDishId,
                                        'quantity': 1,
                                        'serving_size_id':
                                            tempSelectedServingSize
                                                    ?.servingSizeId ??
                                                1,
                                      }));
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: EdgeInsets.symmetric(
                                  horizontal: padding,
                                  vertical: padding * 0.75,
                                ),
                              ),
                              child: Text(
                                'Add to Cart',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w600,
                                  fontSize: fontSizeText,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.close, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is AddFavouritesSuccess) {
            setState(() {
              isFavorite = true;
              _favoriteStateOverride = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          } else if (state is AddFavouritesFailed) {
            setState(() {
              isFavorite = false;
              _favoriteStateOverride = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          } else if (state is AddToCartSuccess) {
            Navigator.of(context, rootNavigator: true).pop(); // Pop the dialog
            // ScaffoldMessenger.of(context).showSnackBar(
            //   SnackBar(
            //     content: Text(
            //       state.message,
            //       style: const TextStyle(
            //         fontFamily: 'Inter',
            //         fontWeight: FontWeight.w400,
            //         fontSize: 14,
            //       ),
            //     ),
            //     backgroundColor: Colors.black,
            //   ),
            // );
          } else if (state is AddToCartFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          }
        },
        child: BlocBuilder<OrderBloc, OrderState>(
          builder: (context, state) {
            if (state is ListDishDetailsLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: Colors.black,
                ),
              );
            } else if (state is ListDishDetailsSuccess) {
              final dishData =
                  DishData.fromDishDetailModel(Initializer.dishDetailModel);

              if (!_favoriteStateOverride) {
                isFavorite =
                    Initializer.dishDetailModel.data?.isFavourite ?? false;
              }

              selectedServingSize ??= Initializer.dishDetailModel.data?.dish
                          ?.servingSizePrices?.isNotEmpty ==
                      true
                  ? Initializer
                      .dishDetailModel.data!.dish!.servingSizePrices!.first
                  : null;
              return _buildDishDetailContent(
                  context, dishData, Initializer.dishDetailModel);
            } else if (state is ListDishDetailsFailed) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    ElevatedButton(
                      onPressed: () {
                        final int? parsedDishId = int.tryParse(widget.dishId);
                        if (parsedDishId != null) {
                          context
                              .read<OrderBloc>()
                              .add(ListDishDetails(parsedDishId));
                        }
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }
            return const Center(
                child: Text('Invalid dish ID or no data available'));
          },
        ),
      ),
    );
  }

  Widget _buildDishDetailContent(
      BuildContext context, DishData dishData, DishDetailModel model) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(left: 16.0, right: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: model.data?.dish?.photo != null
                        ? Image.network(
                            ServerHelper.imageUrl + model.data!.dish!.photo!,
                            width: double.infinity,
                            height: 170,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: double.infinity,
                                height: 170,
                                color: Colors.grey[300],
                                child:
                                    const Icon(Icons.restaurant_menu, size: 50),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                width: double.infinity,
                                height: 170,
                                color: Colors.grey[100],
                                child: const Center(
                                    child: CircularProgressIndicator()),
                              );
                            },
                          )
                        : Container(
                            width: double.infinity,
                            height: 170,
                            color: Colors.grey[300],
                            child: const Icon(Icons.restaurant_menu, size: 50),
                          ),
                  ),
                  const SizedBox(height: 18),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          dishData.title,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: 24,
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          _buildActionButton(
                            context,
                            iconPath: 'assets/icons/favorites.png',
                            onPressed: () => _onToggleFavorite(
                                int.tryParse(widget.dishId) ?? 0),
                            tooltip: 'Add to favorites',
                            child: Icon(
                              isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: isFavorite
                                  ? Colors.red
                                  : const Color(0xFF1F2122),
                              size: MediaQuery.of(context).size.width * 0.06,
                            ),
                          ),
                          const SizedBox(width: 8),
                          _buildActionButton(
                            context,
                            iconPath: 'assets/icons/shopping_cart2.png',
                            onPressed: () => _onAddToCart(dishData, model),
                            tooltip: 'Add to cart',
                          ),
                        ],
                      ),
                    ],
                  ),

                  // // Serving Size Dropdown
                  // if (model.data?.dish?.servingSizePrices?.isNotEmpty == true)
                  //   DropdownButton<String>(
                  //     value: selectedServingSize?.id?.toString() ??
                  //         model.data!.dish!.servingSizePrices!.first.id
                  //             ?.toString(),
                  //     onChanged: (String? newValue) {
                  //       setState(() {
                  //         selectedServingSize =
                  //             model.data!.dish!.servingSizePrices!.firstWhere(
                  //                 (size) => size.id.toString() == newValue);
                  //       });
                  //     },
                  //     items: model.data!.dish!.servingSizePrices!
                  //         .map<DropdownMenuItem<String>>(
                  //           (ServingSizePrices size) =>
                  //               DropdownMenuItem<String>(
                  //             value: size.id.toString(),
                  //             child: Text(
                  //               '${size.servingSize?.title ?? 'Size'} (\$${double.tryParse(size.price ?? '0.00')?.toStringAsFixed(2) ?? '0.00'})',
                  //               style: const TextStyle(
                  //                 fontFamily: 'Inter',
                  //                 fontSize: 14,
                  //               ),
                  //             ),
                  //           ),
                  //         )
                  //         .toList(),
                  //   ),
                  // const SizedBox(height: 10),
                  const SizedBox(height: 10),
                  Text(
                    '\$${double.tryParse(selectedServingSize?.price ?? dishData.price.toString())?.toStringAsFixed(2) ?? dishData.price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 24,
                    ),
                  ),
                  const SizedBox(height: 10),
                  if (model.data?.dish?.servingSizePrices?.isNotEmpty == true)
                    Wrap(
                      spacing: 4.0,
                      runSpacing: 6.0,
                      children: model.data!.dish!.servingSizePrices!
                          .map((size) => ChoiceChip(
                                label: Text(
                                  '${size.servingSize?.title ?? 'Size'} (\$${double.tryParse(size.price ?? '0.00')?.toStringAsFixed(2) ?? '0.00'})',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 10,
                                    color: selectedServingSize?.id == size.id
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                                selected: selectedServingSize?.id == size.id,
                                onSelected: (bool selected) {
                                  if (selected) {
                                    setState(() {
                                      selectedServingSize = size;
                                    });
                                  }
                                },
                                selectedColor: Colors.black,
                                backgroundColor: const Color(0xFFE1E3E6),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                  side: BorderSide(
                                    color: selectedServingSize?.id == size.id
                                        ? Colors.black
                                        : Colors.grey[300]!,
                                  ),
                                ),
                                showCheckmark: false,
                                labelPadding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 2, vertical: 3),
                              ))
                          .toList(),
                    ),
                  const SizedBox(height: 16),
                  const Text(
                    'Description',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    dishData.description,
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: Color(0xFF1F2122),
                      letterSpacing: -0.1,
                      height: 1.35,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Ingredients:',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          dishData.ingredients,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (selectedServingSize?.servingSize?.serves != null)
                        _buildInfoContainer(
                          'assets/icons/servings.png',
                          '${selectedServingSize?.servingSize?.serves} servings',
                        ),
                      if (model.data?.dish?.spiceLevel?.name != null) ...[
                        const SizedBox(width: 8),
                        _buildInfoContainer(
                          'assets/icons/chili.png',
                          model.data!.dish!.spiceLevel!.name ?? '',
                        ),
                      ],
                      if (model.data?.dish?.dietary?.name != null) ...[
                        const SizedBox(width: 8),
                        _buildInfoContainer(
                          'assets/icons/organic.png',
                          model.data!.dish!.dietary!.name ?? '',
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String iconPath,
    required VoidCallback onPressed,
    required String tooltip,
    Widget? child,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      width: screenWidth * 0.1,
      height: screenWidth * 0.1,
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(screenWidth * 0.025),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: child ??
            Image.asset(
              iconPath,
              width: screenWidth * 0.06,
              height: screenWidth * 0.06,
            ),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
        tooltip: tooltip,
      ),
    );
  }

  Widget _buildInfoContainer(String iconPath, String text,
      {bool useNetworkImage = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (useNetworkImage)
            Image.network(
              iconPath,
              width: 20,
              height: 22,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(Icons.image_not_supported, size: 20);
              },
            )
          else
            Image.asset(
              iconPath,
              width: 20,
              height: 22,
            ),
          const SizedBox(height: 2),
          Text(
            text,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
