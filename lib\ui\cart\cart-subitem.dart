import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/cart_checkout_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/cart/viewcartmodel.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/ui/cart/add_item.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';

class CartPage2 extends StatefulWidget {
  final int chef_id;

  const CartPage2({
    super.key,
    required this.chef_id,
  });

  @override
  State<CartPage2> createState() => _CartPage2State();
}

class _CartPage2State extends State<CartPage2> {
  List<Items>? cartItems;
  double subtotal = 0.0;
  Chef? chefData;
  String? customerTimePreference;
  String? currentAddress;

  Map<int, String> _updatingQuantity = {};
  List<Timings>? _availableTimings;

  @override
  void initState() {
    super.initState();
    context.read<AccountBloc>().add(ViewCartEvent(widget.chef_id));
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '12AM';
    try {
      final timeParts = time.substring(0, 5).split(':');
      int hours = int.parse(timeParts[0]);
      final period = hours >= 12 ? 'PM' : 'AM';
      hours = hours > 12
          ? hours - 12
          : hours == 0
              ? 12
              : hours;
      return '$hours$period';
    } catch (e) {
      return '12AM';
    }
  }

  String _formatDays(List<String>? days) {
    if (days == null || days.isEmpty) return 'N/A';
    return days.map((day) => day[0]).join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    return MultiBlocProvider(
      providers: [
        BlocProvider<AccountBloc>.value(
          value: context.read<AccountBloc>(),
        ),
        BlocProvider<MealplanBloc>(
          create: (context) => MealplanBloc(),
        ),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ViewCartSuccess) {
            setState(() {
              cartItems = state.data?.items ?? [];
              subtotal = (state.data?.totalPrice ?? 0).toDouble();
              if (state.data?.chef != null &&
                  state.data!.chef!.chefOperationDays != null &&
                  state.data!.chef!.chefOperationDays!.isNotEmpty) {
                chefData = state.data?.chef;
              }
              if (state.data?.customerTimePreference is Map) {
                final timeData =
                    state.data?.customerTimePreference as Map<String, dynamic>;
                final startTime = _formatTimeToAmPm(timeData['start_time']);
                final endTime = _formatTimeToAmPm(timeData['end_time']);
                customerTimePreference = '$startTime-$endTime';
              } else {
                customerTimePreference = 'Any time';
              }
              currentAddress = state.data?.currentAddress?.addressText;
              _updatingQuantity.clear();
            });
          } else if (state is RemoveCartItemSuccess) {
            Navigator.pop(context);
            context.read<AccountBloc>().add(ListCartEvent());
          } else if (state is UpdateCartItemQuantitySuccess) {
            // Only trigger ViewCartEvent if we need to refresh data
            if (mounted) {
              context.read<AccountBloc>().add(ViewCartEvent(widget.chef_id));
            }
          }
        },
        child: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            if (state is ViewCartLoading) {
              return Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  centerTitle: false,
                  title: const Text(
                    'Back',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
                body: const SafeArea(
                  child: Center(
                    child: CupertinoActivityIndicator(
                      radius: 10,
                    ),
                  ),
                ),
              );
            }

            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                backgroundColor: Colors.white,
                elevation: 0,
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                    size: isLargeScreen ? 32 : screenSize.width * 0.06,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                centerTitle: false,
                title: Text(
                  'Back',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: isLargeScreen ? 20 : screenSize.width * 0.041,
                    color: Colors.black,
                  ),
                ),
                actions: [
                  Padding(
                    padding: EdgeInsets.only(right: screenSize.width * 0.04),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AddItem(
                              id: widget.chef_id,
                              title: chefData?.chefName ?? '',
                              fromPage: 'Cart',
                            ),
                          ),
                        ).then((_) {
                          // Refresh cart data when returning from AddItem
                          context
                              .read<AccountBloc>()
                              .add(ViewCartEvent(widget.chef_id));
                        });
                      },
                      child: Row(
                        children: [
                          Icon(
                            Icons.add,
                            size: isLargeScreen ? 16 : screenSize.width * 0.035,
                            color: const Color(0xFF414346),
                          ),
                          SizedBox(width: screenSize.width * 0.01),
                          Text(
                            'Add Dish',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize:
                                  isLargeScreen ? 18 : screenSize.width * 0.04,
                              color: const Color(0xff414346),
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              body: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: screenSize.height * 0.02),
                    _buildChefInfo(screenSize, isLargeScreen),
                    SizedBox(height: screenSize.height * 0.017),
                    _buildDeliveryInfo(screenSize, isLargeScreen),
                    SizedBox(height: screenSize.height * 0.017),
                    Expanded(
                      child: Column(
                        children: [
                          Expanded(
                            child: ListView.builder(
                              padding: EdgeInsets.symmetric(
                                  horizontal: screenSize.width * 0.04),
                              itemCount: (cartItems?.length ?? 0) + 1,
                              itemBuilder: (context, index) {
                                if (index == cartItems?.length) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: screenSize.height * 0.012),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Subtotal',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: isLargeScreen
                                                ? 18
                                                : screenSize.width * 0.04,
                                          ),
                                        ),
                                        Text(
                                          '\$${subtotal.toStringAsFixed(2)}',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: isLargeScreen
                                                ? 18
                                                : screenSize.width * 0.04,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }

                                final item = cartItems![index];
                                return _buildCartItemRow(
                                    item, screenSize, isLargeScreen);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.fromLTRB(
                        screenSize.width * 0.05,
                        screenSize.height * 0.025,
                        screenSize.width * 0.05,
                        screenSize.height * 0.06,
                      ),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CartCheckoutPage(
                                chef_id: widget.chef_id,
                                subtotal: subtotal,
                                selectedChefsWithDetails: {
                                  'chef': chefData,
                                  'items': cartItems,
                                  'totalPrice': subtotal,
                                  'currentAddress': currentAddress,
                                  'chefPhoto': chefData?.chefPhoto,
                                  'chefName': chefData?.chefName,
                                },
                              ),
                            ),
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                              vertical: screenSize.height * 0.017),
                          decoration: BoxDecoration(
                            color: Colors.black,
                            border: Border.all(color: const Color(0xFFAAADB1)),
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Center(
                            child: Text(
                              'Checkout',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: isLargeScreen
                                    ? 18
                                    : screenSize.width * 0.04,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildChefInfo(Size screenSize, bool isLargeScreen) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
      child: Row(
        children: [
          CircleAvatar(
            radius: isLargeScreen ? 30 : screenSize.width * 0.06,
            backgroundImage: NetworkImage(
                ServerHelper.imageUrl + (chefData?.chefPhoto ?? '')),
            backgroundColor: Colors.transparent,
          ),
          SizedBox(width: screenSize.width * 0.03),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                chefData?.chefName ?? '',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: isLargeScreen ? 20 : screenSize.width * 0.045,
                ),
              ),
              SizedBox(height: screenSize.height * 0.007),
              Row(
                children: [
                  Image.asset(
                    'assets/icons/calender_3.png',
                    width: isLargeScreen ? 14 : screenSize.width * 0.03,
                    height: isLargeScreen ? 14 : screenSize.width * 0.03,
                  ),
                  SizedBox(width: screenSize.width * 0.01),
                  Text(
                    'Open, ${_formatTimeToAmPm(chefData?.chefOperationTime?.startTime)}-${_formatTimeToAmPm(chefData?.chefOperationTime?.endTime)}, ',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 12 : screenSize.width * 0.025,
                      color: const Color(0xFF414346),
                    ),
                  ),
                  Text(
                    _formatDays(chefData?.chefOperationDays),
                    style: TextStyle(
                      fontSize: isLargeScreen ? 12 : screenSize.width * 0.025,
                      color: const Color(0xFF414346),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCartItemRow(Items item, Size screenSize, bool isLargeScreen) {
    return Padding(
      padding: EdgeInsets.only(bottom: screenSize.height * 0.01),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE1E3E6)),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.all(screenSize.width * 0.03),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                ServerHelper.imageUrl + (item.dishPhoto ?? ''),
                width: isLargeScreen ? 56 : screenSize.width * 0.12,
                height: isLargeScreen ? 56 : screenSize.width * 0.12,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: isLargeScreen ? 56 : screenSize.width * 0.12,
                    height: isLargeScreen ? 56 : screenSize.width * 0.12,
                    color: Colors.grey.shade300,
                    child: const Icon(Icons.restaurant),
                  );
                },
              ),
            ),
            SizedBox(width: screenSize.width * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.dishName ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                    ),
                  ),
                  SizedBox(height: screenSize.height * 0.005),
                  Text(
                    '\$${item.price?.toStringAsFixed(2) ?? '0.00'}',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.037,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                _buildQuantityButton(
                  Icons.remove,
                  () {
                    setState(() {
                      _updatingQuantity[item.cartItemId!] = 'remove';
                    });
                    if (item.quantity! <= 1) {
                      context.read<AccountBloc>().add(
                            RemoveCartItemEvent(item.dishId!),
                          );
                    } else {
                      _updateQuantity(
                          item.cartItemId!, item.quantity! - 1, false);
                    }
                  },
                  isDelete: item.quantity == 1,
                  cartItemId: item.cartItemId!,
                  screenSize: screenSize,
                  isLargeScreen: isLargeScreen,
                ),
                SizedBox(width: screenSize.width * 0.03),
                Text(
                  '${item.quantity ?? 0}',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                  ),
                ),
                SizedBox(width: screenSize.width * 0.03),
                _buildQuantityButton(
                  Icons.add,
                  () {
                    setState(() {
                      _updatingQuantity[item.cartItemId!] = 'add';
                    });
                    _updateQuantity(item.cartItemId!, item.quantity! + 1, true);
                  },
                  cartItemId: item.cartItemId!,
                  screenSize: screenSize,
                  isLargeScreen: isLargeScreen,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityButton(
    IconData icon,
    VoidCallback onPressed, {
    bool isDelete = false,
    required int cartItemId,
    required Size screenSize,
    required bool isLargeScreen,
  }) {
    bool isLoadingAdd = _updatingQuantity[cartItemId] == 'add';
    bool isLoadingRemove = _updatingQuantity[cartItemId] == 'remove';

    bool isThisButtonLoading = (icon == Icons.add && isLoadingAdd) ||
        ((icon == Icons.remove || isDelete) && isLoadingRemove);

    if (isThisButtonLoading) {
      return SizedBox(
        width: isLargeScreen ? 28 : screenSize.width * 0.06,
        height: isLargeScreen ? 28 : screenSize.width * 0.06,
        child: Center(
          child: SizedBox(
            width: isLargeScreen ? 20 : screenSize.width * 0.04,
            height: isLargeScreen ? 20 : screenSize.width * 0.04,
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
            ),
          ),
        ),
      );
    }

    return InkWell(
      onTap: onPressed,
      child: Container(
        width: isLargeScreen ? 28 : screenSize.width * 0.06,
        height: isLargeScreen ? 28 : screenSize.width * 0.06,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE1E3E6), width: 1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          isDelete ? Icons.delete_outline : icon,
          size: isLargeScreen ? 18 : screenSize.width * 0.04,
          color: isDelete ? Colors.red : const Color(0xFF414346),
        ),
      ),
    );
  }

  void _updateQuantity(int cartItemId, int newQuantity, bool isIncrement) {
    if (!mounted) return;

    // Store the current chef data
    final currentChefData = chefData;

    context.read<AccountBloc>().add(
          UpdateCartItemQuantityEvent(cartItemId, newQuantity),
        );

    // Add a delay to ensure the update is processed before refreshing
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          // Temporarily restore chef data while waiting for refresh
          chefData = currentChefData;
        });
        context.read<AccountBloc>().add(ViewCartEvent(widget.chef_id));
      }
    });
  }

  Widget _buildDeliveryInfo(Size screenSize, bool isLargeScreen) {
    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is ListTimingSuccess) {
          setState(() {
            _availableTimings = state.data.data?.timings;
          });
        } else if (state is AddTimePreferencesSuccess) {
          // Refresh cart data after updating time preference
          context.read<AccountBloc>().add(ViewCartEvent(widget.chef_id));
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Deliver to',
              style: TextStyle(
                fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: screenSize.height * 0.005),
            Row(
              children: [
                Image.asset(
                  'assets/icons/location.png',
                  width: isLargeScreen ? 18 : screenSize.width * 0.04,
                  height: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
                SizedBox(width: screenSize.width * 0.01),
                Expanded(
                  child: Text(
                    currentAddress ?? '_ _ _',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 16 : screenSize.width * 0.033,
                    ),
                  ),
                ),
                Container(
                  width: 4,
                  height: 4,
                  margin:
                      EdgeInsets.symmetric(horizontal: screenSize.width * 0.02),
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                Image.asset(
                  'assets/icons/timer.png',
                  width: isLargeScreen ? 18 : screenSize.width * 0.04,
                  height: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
                SizedBox(width: screenSize.width * 0.01),
                GestureDetector(
                  onTap: () => _showTimePreferenceModal(
                      context, screenSize, isLargeScreen),
                  child: Row(
                    children: [
                      Text(
                        customerTimePreference ?? '_ _ _',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 16 : screenSize.width * 0.033,
                        ),
                      ),
                      SizedBox(width: screenSize.width * 0.01),
                      Image.asset(
                        'assets/icons/up.png',
                        width: isLargeScreen ? 18 : screenSize.width * 0.04,
                        height: isLargeScreen ? 18 : screenSize.width * 0.04,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTimePreferenceModal(
      BuildContext context, Size screenSize, bool isLargeScreen) {
    context.read<MealplanBloc>().add(ListTimingEvent());

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
            top: Radius.circular(screenSize.width * 0.04)),
      ),
      builder: (modalContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.read<MealplanBloc>()),
        ],
        child: BlocConsumer<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              setState(() {
                _availableTimings = state.data.data?.timings;
              });
            }
          },
          builder: (context, state) {
            if (state is ListTimingLoading) {
              return Container(
                height: 200,
                padding: EdgeInsets.all(screenSize.width * 0.04),
                child: const Center(child: CircularProgressIndicator()),
              );
            }

            return Container(
              padding: EdgeInsets.all(screenSize.width * 0.04),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Select Delivery Time',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 20 : screenSize.width * 0.05,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  SizedBox(height: screenSize.height * 0.02),
                  if (_availableTimings != null &&
                      _availableTimings!.isNotEmpty)
                    Expanded(
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemCount: _availableTimings!.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final timing = _availableTimings![index];
                          final timeSlot =
                              '${_formatTimeToAmPm(timing.startTime)}-${_formatTimeToAmPm(timing.endTime)}';

                          return ListTile(
                            onTap: () {
                              final newTimeSlot =
                                  '${_formatTimeToAmPm(timing.startTime)}-${_formatTimeToAmPm(timing.endTime)}';
                              setState(() {
                                customerTimePreference = newTimeSlot;
                              });
                              context.read<MealplanBloc>().add(
                                    AddTimePreferences(
                                        {"time_preference_id": timing.id}),
                                  );
                              Navigator.pop(context);
                            },
                            title: Text(timeSlot),
                            trailing: customerTimePreference == timeSlot
                                ? const Icon(Icons.check, color: Colors.black)
                                : null,
                          );
                        },
                      ),
                    )
                  else
                    Center(
                      child: Text(
                        'No time slots available',
                        style: TextStyle(
                          fontSize: screenSize.width * 0.04,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
