{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/FLUTTER/db-eats/android/app/.cxx/Debug/5e5g5h4a/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/FLUTTER/db-eats/android/app/.cxx/Debug/5e5g5h4a/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}