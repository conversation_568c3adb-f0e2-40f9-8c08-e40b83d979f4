import 'dart:developer';

import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/data/local/meal_plan_db_helper.dart';
import 'package:db_eats/data/models/meal_plan/meal_plan_db_model.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan/checkout_page_personialized.dart';
import 'package:flutter/material.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectchef.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PersonailizedMealplan extends StatefulWidget {
  final Map<String, dynamic> selectedChef;
  final List<Map<String, dynamic>> selectedMeals;
  final int currentDay;
  final Map<String, Map<String, dynamic>> mealdata;
  final List<String> dates;
  final Map<String, dynamic>? dataresponce;
  final String? timeSlots;

  const PersonailizedMealplan({
    Key? key,
    required this.selectedChef,
    required this.selectedMeals,
    required this.currentDay,
    required this.mealdata,
    required this.dates,
    this.dataresponce,
    this.timeSlots,
  }) : super(key: key);

  @override
  State<PersonailizedMealplan> createState() => _PersonailizedMealplanState();
}

class _PersonailizedMealplanState extends State<PersonailizedMealplan> {
  Map<String, List<MealPlanDbModel>>? _mealPlansByDay;
  late int _currentDay;
  bool _isLoading = false; // Add loading state

  @override
  void initState() {
    super.initState();
    _currentDay = widget.currentDay;
    _loadMealPlansForAllDays();
  }

  Future<void> _loadMealPlansForAllDays() async {
    try {
      // Load all meal plans for this meal plan ID
      final mealPlansByDay = await MealPlanDbHelper.instance
          .getMealPlansByDay(widget.selectedChef['id']);
      setState(() {
        _mealPlansByDay = mealPlansByDay;
      });
    } catch (e) {
      print('Error loading meal plans: $e');
    }
  }

  Future<List<MealPlanDbModel>> _getCurrentDayMealPlans() async {
    if (_mealPlansByDay != null && widget.dates.isNotEmpty) {
      final dayIndex = (_currentDay - 1).clamp(0, widget.dates.length - 1);
      final currentDate = widget.dates[dayIndex];
      return _mealPlansByDay![currentDate] ?? [];
    }
    return await MealPlanDbHelper.instance.getMealPlansByDate(
        widget.selectedChef['id'],
        widget.dates.isNotEmpty
            ? widget.dates[(_currentDay - 1).clamp(0, widget.dates.length - 1)]
            : '');
  }

  String _formatDate(String date) {
    final DateTime dateTime = DateTime.parse(date);
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return "${days[dateTime.weekday - 1]}, ${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}";
  }

  Map<String, dynamic> _formatMealPlanData() {
    List<Map<String, dynamic>> days = [];

    for (String date in widget.dates) {
      if (widget.mealdata.containsKey(date) &&
          widget.mealdata[date]?['chefDetails'] != null &&
          widget.mealdata[date]?['selectedDishes'] != null) {
        final dayData = widget.mealdata[date]!;
        final items = (dayData['selectedDishes'] as List<dynamic>)
            .map((dish) => {"chef_menu_item_id": dish['meal_id']})
            .toList();

        if (items.isNotEmpty) {
          days.add({
            "date": date,
            "chef_id": dayData['chefDetails']['chef_id'],
            "items": items
          });
        }
      }
    }

    return {"meal_plan_id": widget.selectedChef['id'], "days": days};
  }

  Future<Map<String, dynamic>> _getMealPlanDataFromDb() async {
    try {
      return await MealPlanDbHelper.instance
          .getFormattedMealPlanData(widget.selectedChef['id']);
    } catch (e) {
      print('Error getting meal plan data: $e');

      List<Map<String, dynamic>> days = [];

      final allMealPlans = await MealPlanDbHelper.instance
          .getAllMealPlans(widget.selectedChef['id']);

      final mealPlansByDate = <String, List<MealPlanDbModel>>{};
      for (var mealPlan in allMealPlans) {
        if (!mealPlansByDate.containsKey(mealPlan.date)) {
          mealPlansByDate[mealPlan.date] = [];
        }
        mealPlansByDate[mealPlan.date]!.add(mealPlan);
      }

      // Format data for each date
      for (var date in mealPlansByDate.keys) {
        final mealsForDate = mealPlansByDate[date]!;
        if (mealsForDate.isNotEmpty) {
          final chefId = mealsForDate.first.chefId;
          final items = mealsForDate
              .map((meal) => {"chef_menu_item_id": meal.menuItemId})
              .toList();

          days.add({"date": date, "chef_id": chefId, "items": items});
        }
      }

      return {"meal_plan_id": widget.selectedChef['id'], "days": days};
    }
  }

  int get _planDuration =>
      int.tryParse(
          widget.dataresponce?['meal_plan_duration']?.toString() ?? '5') ??
      5;

  Future<void> _handleNavigation(BuildContext context) async {
    try {
      final nextIncompleteDay = await MealPlanDbHelper.instance
          .getNextIncompleteDay(widget.selectedChef['id']);

      if (nextIncompleteDay == null || _currentDay >= _planDuration) {
        final currentDate =
            widget.dates[(_currentDay - 1).clamp(0, widget.dates.length - 1)];
        await MealPlanDbHelper.instance.markDayAsCompleted(
          widget.selectedChef['id'],
          currentDate,
        );

        final mealPlanData = await _getMealPlanDataFromDb();
        log('Final Meal Plan Data:');
        log(mealPlanData.toString());

        context.read<MealplanBloc>().add(Step6MealPlanEvent(mealPlanData));
      } else {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PersonailizedSelectchef(
              mealPlanId: widget.selectedChef['id'],
              currentday: _currentDay + 1,
              dates: widget.dates,
              mealdata: widget.mealdata,
              dataresponce: widget.dataresponce,
              timeSlots: widget.timeSlots,
            ),
          ),
        );
      }
    } catch (e) {
      print('Navigation error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error navigating: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<Map<String, dynamic>?> _getChefForCurrentDay() async {
    try {
      final dayIndex = (_currentDay - 1).clamp(0, widget.dates.length - 1);
      final currentDate = widget.dates[dayIndex];

      // Get meals for the current day
      final meals = await MealPlanDbHelper.instance
          .getMealPlansByDate(widget.selectedChef['id'], currentDate);

      if (meals.isNotEmpty) {
        return {
          'chef_id': meals.first.chefId,
          'name': meals.first.chefName,
          'image': meals.first.chefImage,
        };
      }

      // Fallback to selected chef if no meals found
      return widget.selectedChef;
    } catch (e) {
      print('Error getting chef for current day: $e');
      return widget.selectedChef;
    }
  }

  Future<bool> _hasMealPlansForDay(int day) async {
    if (day < 1 || day > widget.dates.length) return false;

    final dayIndex = (day - 1).clamp(0, widget.dates.length - 1);
    final date = widget.dates[dayIndex];

    final meals = await MealPlanDbHelper.instance
        .getMealPlansByDate(widget.selectedChef['id'], date);

    return meals.isNotEmpty;
  }

  Future<void> _navigateToDay(int day) async {
    if (day < 1 || day > widget.dates.length) return;

    final hasMealPlans = await _hasMealPlansForDay(day);
    if (!hasMealPlans) return;

    setState(() {
      _currentDay = day;
    });
  }

  @override
  Widget build(BuildContext context) {
    final validDay = widget.currentDay.clamp(1, 5);

    return BlocConsumer<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is Step6MealPlanLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is Step6MealPlanSuccess) {
          setState(() {
            _isLoading = false;
          });
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => CheckoutPagePersonialized(
                mealPlanId: widget.selectedChef['id'],
              ),
            ),
          );
        } else if (state is Step6MealPlanFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 0,
            centerTitle: true,
            title: const Text(
              "Meal Plan",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2122),
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.close, color: Color(0xFF1F2122)),
              onPressed: () => Navigator.pop(context),
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1),
              child: Container(
                color: const Color(0xFFE1E3E6),
                height: 1,
              ),
            ),
          ),
          body: FutureBuilder<List<MealPlanDbModel>>(
            future: _getCurrentDayMealPlans(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final mealItems = snapshot.data ?? [];
              double subtotal =
                  mealItems.fold(0, (sum, meal) => sum + meal.price);

              // Generate pricing items dynamically from meal items
              final List<Map<String, dynamic>> pricingItems = mealItems
                  .map((meal) => {
                        'name': meal.itemName,
                        'price': meal.price,
                      })
                  .toList();

              String currentDate = widget.dates.isNotEmpty
                  ? widget.dates[
                      (_currentDay - 1).clamp(0, widget.dates.length - 1)]
                  : DateTime.now().toString().split(' ')[0];

              return Column(
                children: [
                  // Date and chef info
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          _formatDate(currentDate),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                        Row(
                          children: [
                            FutureBuilder<bool>(
                                future: _hasMealPlansForDay(_currentDay - 1),
                                builder: (context, prevDaySnapshot) {
                                  final hasPrevDayData =
                                      prevDaySnapshot.data ?? false;
                                  return (_currentDay > 1 && hasPrevDayData)
                                      ? IconButton(
                                          icon: Transform.rotate(
                                            angle: 3.1416,
                                            child: Image.asset(
                                              'assets/icons/arrow_right.png',
                                              width: 32,
                                              height: 32,
                                              color: null,
                                            ),
                                          ),
                                          onPressed: () =>
                                              _navigateToDay(_currentDay - 1),
                                          constraints: const BoxConstraints(),
                                          padding: EdgeInsets.zero,
                                        )
                                      : SizedBox(width: 32);
                                }),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.access_time, size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    widget.timeSlots ?? "No time selected",
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF414346),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            FutureBuilder<bool>(
                              future: _hasMealPlansForDay(_currentDay + 1),
                              builder: (context, nextDaySnapshot) {
                                final hasNextDayData =
                                    nextDaySnapshot.data ?? false;
                                return (_currentDay < _planDuration &&
                                        hasNextDayData)
                                    ? IconButton(
                                        icon: Image.asset(
                                          'assets/icons/arrow_right.png',
                                          width: 32,
                                          height: 32,
                                          color: null,
                                        ),
                                        onPressed: () =>
                                            _navigateToDay(_currentDay + 1),
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                      )
                                    : SizedBox(width: 32);
                              },
                            ),
                          ],
                        ),
                        // const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FutureBuilder<Map<String, dynamic>?>(
                              future: _getChefForCurrentDay(),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const CircularProgressIndicator(
                                      strokeWidth: 2);
                                }

                                final chefData =
                                    snapshot.data ?? widget.selectedChef;
                                return Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundImage: chefData['image'] !=
                                                  null &&
                                              chefData['image'].isNotEmpty
                                          ? NetworkImage(
                                              ServerHelper.imageUrl +
                                                  chefData['image'],
                                            )
                                          : const AssetImage(
                                                  'assets/images/chef_placeholder.png')
                                              as ImageProvider,
                                      radius: 10,
                                      backgroundColor: Colors.grey[200],
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      chefData['name'] ?? 'Chef',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Divider(
                    height: 1,
                    thickness: 1,
                    color: const Color(0xFFE1E3E6),
                  ),

                  // Meals list
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Meals (${mealItems.length})",
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...mealItems.map((meal) => _buildMealItem({
                              'name': meal.itemName,
                              'photo': meal.itemImage,
                              'price': meal.price,
                              'servings': meal.servings,
                            })),
                      ],
                    ),
                  ),

                  // const Divider(height: 1),

                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        ...pricingItems.map((item) =>
                            _buildPriceRow(item['name'], item['price'])),
                        const SizedBox(height: 16),
                        _buildPriceRow("Subtotal", subtotal, isTotal: true),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
          bottomNavigationBar: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(color: Colors.white),
            child: FutureBuilder<String?>(
              future: MealPlanDbHelper.instance
                  .getNextIncompleteDay(widget.selectedChef['id']),
              builder: (context, snapshot) {
                final nextIncompleteDay = snapshot.data;
                final buttonText = nextIncompleteDay != null &&
                        widget.dates.indexOf(nextIncompleteDay) + 1 <=
                            _planDuration
                    ? "Continue To Day ${widget.dates.indexOf(nextIncompleteDay) + 1} Meals"
                    : "Continue To Checkout";

                return ElevatedButton(
                  onPressed:
                      _isLoading ? null : () => _handleNavigation(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 18),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          buttonText,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                          ),
                        ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildMealItem(Map<String, dynamic> meal) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE1E3E6)),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              ServerHelper.imageUrl + (meal['photo'] ?? ''),
              width: 48,
              height: 48,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 48,
                  height: 48,
                  color: Colors.grey[200],
                  child: const Icon(Icons.restaurant_menu,
                      color: Color(0xFF1F2122)),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      meal['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE1E3E6),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "${meal['servings']}",
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF414346),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  "\$${meal['price'].toStringAsFixed(2)} total for this dish",
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF414346),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String title, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
        ///      fontSize: isTotal ? 16 : 14,
              fontSize:  14,
            ////  fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
          Text(
            "\$${amount.toStringAsFixed(2)}",
            style: TextStyle(
        //      fontSize: isTotal ? 16 : 14,
              fontSize:  12,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }
}
