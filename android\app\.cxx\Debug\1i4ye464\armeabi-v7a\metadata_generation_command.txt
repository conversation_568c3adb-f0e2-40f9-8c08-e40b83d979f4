                        -H/home/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/usr/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Documents/GitHub/db-eats/build/app/intermediates/cxx/Debug/1i4ye464/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Documents/GitHub/db-eats/build/app/intermediates/cxx/Debug/1i4ye464/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Documents/GitHub/db-eats/android/app/.cxx/Debug/1i4ye464/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2