import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/account/listfavouritedishesmodel.dart';
import 'package:db_eats/data/models/chef/listfavouritechefsmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with SingleTickerProviderStateMixin {
  String selectedTab = 'Dishes';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  bool _isAnimating = false;
  String _pendingTab = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
        reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeIn),
      ),
    );

    _animationController.addStatusListener(_handleAnimationStatus);

    // Load initial data after widget is mounted
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (selectedTab == 'Dishes') {
        _loadFavoriteDishes();
      } else {
        _loadFavoriteChefs();
      }
    });
  }

       late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _loadFavoriteDishes() {
    context.read<AccountBloc>().add(ListFavouriteDishes());
  }

  void _loadFavoriteChefs() {
    context.read<AccountBloc>().add(ListFavouriteChefsEvent());
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      setState(() {
        selectedTab = _pendingTab;
        _isAnimating = false;
      });
      _animationController.reverse();

      // Load data for the new tab
      if (selectedTab == 'Dishes') {
        _loadFavoriteDishes();
      } else {
        _loadFavoriteChefs();
      }
    }
  }

  @override
  void dispose() {
    _animationController.removeStatusListener(_handleAnimationStatus);
    _animationController.dispose();
    super.dispose();
  }

  void _switchTab(String tab) {
    if (selectedTab != tab && !_isAnimating) {
      setState(() {
        _isAnimating = true;
        _pendingTab = tab;
      });
      _animationController.forward();
    }
  }

  void _handleSwipe(DragEndDetails details, Size screenSize) {
    if (details.primaryVelocity != null &&
        details.primaryVelocity!.abs() > screenSize.width * 0.5) {
      // Lowered threshold
      if (details.primaryVelocity! > 0) {
        if (selectedTab == 'Chefs') {
          _switchTab('Dishes');
        }
      } else {
        if (selectedTab == 'Dishes') {
          _switchTab('Chefs');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    return Scaffold(
      backgroundColor: const Color(0xFFf6f3ec),
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            size: isLargeScreen ? 28 : screenSize.width * 0.06,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        scrolledUnderElevation: 0,
        backgroundColor: const Color(0xFFf6f3ec),
        elevation: 0,
      ),
      body: SafeArea(
        child: BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is AddToCartSuccess) {
              // Navigator.of(context).pop(); // Close serving size dialog if open
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.message,
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  backgroundColor: Colors.black,
                ),
              );
              // Refresh the favorites list
              _loadFavoriteDishes();
            } else if (state is AddToCartFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.message,
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: screenSize.width * 0.04),
                child: Text(
                  'Favorites',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize:
                        isLargeScreen ? 32 : screenSize.width * 0.0467 // ≈ 18
                    ,
                  ),
                ),
              ),
              SizedBox(height: screenSize.height * 0.02),
              _buildTabs(screenSize, isLargeScreen),
              SizedBox(height: screenSize.height * 0.02),
              Expanded(
                child: GestureDetector(
                  onHorizontalDragEnd: (details) =>
                      _handleSwipe(details, screenSize),
                  child: AnimatedBuilder(
                    animation: _fadeAnimation,
                    builder: (context, child) {
                      return Opacity(
                        opacity: _isAnimating ? _fadeAnimation.value : 1.0,
                        child: _buildCardsList(screenSize, isLargeScreen),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabs(Size screenSize, bool isLargeScreen) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
      child: Row(
        children: [
          Expanded(child: _buildTabItem('Dishes', screenSize, isLargeScreen)),
          Expanded(child: _buildTabItem('Chefs', screenSize, isLargeScreen)),
        ],
      ),
    );
  }

  Widget _buildTabItem(String title, Size screenSize, bool isLargeScreen) {
    final isSelected = selectedTab == title || _pendingTab == title;

    return GestureDetector(
      onTap: () => _switchTab(title),
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(top: screenSize.height * 0.005),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                color: isSelected
                    ? const Color(0xFF1F2122)
                    : const Color(0xFF66696D),
              ),
            ),
            SizedBox(height: screenSize.height * 0.005),
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0.0,
                end: isSelected ? 1.0 : 0.0,
              ),
              builder: (context, value, child) {
                return Container(
                  height: 2,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Color.lerp(
                      Colors.transparent,
                      const Color(0xFFFFBE16),
                      value,
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardsList(Size screenSize, bool isLargeScreen) {
    return selectedTab == 'Dishes'
        ? _buildDishesTab(screenSize, isLargeScreen)
        : _buildChefsTab(screenSize, isLargeScreen);
  }

  Widget _buildDishesTab(Size screenSize, bool isLargeScreen) {
    return BlocBuilder<AccountBloc, AccountState>(
      builder: (context, state) {
        if (state is ListFavouriteDishesLoading) {
          return Center(
            child: CircularProgressIndicator(
              color: const Color(0xFFFFBE16),
              strokeWidth: isLargeScreen ? 5 : screenSize.width * 0.01,
            ),
          );
        } else if (state is ListFavouriteDishesSuccess) {
          List<FavouriteDishData> dishes = state.data;

          if (dishes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: isLargeScreen ? 80 : screenSize.width * 0.15,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: screenSize.height * 0.02),
                  Text(
                    'No favorite dishes found',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
            itemCount: dishes.length,
            itemBuilder: (context, index) {
              return AnimatedCard(
                delay: Duration(milliseconds: 50 * index),
                child: Padding(
                  padding: EdgeInsets.only(bottom: screenSize.height * 0.02),
                  child: _buildDishCardFromAPI(
                      dishes[index], screenSize, isLargeScreen, index),
                ),
              );
            },
          );
        } else if (state is ListFavouriteDishesFailed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: isLargeScreen ? 80 : screenSize.width * 0.15,
                  color: Colors.grey[400],
                ),
                SizedBox(height: screenSize.height * 0.02),
                Text(
                  'Error loading favorite dishes',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: screenSize.height * 0.01),
                Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenSize.height * 0.02),
                ElevatedButton(
                  onPressed: _loadFavoriteDishes,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFBE16),
                    foregroundColor: Colors.black,
                    padding: EdgeInsets.symmetric(
                      horizontal: screenSize.width * 0.05,
                      vertical: screenSize.height * 0.015,
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildChefsTab(Size screenSize, bool isLargeScreen) {
    return BlocBuilder<AccountBloc, AccountState>(
      builder: (context, state) {
        if (state is ListFavouriteChefsLoading) {
          return Center(
            child: CircularProgressIndicator(
              color: const Color(0xFFFFBE16),
              strokeWidth: isLargeScreen ? 5 : screenSize.width * 0.01,
            ),
          );
        } else if (state is ListFavouriteChefsSuccess) {
          List<FavouriteChefData> chefs = [];

          if (state.data is ListFavouriteChefsModel) {
            final model = state.data as ListFavouriteChefsModel;
            chefs = model.data?.cast<FavouriteChefData>() ?? [];
          } else if (state.data is Map<String, dynamic>) {
            final model = ListFavouriteChefsModel.fromJson(state.data);
            chefs = model.data?.cast<FavouriteChefData>() ?? [];
          } else if (state.data is List) {
            chefs = (state.data as List)
                .map((chef) => chef is FavouriteChefData
                    ? chef
                    : FavouriteChefData.fromJson(chef))
                .toList();
          }

          if (chefs.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: isLargeScreen ? 80 : screenSize.width * 0.15,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: screenSize.height * 0.02),
                  Text(
                    'No favorite chefs found',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
            itemCount: chefs.length,
            itemBuilder: (context, index) {
              return AnimatedCard(
                delay: Duration(milliseconds: 50 * index),
                child: Padding(
                  padding: EdgeInsets.only(bottom: screenSize.height * 0.02),
                  child: _buildChefCardFromAPI(
                      chefs[index], screenSize, isLargeScreen),
                ),
              );
            },
          );
        } else if (state is ListFavouriteChefsFailed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: isLargeScreen ? 80 : screenSize.width * 0.15,
                  color: Colors.grey[400],
                ),
                SizedBox(height: screenSize.height * 0.02),
                Text(
                  'Error loading favorite chefs',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: screenSize.height * 0.01),
                Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenSize.height * 0.02),
                ElevatedButton(
                  onPressed: _loadFavoriteChefs,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFBE16),
                    foregroundColor: Colors.black,
                    padding: EdgeInsets.symmetric(
                      horizontal: screenSize.width * 0.05,
                      vertical: screenSize.height * 0.015,
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildDishCardFromAPI(
      FavouriteDishData dish, Size screenSize, bool isLargeScreen, int index) {
    String getFullImageUrl(String? imageName) {
      if (imageName == null || imageName.isEmpty) return '';
      return '${ServerHelper.imageUrl}/$imageName';
    }

    final List<Map<String, int>> staticDishData = [
      {'likes': 82, 'reviews': 49},
      {'likes': 78, 'reviews': 32},
      {'likes': 90, 'reviews': 61},
    ];

    final int likes = staticDishData[index % staticDishData.length]['likes']!;
    final int reviews =
        staticDishData[index % staticDishData.length]['reviews']!;

    // Helper functions to get serving size and price info
    String getServingSizeText() {
      if (dish.servingSizePrices == null || dish.servingSizePrices!.isEmpty) {
        return 'N/A Servings';
      }

      // If only one serving size, show its title
      if (dish.servingSizePrices!.length == 1) {
        final servingSize = dish.servingSizePrices!.first.servingSize;
        if (servingSize?.title != null && servingSize!.title!.isNotEmpty) {
          return servingSize.title!;
        }
        return '${servingSize?.serves ?? 1} Servings';
      }

      // Multiple serving sizes - show all options or summary
      final servingSizes = dish.servingSizePrices!
          .map((sp) => sp.servingSize?.title)
          .where((title) => title != null && title.isNotEmpty)
          .toList();

      if (servingSizes.isNotEmpty) {
        // If we have 2 or less, show them all
        if (servingSizes.length <= 2) {
          return servingSizes.join(', ');
        } else {
          // More than 2, show count
          return '${servingSizes.length} size options';
        }
      }

      return '${dish.servingSizePrices!.length} options';
    }

    String getPriceText() {
      if (dish.servingSizePrices == null || dish.servingSizePrices!.isEmpty) {
        return 'N/A';
      }

      if (dish.servingSizePrices!.length == 1) {
        return '\$${dish.servingSizePrices!.first.price ?? '0'}';
      } else {
        // Multiple prices - show range
        final prices = dish.servingSizePrices!
            .map((sp) => double.tryParse(sp.price ?? '0') ?? 0)
            .where((price) => price > 0)
            .toList();

        if (prices.isEmpty) return 'N/A';

        prices.sort();
        if (prices.first == prices.last) {
          return '\$${prices.first.toStringAsFixed(2)}';
        } else {
          return '\$${prices.first.toStringAsFixed(2)} - \$${prices.last.toStringAsFixed(2)}';
        }
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dish image
          Expanded(
            flex: 3,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
              child: dish.dishPhoto != null && dish.dishPhoto!.isNotEmpty
                  ? Image.network(
                      getFullImageUrl(dish.dishPhoto),
                      fit: BoxFit.cover,
                      height: isLargeScreen ? 180 : screenSize.height * 0.165,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height:
                              isLargeScreen ? 180 : screenSize.height * 0.18,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.restaurant,
                            size: isLargeScreen ? 48 : screenSize.width * 0.1,
                            color: Colors.grey,
                          ),
                        );
                      },
                    )
                  : Container(
                      height: isLargeScreen ? 180 : screenSize.height * 0.18,
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.restaurant,
                        size: isLargeScreen ? 48 : screenSize.width * 0.1,
                        color: Colors.grey,
                      ),
                    ),
            ),
          ),
          // Content
          Expanded(
            flex: 6,
            child: Padding(
              padding: EdgeInsets.only(
                left: screenSize.width * 0.035,
                right: screenSize.width * 0.035,
                top: screenSize.height * 0.015,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Dish name
                  Text(
                    dish.dishName ?? 'Unknown Dish',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: isLargeScreen ? 20 : screenSize.width * 0.04,
                    ),
                  ),
                  SizedBox(height: screenSize.height * 0.008),
                  // Servings and likes
                  Wrap(
                    spacing: screenSize.width * 0.02,
                    runSpacing: screenSize.height * 0.01,
                    children: [
                      // Serving size info
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenSize.width * 0.015,
                          vertical: screenSize.height * 0.002,
                        ),
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(225, 227, 230, 1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          getServingSizeText(),
                          style: TextStyle(
                            fontSize:
                                isLargeScreen ? 14 : screenSize.width * 0.03,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            letterSpacing: 0.2,
                          ),
                        ),
                      ),
                      // Likes container
                      // Container(
                      //   padding: EdgeInsets.symmetric(
                      //     horizontal: screenSize.width * 0.015,
                      //     vertical: screenSize.height * 0.002,
                      //   ),
                      //   decoration: BoxDecoration(
                      //     color: const Color.fromRGBO(225, 227, 230, 1),
                      //     borderRadius: BorderRadius.circular(12),
                      //   ),
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     children: [
                      //       Image.asset(
                      //         'assets/icons/thump.png',
                      //         width:
                      //             isLargeScreen ? 13 : screenSize.width * 0.03,
                      //         height:
                      //             isLargeScreen ? 12 : screenSize.width * 0.025,
                      //         color: Colors.black54,
                      //       ),
                      //       SizedBox(width: screenSize.width * 0.01),
                      //       Text(
                      //         "$likes%",
                      //         style: TextStyle(
                      //           fontSize: isLargeScreen
                      //               ? 14
                      //               : screenSize.width * 0.03,
                      //           fontWeight: FontWeight.w500,
                      //           fontFamily: 'Inter',
                      //         ),
                      //       ),
                      //       SizedBox(width: screenSize.width * 0.005),
                      //       Text(
                      //         "($reviews)",
                      //         style: TextStyle(
                      //           fontSize: isLargeScreen
                      //               ? 14
                      //               : screenSize.width * 0.03,
                      //           fontWeight: FontWeight.w500,
                      //           fontFamily: 'Inter',
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: screenSize.height * 0.009),
                  // Chef info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: isLargeScreen ? 16 : screenSize.width * 0.035,
                        backgroundImage:
                            dish.chefPhoto != null && dish.chefPhoto!.isNotEmpty
                                ? NetworkImage(getFullImageUrl(dish.chefPhoto))
                                : null,
                        child: dish.chefPhoto == null || dish.chefPhoto!.isEmpty
                            ? Icon(
                                Icons.person,
                                size: isLargeScreen
                                    ? 16
                                    : screenSize.width * 0.035,
                              )
                            : null,
                      ),
                      SizedBox(width: screenSize.width * 0.03),
                      Text(
                        dish.chefName ?? 'Unknown Chef',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize:
                              isLargeScreen ? 16 : screenSize.width * 0.035,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenSize.height * 0.01),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getPriceText(),
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 18 : screenSize.width * 0.035,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => _handleAddDish(dish),
                        child: Image.asset(
                          'assets/icons/add.png',
                          width: isLargeScreen ? 36 : screenSize.width * 0.08,
                          height: isLargeScreen ? 36 : screenSize.width * 0.08,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChefCardFromAPI(
      FavouriteChefData chef, Size screenSize, bool isLargeScreen) {
    String getFullImageUrl(String? imageName) {
      if (imageName == null || imageName.isEmpty) return '';
      return '${ServerHelper.imageUrl}/$imageName';
    }

    // Debug print to check the chef photo URL
    print('Chef photo URL: ${getFullImageUrl(chef.chefPhoto)}');
    print('Dish photo URL: ${getFullImageUrl(chef.dishPhoto)}');

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dish image
          Expanded(
            flex: 2,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
              child: chef.dishPhoto != null && chef.dishPhoto!.isNotEmpty
                  ? Image.network(
                      getFullImageUrl(chef.dishPhoto),
                      fit: BoxFit.cover,
                      height: isLargeScreen ? 180 : screenSize.height * 0.165,
                      errorBuilder: (context, error, stackTrace) {
                        print('Error loading dish image: $error');
                        return Container(
                          height:
                              isLargeScreen ? 180 : screenSize.height * 0.18,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.restaurant,
                            size: isLargeScreen ? 48 : screenSize.width * 0.1,
                            color: Colors.grey,
                          ),
                        );
                      },
                    )
                  : Container(
                      height: isLargeScreen ? 180 : screenSize.height * 0.18,
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.restaurant,
                        size: isLargeScreen ? 48 : screenSize.width * 0.1,
                        color: Colors.grey,
                      ),
                    ),
            ),
          ),
          // Content
          Expanded(
            flex: 5,
            child: Padding(
              padding: EdgeInsets.only(
                left: screenSize.width * 0.035,
                right: screenSize.width * 0.035,
                top: screenSize.height * 0.015,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Chef profile image and name
                  Row(
                    children: [
                      CircleAvatar(
                        radius: isLargeScreen ? 16 : screenSize.width * 0.035,
                        backgroundImage:
                            chef.chefPhoto != null && chef.chefPhoto!.isNotEmpty
                                ? NetworkImage(getFullImageUrl(chef.chefPhoto))
                                : null,
                        child: chef.chefPhoto == null || chef.chefPhoto!.isEmpty
                            ? Icon(
                                Icons.person,
                                size: isLargeScreen
                                    ? 16
                                    : screenSize.width * 0.035,
                              )
                            : null,
                      ),
                      SizedBox(width: screenSize.width * 0.03),
                      Text(
                        chef.chefName ?? 'Unknown Chef',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize:
                              isLargeScreen ? 18 : screenSize.width * 0.04,
                        ),
                      ),
                    ],
                  ),
                  // SizedBox(height: screenSize.height * 0.01),
                  SizedBox(height: screenSize.height * 0.005),
                  // Tags
                  if (chef.chefSearchTags != null &&
                      chef.chefSearchTags!.isNotEmpty)
                    Text(
                      chef.chefSearchTags!.join(', '),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isLargeScreen ? 15 : screenSize.width * 0.027,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF66696D),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  SizedBox(height: screenSize.height * 0.01),

                  // Rating, Time, Distance
                  Row(
                    children: [
                      // Rating
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenSize.width * 0.02,
                          vertical: screenSize.height * 0.005,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1E3E6),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${((chef.chefRatingPercentage ?? 4.5) * 20).round()}%',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: isLargeScreen
                                    ? 14
                                    : screenSize.width * 0.027,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(width: screenSize.width * 0.01),
                            Text(
                              '(${chef.chefTotalRatings ?? 49})',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: isLargeScreen
                                    ? 14
                                    : screenSize.width * 0.027,
                                fontWeight: FontWeight.w400,
                                color: const Color(0xFF66696D),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: screenSize.width * 0.02),
                      // Time
                      Icon(
                        Icons.access_time,
                        size: isLargeScreen ? 16 : screenSize.width * 0.035,
                        color: const Color(0xFF66696D),
                      ),
                      SizedBox(width: screenSize.width * 0.01),
                      Text(
                        '${chef.preparationTime ?? 35} mins',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize:
                              isLargeScreen ? 14 : screenSize.width * 0.027,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF66696D),
                        ),
                      ),
                      if (chef.distanceKm != null) ...[
                        SizedBox(width: screenSize.width * 0.02),
                        const Text(
                          '•',
                          style: TextStyle(
                            color: Color(0xFF66696D),
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(width: screenSize.width * 0.02),
                        Icon(
                          Icons.location_on_outlined,
                          size: isLargeScreen ? 16 : screenSize.width * 0.035,
                          color: const Color(0xFF66696D),
                        ),
                        SizedBox(width: screenSize.width * 0.01),
                        Text(
                          '${chef.distanceKm!.toStringAsFixed(1)} KM',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize:
                                isLargeScreen ? 14 : screenSize.width * 0.027,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF66696D),
                          ),
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: screenSize.height * 0.01),
                  // Operation days
                  if (chef.operationDays != null &&
                      chef.operationDays!.isNotEmpty)
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Image.asset(
                          'assets/icons/calender_2.png',
                          width: isLargeScreen ? 16 : screenSize.width * 0.035,
                          height: isLargeScreen ? 16 : screenSize.width * 0.035,
                        ),
                        SizedBox(width: screenSize.width * 0.015),
                        Expanded(
                          child: Text(
                            chef.operationDays!.map((day) {
                              switch (day.toLowerCase()) {
                                case 'monday':
                                  return 'M';
                                case 'tuesday':
                                  return 'T';
                                case 'wednesday':
                                  return 'W';
                                case 'thursday':
                                  return 'T';
                                case 'friday':
                                  return 'F';
                                case 'saturday':
                                  return 'S';
                                case 'sunday':
                                  return 'S';
                                default:
                                  return day.substring(0, 1).toUpperCase();
                              }
                            }).join(', '),
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w500,
                              fontSize:
                                  isLargeScreen ? 14 : screenSize.width * 0.027,
                              color: const Color(0xFF1F2122),
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleAddDish(FavouriteDishData dish) {
    if (dish.servingSizePrices != null && dish.servingSizePrices!.length > 1) {
      _showServingSizeSelection(dish);
    } else if (dish.servingSizePrices != null &&
        dish.servingSizePrices!.isNotEmpty) {
      _addToCart(dish, dish.servingSizePrices!.first);
    } else {
      _showMessage('No serving size options available', isError: true);
    }
  }

  void _showServingSizeSelection(FavouriteDishData dish) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final bottomPadding = MediaQuery.of(context).viewInsets.bottom;
    ServingSizePrice? selectedServingSize;

    // Calculate dynamic dimensions
    final double headerImageSize =
        isTablet ? screenSize.width * 0.1 : screenSize.width * 0.15;
    final double titleFontSize =
        isTablet ? screenSize.width * 0.03 : screenSize.width * 0.045;
    final double subtitleFontSize =
        isTablet ? screenSize.width * 0.02 : screenSize.width * 0.035;
    final double optionFontSize =
        isTablet ? screenSize.width * 0.018 : screenSize.width * 0.04;
    final double verticalPadding = screenSize.height * 0.02;
    final double horizontalPadding = screenSize.width * 0.04;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) {
          return Container(
            padding: EdgeInsets.only(bottom: bottomPadding),
            constraints: BoxConstraints(
              maxHeight: screenSize.height * 0.85,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isTablet ? 30 : 20),
                topRight: Radius.circular(isTablet ? 30 : 20),
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: horizontalPadding,
                  vertical: verticalPadding,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      width: screenSize.width * 0.1,
                      height: 4,
                      margin: EdgeInsets.only(bottom: verticalPadding),
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Dish info header
                    Container(
                      margin: EdgeInsets.only(bottom: verticalPadding * 1.5),
                      child: Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              '${ServerHelper.imageUrl}/${dish.dishPhoto}',
                              width: headerImageSize,
                              height: headerImageSize,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                width: headerImageSize,
                                height: headerImageSize,
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.restaurant,
                                  size: headerImageSize * 0.5,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: horizontalPadding),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  dish.dishName ?? 'Unknown Dish',
                                  style: TextStyle(
                                    fontSize: titleFontSize,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'Inter',
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: verticalPadding * 0.3),
                                Text(
                                  'by ${dish.chefName ?? 'Unknown Chef'}',
                                  style: TextStyle(
                                    fontSize: subtitleFontSize,
                                    color: Colors.grey[600],
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: verticalPadding),

                    Text(
                      'Select Serving Size',
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                      ),
                    ),
                    SizedBox(height: verticalPadding * 0.5),

                    // Serving size options with selection
                    ...dish.servingSizePrices!.map(
                      (servingSizePrice) => Container(
                        margin:
                            EdgeInsets.only(bottom: screenSize.height * 0.015),
                        child: InkWell(
                          onTap: () {
                            setModalState(() {
                              selectedServingSize = servingSizePrice;
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: screenSize.width * 0.04,
                              vertical: screenSize.height * 0.02,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: selectedServingSize?.id ==
                                        servingSizePrice.id
                                    ? const Color(0xFFFFBE16)
                                    : Colors.grey[300]!,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              color:
                                  selectedServingSize?.id == servingSizePrice.id
                                      ? const Color(0xFFFFFBF2)
                                      : Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  servingSizePrice.servingSize?.title ??
                                      'Unknown Size',
                                  style: TextStyle(
                                    fontSize: optionFontSize,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                    color: selectedServingSize?.id ==
                                            servingSizePrice.id
                                        ? Colors.black
                                        : Colors.grey[700],
                                  ),
                                ),
                                Text(
                                  '\$${servingSizePrice.price ?? '0'}',
                                  style: TextStyle(
                                    fontSize: optionFontSize,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: verticalPadding * 1.5),

                    // Action Buttons
                    Row(
                      children: [
                        // Cancel Button
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                vertical: screenSize.height * 0.02,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                fontSize: isTablet ? 18 : 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[700],
                                fontFamily: 'Inter',
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: screenSize.width * 0.03),
                        // Add to Cart Button
                        Expanded(
                          child: ElevatedButton(
                            onPressed: selectedServingSize != null
                                ? () {
                                    Navigator.pop(context);
                                    _addToCart(dish, selectedServingSize!);
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFFFBE16),
                              foregroundColor: Colors.black,
                              disabledBackgroundColor: Colors.grey[300],
                              padding: EdgeInsets.symmetric(
                                vertical: screenSize.height * 0.02,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Add to Cart',
                              style: TextStyle(
                                fontSize: isTablet ? 18 : 16,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: screenSize.height * 0.02),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _addToCart(FavouriteDishData dish, ServingSizePrice servingSizePrice) {
    context.read<AccountBloc>().add(AddToCartEvent({
          'chef_id': dish.chefId,
          'chef_dish_id': dish.dishId,
          'quantity': 1,
          'serving_size_id': servingSizePrice.servingSizeId,
        }));
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isError ? Colors.red : Colors.black,
      ),
    );
  }
}

class AnimatedCard extends StatefulWidget {
  final Widget child;
  final Duration delay;

  const AnimatedCard({
    super.key,
    required this.child,
    this.delay = Duration.zero,
  });

  @override
  State<AnimatedCard> createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<AnimatedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.25),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ),
    );

    Future.delayed(widget.delay, () {
      if (mounted) {
        setState(() {
          _isVisible = true;
        });
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _opacityAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: widget.child,
      ),
    );
  }
}
