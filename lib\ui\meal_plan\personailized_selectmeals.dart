import 'dart:developer';

import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/meal_plan/filtereddishmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan/checkout_page.dart';
import 'package:db_eats/ui/meal_plan/checkout_page_personialized.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectchef.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:shimmer/shimmer.dart';

class MealItem {
  final int mealId;
  final String name;
  final String photo;
  final double price;
  final String servingSize;
  final int? dayId; // Added dayId field

  MealItem({
    required this.mealId,
    required this.name,
    required this.photo,
    required this.price,
    required this.servingSize,
    this.dayId, // Optional dayId parameter
  });

  Map<String, dynamic> toMap() {
    return {
      'meal_id': mealId,
      'name': name,
      'photo': photo,
      'price': price,
      'servings': servingSize.split(' ').first,
    };
  }
}

class PersonailizedSelectmeals extends StatefulWidget {
  final int id;
  final int selectedDay;
  final String deliveryTime;
  final String selectedDate;
  final int mealPlanId;
  final int servingSizeId;
  final int maxDishesPerDay;
  final Map<String, Map<String, dynamic>> mealData;
  final List<String> dates;
  final Map<String, dynamic>? dataresponce;
  final String? timeSlots;
  final double distance;
  final bool isEditing;
  final int? editDayId;
  final Map<String, dynamic>? viewDayData;

  const PersonailizedSelectmeals({
    super.key,
    required this.id,
    required this.selectedDay,
    required this.deliveryTime,
    required this.selectedDate,
    required this.mealPlanId,
    required this.servingSizeId,
    required this.maxDishesPerDay,
    required this.mealData,
    required this.dates,
    this.dataresponce,
    this.timeSlots,
    required this.distance,
    this.isEditing = false,
    this.editDayId,
    this.viewDayData,
  });

  @override
  State<PersonailizedSelectmeals> createState() =>
      _PersonailizedSelectmealsState();
}

class _PersonailizedSelectmealsState extends State<PersonailizedSelectmeals>
    with TickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  FilteredDishesModel? dishesData;
  late TabController _tabController;
  List<String> _categories = [];
  String _deliveryTime = '';
  String _startDate = '';
  String _endDate = '';
  String _planduration = '5';
  String _displayStartDate = '';
  bool _isLoading = true;
  int _currentDay = 0;
  int _mealPlanDuration = 5;
  List<String> _workingDays = [];

  List<MealItem> selectedMeals = [];



  Color kBlack = const Color(0xFF1F2122);
  Color kSecondBlack = const Color(0xFF414346);

  int _maxDishesPerDay = 1; // Default to 1 instead of using late
  int _maxDishes = 1; // Default to 1 instead of using late

  @override
  void initState() {
    super.initState();
    // Initialize dates using the selected date from chef selection
    _displayStartDate = widget.selectedDate;
    _currentDay = widget.selectedDay;

    _categories = ['All'];

    _tabController = TabController(
      length: _categories.length,
      vsync: this,
    );

    // Initialize max dishes values
    if (widget.dataresponce != null) {
      log(widget.dataresponce.toString());
      _maxDishesPerDay = widget.dataresponce!['dishes_per_day'] ?? 1;
    } else {
      _maxDishesPerDay = widget.maxDishesPerDay > 0 ? widget.maxDishesPerDay : 1;
    }
  
    _maxDishes = _maxDishesPerDay;

    // Override max dishes per day in edit mode with the number of items from the response
    if (widget.isEditing && widget.viewDayData != null && widget.viewDayData!['items'] != null) {
      final items = widget.viewDayData!['items'] as List?;
      if (items != null) {
        _maxDishesPerDay = items.length;
        _maxDishes = items.length;
      }
    }

    if (widget.dataresponce != null) {
      _startDate =
          widget.dataresponce!['start_date'] ?? DateTime.now().toString();
      _endDate = widget.dataresponce!['end_date'] ?? '';
      _planduration =
          widget.dataresponce!['meal_plan_duration']?.toString() ?? '5';
      _mealPlanDuration = int.parse(_planduration);

      // Use dates passed from chef selection
      _workingDays = widget.dates;

      if (_startDate.isNotEmpty) {
        _validateDateRange();
        // Always use selectedDay for current day
        if (_workingDays.isNotEmpty &&
            widget.selectedDay > 0 &&
            widget.selectedDay <= _workingDays.length) {
          _currentDay = widget.selectedDay;
          _displayStartDate = _workingDays[_currentDay - 1];
        } else {
          _currentDay = 1;
          _displayStartDate = _startDate;
        }
      }
    } else {
      _startDate = _formatStorageDate(DateTime.now());
      _planduration = '5';
      _mealPlanDuration = 5;
      _workingDays = _generateWorkingDays(_startDate, _mealPlanDuration);
      _displayStartDate =
          _workingDays.isNotEmpty ? _workingDays[0] : _startDate;
    }

    // Initialize selected meals from viewDayData if available
    if (widget.viewDayData != null && widget.isEditing) {
      final items = widget.viewDayData!['items'] as List?;
      if (items != null) {
        // Only initialize selected meals if the chef ID matches
        if (widget.viewDayData!['chef']?['id'] == widget.id) {
          selectedMeals = items
              .map((item) => MealItem(
                    mealId: item['chef_menu_item_id'],
                    name: item['menu_item']['name'],
                    photo: item['menu_item']['photo'],
                    price: double.parse(item['price'].toString()),
                    servingSize:
                        '1 Serving', // Use actual serving size if available
                    dayId: widget.editDayId,
                  ))
              .toList();
        }
        // If chef ID doesn't match, leave selectedMeals empty
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<HomeBloc>();
      bloc.add(ViewChefDetailsEvent(chefId: widget.id));
    });
  }

   late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _navigateBack() {
    Navigator.pop(context);
  }

  void _saveMealSelection() {
    // Check if user has selected the required number of dishes
    if (selectedMeals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one meal')),
      );
      return;
    }

    // Add validation to ensure user has selected the maximum required dishes
    if (selectedMeals.length < _maxDishesPerDay && _maxDishesPerDay > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Please select $_maxDishesPerDay dish${_maxDishesPerDay > 1 ? 'es' : ''} to continue'),
        ),
      );
      return;
    }

    final chefData = {
      'chef_id': widget.id,
      'name':
          '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}'
              .trim(),
      'image': chefDetails?.data?.chef?.profilePhoto ?? '',
      'tags': chefDetails?.data?.chef?.searchTags ?? [],
      'date': widget.selectedDate,
      'rating': '82% (49)',
      'selectedDishes': selectedMeals.map((meal) => meal.toMap()).toList(),
    };

    _handleContinue();
  }

  void _toggleMealSelection(Map<String, dynamic> dish) {
    // Ensure _maxDishesPerDay is at least 1 to prevent unlimited selection
    final effectiveMaxDishes = _maxDishesPerDay > 0 ? _maxDishesPerDay : 1;

    setState(() {
      final existingIndex =
          selectedMeals.indexWhere((meal) => meal.mealId == dish['id']);

      if (existingIndex != -1) {
        // Always allow unselecting
        selectedMeals.removeAt(existingIndex);
      } else if (selectedMeals.length < effectiveMaxDishes) {
        // Add new meal if under limit
        selectedMeals.add(MealItem(
          mealId: dish['id'],
          name: dish['name'],
          photo: dish['photo'] ?? '',
          price: double.parse(dish['price'].toString()),
          servingSize: dish['serving_size'] ?? '',
          dayId: widget.editDayId,
        ));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'You can only select $effectiveMaxDishes dish${effectiveMaxDishes > 1 ? 'es' : ''} per day. Please unselect a dish first.'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }

  String _formatDate(String date) {
    if (date.isEmpty) return "Loading...";
    try {
      final DateTime dateTime = DateTime.parse(date);
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return "${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}, ${days[dateTime.weekday - 1]}";
    } catch (e) {
      return "Invalid Date";
    }
  }

  List<String> _generateWorkingDays(String startDate, int planDuration) {
    if (startDate.isEmpty) {
      return List.filled(planDuration, _formatStorageDate(DateTime.now()));
    }
    List<String> dates = [];
    try {
      DateTime currentDate = DateTime.parse(startDate);
      while (dates.length < planDuration) {
        if (currentDate.weekday != DateTime.saturday &&
            currentDate.weekday != DateTime.sunday) {
          dates.add(_formatStorageDate(currentDate));
        }
        currentDate = currentDate.add(const Duration(days: 1));
      }
    } catch (e) {
      print("Error generating working days: $e");
      return List.filled(planDuration, _formatStorageDate(DateTime.now()));
    }
    return dates;
  }

  String _formatStorageDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  void _validateDateRange() {
    if (_startDate.isEmpty || _endDate.isEmpty) return;
    try {
      DateTime start = DateTime.parse(_startDate);
      DateTime end = DateTime.parse(_endDate);
      int workingDays = 0;
      DateTime current = start;
      while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
        if (current.weekday != DateTime.saturday &&
            current.weekday != DateTime.sunday) {
          workingDays++;
        }
        current = current.add(const Duration(days: 1));
      }
      if (workingDays != _mealPlanDuration) {
        print(
            "Warning: Date range has $workingDays working days, expected $_mealPlanDuration");
        _workingDays = _generateWorkingDays(_startDate, _mealPlanDuration);
        _endDate = _workingDays.isNotEmpty ? _workingDays.last : _startDate;
      }
    } catch (e) {
      print("Error validating date range: $e");
      _workingDays = _generateWorkingDays(_startDate, _mealPlanDuration);
      _endDate = _workingDays.isNotEmpty ? _workingDays.last : _startDate;
    }
  }

  bool _isDayCompleted(String date) {
    final mealData = widget.mealData[date];
    return mealData != null &&
        mealData['selectedDishes'] != null &&
        (mealData['selectedDishes'] as List).isNotEmpty;
  }

  Widget _buildDateSelector() {
    return Container(
      padding:  EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            size: sixteen, color: kSecondBlack),
                         SizedBox(width: sixteen/2),
                        Text(
                          _displayStartDate.isNotEmpty
                              ? _formatDate(_displayStartDate)
                              : "Loading...",
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: kBlack,
                          ),
                        ),
                      ],
                    ),
                     SizedBox(height: sixteen/4),
                    // Show time slot from view day response when in edit mode
                    if (widget.isEditing && widget.viewDayData != null && widget.viewDayData!['time_slot'] != null)
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                           SizedBox(width: sixteen/2),
                          Text(
                            "${_formatTimeToAmPm(widget.viewDayData!['time_slot']['start_time'])} - ${_formatTimeToAmPm(widget.viewDayData!['time_slot']['end_time'])}",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      )
                    else
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                           SizedBox(width: sixteen/2),
                          Text(
                            widget.timeSlots ?? "twelve:00PM-1:00PM",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children:
                    List.generate(int.parse(_planduration) * 2 - 1, (index) {
                  if (index.isOdd) {
                    return Container(
                      width: sixteen,
                      height: 1,
                      color: const Color(0xFFE1DDD5),
                    );
                  } else {
                    final dayNumber = (index ~/ 2) + 1;
                    final isSelected = dayNumber == _currentDay;
                    final isCompleted = index ~/ 2 < _workingDays.length &&
                        _isDayCompleted(_workingDays[index ~/ 2]);
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _currentDay = dayNumber;
                          if (_workingDays.isNotEmpty &&
                              dayNumber > 0 &&
                              dayNumber <= _workingDays.length) {
                            _displayStartDate = _workingDays[dayNumber - 1];
                          } else {
                            _displayStartDate = _startDate;
                          }
                        });

                        if (_displayStartDate.isNotEmpty) {
                          context.read<MealplanBloc>().add(ListFilterdDishes({
                                "chef_id": widget.id,
                                "serving_size_id": widget.servingSizeId,
                                "time_slot_id":
                                    widget.dataresponce?['time_slot_id'],
                                "date": _displayStartDate,
                              }));
                        }
                      },
                      child: Container(
                        width: ten*2.2,
                        height: ten*2.2,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected ? kSecondBlack : Colors.transparent,
                          border: Border.all(
                            color: isSelected || isCompleted
                                ? kSecondBlack
                                : const Color(0xFFE1DDD5),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '$dayNumber',
                            style: TextStyle(
                              fontSize: forteen,
                              fontWeight: FontWeight.w500,
                              color: isSelected || isCompleted
                                  ? Colors.white
                                  : kSecondBlack,
                            ),
                          ),
                        ),
                      ),
                    );
                  }
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListFilterdDishesSuccess) {
              final newCategories = state.data.data?.categoryBasedList
                      ?.where((cat) => cat.category?.name != null)
                      .map((cat) => cat.category!.name!)
                      .toList() ??
                  [];
              setState(() {
                _isLoading = false;
                dishesData = state.data;
                if (_categories.length != newCategories.length) {
                  _tabController.dispose();
                  _tabController =
                      TabController(length: newCategories.length, vsync: this);
                }
                _categories = newCategories;
              });
            } else if (state is ListFilterdDishesFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            } else if (state is AddEditMealPlanSuccess) {
              if (widget.isEditing) {
                // In edit mode, navigate to checkout page
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CheckoutPage(
                      mealPlanId: widget.mealPlanId,
                    ),
                  ),
                );
              } else {
                // Original navigation logic for non-edit mode
                Map<String, Map<String, dynamic>> updatedMealData =
                    Map.from(widget.mealData);
                updatedMealData[_displayStartDate] = {
                  'chef_id': widget.id,
                  'selectedDishes':
                      selectedMeals.map((meal) => meal.toMap()).toList(),
                };

                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PersonailizedSelectchef(
                      dates: widget.dates,
                      mealPlanId: widget.mealPlanId,
                      currentday: widget.selectedDay + 1,
                      mealdata: updatedMealData,
                      dataresponce: widget.dataresponce,
                      timeSlots: widget.timeSlots,
                    ),
                  ),
                );
              }
            } else if (state is AddEditMealPlanFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message ?? 'Failed to update meal plan'),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          },
        ),
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ChefDetailsSuccess) {
              setState(() {
                chefDetails = state.data;
              });
              if (widget.dataresponce?['time_slot_id'] == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Time slot ID is missing'),
                    backgroundColor: Color(0xFFE11900),
                  ),
                );
                return;
              }
              context.read<MealplanBloc>().add(ListFilterdDishes({
                    "chef_id": widget.id,
                    "serving_size_id": widget.servingSizeId,
                    "time_slot_id": widget.dataresponce!['time_slot_id'],
                    "date": widget.selectedDate,
                  }));
            }
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              centerTitle: false,
              scrolledUnderElevation: 0,
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: Image.asset('assets/icons/left_arrow.png',
                    width: twelve, height: twelve),
                onPressed: _navigateBack,
              ),
              elevation: 0,
            ),
            body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                   Padding(
                    padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen/4),
                    child: Text(
                      "Select Meals",
                      style: TextStyle(
                        fontSize: eighteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                   SizedBox(height: sixteen/2),
                  _isLoading
                      ? _buildShimmerDateSelector()
                      : _buildDateSelector(),
                  const SizedBox(height: 26),
                  Padding(
                    padding:  EdgeInsets.symmetric(horizontal: sixteen),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: twenty,
                          backgroundImage: NetworkImage(
                            ServerHelper.imageUrl +
                                (chefDetails?.data?.chef?.profilePhoto ?? ''),
                          ),
                        ),
                         SizedBox(width: twelve),
                        Expanded(
                          child: Text(
                            '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                            style:  TextStyle(
                              fontSize: eighteen,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                   SizedBox(height: ten),
                  Padding(
                    padding:  EdgeInsets.only(
                        left: forteen, right: forteen, top: sixteen/2, bottom: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding:  EdgeInsets.symmetric(
                                  horizontal: twelve/2, vertical: 0),
                              constraints:  BoxConstraints(
                                  minWidth: ten*5, minHeight: ten*3.4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF6F3EC),
                                borderRadius: BorderRadius.circular(ten),
                                border:
                                    Border.all(color: const Color(0xFFB9B6AD)),
                              ),
                              child: Row(
                                children:  [
                                  Icon(Icons.access_time,
                                      size: sixteen, color: Color(0xFF414346)),
                                  SizedBox(width: sixteen/4),
                                  Text(
                                    "35 mins",
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w600,
                                      height: 24 / 16,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                             SizedBox(width: sixteen/2),
                            Container(
                              padding:  EdgeInsets.symmetric(
                                  horizontal: twelve/2, vertical: 0),
                              constraints:  BoxConstraints(
                                  minWidth: ten*6.1, minHeight: ten*3.4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF6F3EC),
                                borderRadius: BorderRadius.circular(ten),
                                border:
                                    Border.all(color: const Color(0xFFB9B6AD)),
                              ),
                              child: Row(
                                children:  [
                                  Icon(Icons.star,
                                      size: sixteen, color: Color(0xFF414346)),
                                  SizedBox(width: sixteen/4),
                                  Text(
                                    "4.9",
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      height: 24 / 16,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                             SizedBox(width: sixteen/2),
                            Container(
                              padding:  EdgeInsets.symmetric(
                                  horizontal: twelve/2, vertical: 0),
                              constraints:  BoxConstraints(
                                  minWidth: ten*8.7, minHeight: ten*3.4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF6F3EC),
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(color: const Color(0xFFB9B6AD)),
                              ),
                              child: Row(
                                children: [
                                   Icon(Icons.location_on_outlined,
                                      size: ten*1.7, color: Color(0xFF414346)),
                                   SizedBox(width: sixteen/4),
                                  Text(
                                    widget.distance.toStringAsFixed(1) + ' KM',
                                    style:  TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      height: 24 / 16,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // const SizedBox(width: sixteen/2),
                            // Container(
                            //   width: 34,
                            //   height: 34,
                            //   decoration: BoxDecoration(
                            //     color: Colors.white,
                            //     borderRadius: BorderRadius.circular(10),
                            //     boxShadow: [
                            //       BoxShadow(
                            //         color: Colors.black.withOpacity(0.05),
                            //         blurRadius: sixteen/4,
                            //         offset: const Offset(0, 2),
                            //       ),
                            //     ],
                            //   ),
                            //   child: IconButton(
                            //     icon: Image.asset(
                            //       'assets/icons/favorites.png',
                            //       width: twentyFour
                            //       height: twentyFour
                            //       color: Color(0xFF1F2122),
                            //     ),
                            //     onPressed: () {},
                            //     padding: EdgeInsets.zero,
                            //   ),
                            // ),
                          ],
                        ),
                         SizedBox(height: sixteen),
                        Wrap(
                          alignment: WrapAlignment.start,
                          runSpacing: 2,
                          children: (chefDetails?.data?.chef?.searchTags ?? [])
                              .map((tag) {
                            return Container(
                              padding:  EdgeInsets.only(right: twelve),
                              child: Text(
                                tag,
                                style:  TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF414346),
                                  height: 20 / 14,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                         SizedBox(height: twenty),
                        Text(
                          chefDetails?.data?.chef?.description ?? '',
                          style:  TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w400,
                            letterSpacing: -0.3,
                            color: Color(0xFF414346),
                          ),
                          textAlign: TextAlign.justify,
                        ),
                         SizedBox(height: twentyFour),
                        SizedBox(
                          height: 30,
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 0),
                            children: _categories.asMap().entries.map((entry) {
                              final index = entry.key;
                              final category = entry.value;
                              final isSelected = _tabController.index == index;
                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _tabController.animateTo(index);
                                  });
                                },
                                child: Container(
                                  margin:  EdgeInsets.only(right: sixteen/2),
                                  padding:  EdgeInsets.symmetric(
                                      horizontal: twelve, vertical: 0),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? const Color(0xFFB9B6AD)
                                        : const Color(0xFFE1DDD5),
                                    borderRadius: BorderRadius.circular(twenty),
                                  ),
                                  child: Center(
                                    child: Text(
                                      category,
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: 0.08,
                                        color: isSelected
                                            ? const Color(0xFF1F2122)
                                            : const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                         SizedBox(height: twenty),
                        // Padding(
                        //   padding: const EdgeInsets.only(
                        //       top: sixteen, bottom: sixteen, right: forteen),
                        //   child: Container(
                        //     height: 46,
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(twentyFour),
                        //       border:
                        //           Border.all(color: const Color(0xFF1F2122)),
                        //     ),
                        //     child: InkWell(
                        //       borderRadius: BorderRadius.circular(twentyFour),
                        //       onTap: () {},
                        //       child: Row(
                        //         mainAxisAlignment: MainAxisAlignment.center,
                        //         children: const [
                        //           Icon(Icons.tune,
                        //               size: sixteen, color: Color(0xFF1F2122)),
                        //           SizedBox(width: sixteen/2),
                        //           Text(
                        //             "View Filters",
                        //             style: TextStyle(
                        //               fontSize: twelve,
                        //               fontWeight: FontWeight.w600,
                        //               color: Color(0xFF1F2122),
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ),
                        // ),
                         Text(
                          "Featured Items",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: twenty,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                         SizedBox(height: twenty),
                        _isLoading
                            ? _buildShimmerCards()
                            : _buildFeaturedItems(),
                         SizedBox(height: eighteen),
                        _isLoading
                            ? _buildShimmerCategoryContent()
                            : _buildCategoryContent(),
                         SizedBox(height: twenty),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            bottomNavigationBar: Container(
              padding:  EdgeInsets.all(sixteen),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, -2),
                    blurRadius: sixteen/2,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${selectedMeals.length}/$_maxDishesPerDay Dishes",
                            style: TextStyle(
                              fontSize: sixteen,
                              fontWeight: FontWeight.w400,
                              color: selectedMeals.length < _maxDishesPerDay
                                  ? Colors.red
                                  : const Color(0xFF1F2122),
                            ),
                          ),
                          const SizedBox(height: 0),
                          Container(
                            height: 1.5,
                            width: 84,
                            color: selectedMeals.length < _maxDishesPerDay
                                ? Colors.red
                                : const Color(0xFF1F2122),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: selectedMeals.length == _maxDishesPerDay
                        ? _saveMealSelection
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1F2122),
                      foregroundColor: Colors.white,
                      padding:  EdgeInsets.symmetric(
                          horizontal: twentyFour ,vertical: sixteen),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28)),
                      disabledBackgroundColor: const Color(0xFFE1DDD5),
                    ),
                    child: Text(
                      widget.isEditing
                          ? 'Save Changes'
                          : _currentDay < int.parse(_planduration)
                              ? 'Continue to Day ${_currentDay + 1} Meals'
                              : 'Continue to Checkout',
                      style:  TextStyle(
                          fontSize: sixteen, fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleContinue() {
    if (selectedMeals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one meal'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Add validation to ensure user has selected the maximum required dishes
    if (selectedMeals.length < _maxDishesPerDay && _maxDishesPerDay > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Please select $_maxDishesPerDay dish${_maxDishesPerDay > 1 ? 'es' : ''} to continue'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final requestData = {
      "mealPlanId": widget.mealPlanId,
      "date": _displayStartDate,
      "chef_id": widget.id,
      "items": selectedMeals
          .map((meal) => {"chef_menu_item_id": meal.mealId})
          .toList()
    };

    // Add day_id when in edit mode
    if (widget.isEditing && widget.editDayId != null) {
      requestData["day_id"] = widget.editDayId!;
    }

    context.read<MealplanBloc>().add(AddEditMealPlanEvent(requestData));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildShimmerDateSelector() {
    return Padding(
      padding:  EdgeInsets.symmetric(horizontal: sixteen),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Row(
              children: [
                Container(width: sixteen, height: sixteen, color: Colors.white),
                 SizedBox(width: sixteen/2),
                Container(
                  width: ten*15,
                  height: forteen,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(sixteen/4)),
                ),
                const Spacer(),
                Row(
                  children: List.generate(
                    5,
                    (index) => Padding(
                      padding:  EdgeInsets.symmetric(horizontal: sixteen/4),
                      child: Container(
                        width: ten*2.5,
                        height: ten*2.5,
                        decoration: const BoxDecoration(
                            color: Colors.white, shape: BoxShape.circle),
                      ),
                    ),
                  ),
                ),
              ],
            ),
             SizedBox(height: sixteen/4),
            Row(
              children: [
                Container(width: sixteen, height: sixteen, color: Colors.white),
                 SizedBox(width: sixteen/2),
                Container(
                  width: ten*10,
                  height: forteen,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(sixteen/4)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedItems() {
    if (dishesData?.data?.featuredList == null ||
        dishesData!.data!.featuredList!.isEmpty) {
      return Container(
        height: ten*10,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(twelve),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child:  Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.restaurant_menu, size: 32, color: Color(0xFF9E9E9E)),
              SizedBox(height: sixteen/2),
              Text(
                "No featured items available",
                style: TextStyle(
                    fontSize: sixteen,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF9E9E9E),
                    fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: ten*19.5, // Reduced from 284
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding:  EdgeInsets.only(right: sixteen),
        children: dishesData!.data!.featuredList!
            .map((dish) => Container(
                  width: ten*20, // Reduced from ten*22
                  margin:  EdgeInsets.only(right: sixteen),
                  child: _buildDishCard({
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(String category) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;
    if (categoryList?.isEmpty ?? true) {
      return Container(
        height: ten*10,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(twelve),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.no_meals, size: 32, color: Color(0xFF9E9E9E)),
              SizedBox(height: sixteen/2),
              Text(
                "No items available in $category",
                style: TextStyle(
                    fontSize: forteen,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF9E9E9E),
                    fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: twentyFour*10, // Reduced from 284
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding:  EdgeInsets.only(right: sixteen),
        children: categoryList!
            .map((dish) => Container(
                  width: ten*20,// Reduced from ten*22
                  margin:  EdgeInsets.only(right: sixteen),
                  child: _buildDishCard({
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent() {
    if (dishesData?.data?.categoryBasedList == null) return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';
        final dishes = category.dishList ?? [];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding:  EdgeInsets.symmetric(horizontal: 0, vertical: sixteen/2),
              child: Text(
                categoryName,
                style:  TextStyle(
                    fontFamily: 'Inter',
                    fontSize: twenty,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122)),
              ),
            ),
             SizedBox(height: sixteen/2),
            dishes.isEmpty
                ? Container(
                    height: ten*10,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(twelve),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.no_meals,
                              size: ten*3.2, color: Color(0xFF9E9E9E)),
                          SizedBox(height: sixteen/2),
                          Text(
                            "No items available in $categoryName",
                            style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF9E9E9E),
                                fontFamily: 'Inter'),
                          ),
                        ],
                      ),
                    ),
                  )
                : _buildCategorySection(categoryName),
             SizedBox(height: eighteen),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerCards() {
    return SizedBox(
      height: ten*28.4,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        padding:  EdgeInsets.only(right: sixteen),
        itemBuilder: (context, index) {
          return Container(
            width: ten*22,
            margin:  EdgeInsets.only(right: sixteen/2),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(sixteen)),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: ten*17.4,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(12)),
                    ),
                  ),
                  Padding(
                    padding:  EdgeInsets.all(sixteen),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(width: ten*10, height: forteen, color: Colors.white),
                         SizedBox(height: twelve),
                        Container(width: ten*5, height: ten, color: Colors.white),
                         SizedBox(height: sixteen/2),
                        Container(width: ten*5, height: ten, color: Colors.white),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerCategoryContent() {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding:  EdgeInsets.symmetric(horizontal: forteen, vertical: sixteen/2.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(width: ten*15, height: twenty, color: Colors.white),
              ),
               SizedBox(height: twelve),
              _buildShimmerCards(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDishCard(Map<String, dynamic> dish) {
    final isSelected = selectedMeals.any((meal) => meal.mealId == dish['id']);
    final isMaxReached =
        selectedMeals.length >= _maxDishesPerDay && _maxDishesPerDay > 0;
    final isDisabled = isMaxReached && !isSelected;
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;

    return GestureDetector(
      onTap: () => _toggleMealSelection(dish),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(twelve),
          boxShadow: [
            BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: sixteen/2,
                offset: const Offset(0, 2)),
          ],
          border: Border.all(
              color: isSelected ? const Color(0xFF1F2122) : Colors.transparent,
              width: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              child: Image.network(
                ServerHelper.imageUrl + (dish['photo'] ?? ''),
                height: ten*10,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: ten*10,
                  width: double.infinity,
                  color: Colors.grey[200],
                  child: const Center(child: Text('Image unavailable')),
                ),
              ),
            ),
            Padding(
              padding:  EdgeInsets.only(
                  right: twelve, left: twelve, top: sixteen/2, bottom: 6), // Reduced padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dish['name'],
                    style:  TextStyle(
                        fontSize: forteen, // Reduced from forteen
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2122)),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                   SizedBox(height: sixteen/2), // Reduced from twelve
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 6, // Reduced from sixteen/2
                            children: [
                              Container(
                                padding:  EdgeInsets.symmetric(
                                    horizontal: sixteen/4, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1E3E6),
                                  borderRadius: BorderRadius.circular(twelve),
                                ),
                                child: Text(
                                  "${dish['serving_size'].split(' ')[0]} Servings",
                                  style: const TextStyle(
                                      fontSize: 9, // Reduced from 10
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter'),
                                ),
                              ),
                              Container(
                                padding:  EdgeInsets.symmetric(
                                    horizontal: sixteen/4, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1E3E6),
                                  borderRadius: BorderRadius.circular(twelve),
                                ),
                                child: Row(
                                  children: [
                                    Image.asset('assets/icons/thump.png',
                                        width: 9, // Reduced from 10
                                        height: 9, // Reduced from 10
                                        color: Colors.black54),
                                    const SizedBox(width: 3), // Reduced from sixteen/4
                                    const Text("90%",
                                        style: TextStyle(
                                            fontSize: 9, // Reduced from 10
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Inter')),
                                    const Text("(50)",
                                        style: TextStyle(
                                            fontSize: 9, // Reduced from 10
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Inter')),
                                  ],
                                ),
                              ),
                            ],
                          ),
                           SizedBox(height: sixteen/2), // Reduced from twelve
                          Text(
                            '\$${double.tryParse(dish['price'] ?? '0.00')?.toStringAsFixed(2) ?? '0.00'}',
                            style:  TextStyle(
                                fontSize: twelve, // Reduced from twelve
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1F2122)),
                          ),
                        ],
                      ),
                      GestureDetector(
                        onTap: () => _toggleMealSelection(dish),
                        child: Container(
                          width: ten*2.2, // Reduced from 24
                          height: ten*2.2, // Reduced from 24
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? const Color(0xFF1F2122)
                                : Colors.transparent,
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFF1F2122)
                                  : isDisabled
                                      ? const Color(0xFFE1DDD5)
                                      : const Color(0xFF1F2122),
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              isSelected ? Icons.check : Icons.add,
                              size: forteen, // Reduced from sixteen
                              color: isSelected
                                  ? Colors.white
                                  : isDisabled
                                      ? const Color(0xFFE1DDD5)
                                      : const Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimeToAmPm(String time) {
    if (time == null || time.isEmpty) return '';
    
    // Extract hours and minutes from the time string (format: "HH:MM:SS")
    final parts = time.split(':');
    if (parts.length < 2) return time;
    
    int hours = int.tryParse(parts[0]) ?? 0;
    final minutes = parts[1];
    final period = hours >= twelve ? 'PM' : 'AM';
    
    // Convert to twelve-hour format
    hours = hours > 12 ? hours - 12 : (hours == 0 ? 12 : hours);
    
    return '$hours:$minutes$period';
  }
}
