// ignore_for_file: deprecated_member_use, unnecessary_const
import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/homemodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart'
    as meal_plan;
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/chef/popular_chefs_near.dart';
import 'package:db_eats/ui/chef/recommended_chefs.dart';
import 'package:db_eats/ui/chef/top_rated_chefs.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:db_eats/ui/deals/dealslist.dart';
import 'package:db_eats/ui/filter_model.dart';
import 'package:db_eats/ui/meal_plan/choose_plan.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';

int _selectedIndex = 0;

final List<Widget> _pages = [
  const Center(child: Text('Home Page')),
  const Center(child: Text('Orders Page')),
  const Center(child: Text('Catering Page')),
  const Center(child: Text('Messages Page')),
  const Center(child: Text('Account Page')),
];

class Home2 extends StatefulWidget {
  const Home2({super.key});

  @override
  State<Home2> createState() => _Home2State();
}

class _Home2State extends State<Home2> {
  late final MealplanBloc _mealplanBloc;
  bool _isSearchPopupVisible = false;
  OverlayEntry? _overlayEntry;
  String _selectedDeliveryTime = 'Loading...';
  String _currentlySelectedTimeRange = '7:00 AM - 7:30 AM';
  bool _showLocationMenu = false;
  bool _showTimeOptions = false;
  double? _currentLatitude;
  double? _currentLongitude;
  String? _currentAddress;
  List<meal_plan.Timings>? _availableTimings;
  List<AddressData>? _savedAddresses;
  AddressData? _currentAddressData;
  bool _loadingAddresses = false;
  bool _isSearching = false;

  // Location search properties
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";
  double? _pendingLatitude;
  double? _pendingLongitude;
  int? _selectedTimeId;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _mealplanBloc = MealplanBloc();
    _loadAddresses();
    _mealplanBloc.add(ListTimingEvent());
    _mealplanBloc.add(GetAddedTimePreferences());
    context.read<AccountBloc>().add(GetCartCountEvent());
    context.read<HomeBloc>().add(GetFilterDataEvent());

    // Set initial delivery time based on Initializer data
    if (Initializer.addedTimePreferenceModel.data != null) {
      final timeData = Initializer.addedTimePreferenceModel.data!;
      _selectedDeliveryTime = timeData.isDeliverNow == true
          ? 'ASAP'
          : timeData.timePreference != null
              ? '${_formatTimeToAMPM(timeData.timePreference!.startTime)} - ${_formatTimeToAMPM(timeData.timePreference!.endTime)}'
              : 'ASAP';
    }
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Future<void> _showLocationModalIfNeeded() async {
    if (_savedAddresses?.isEmpty ?? true) {
      _currentAddress = await Initializer.getAddress();
      if ((_currentAddress?.isEmpty ?? true) && mounted) {
        showDialog(
          context: context,
          builder: (_) => const ChefLocationModal(),
        );
      }
    }
  }

  Future<void> _loadAddresses() async {
    if (_loadingAddresses) return;

    setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load addresses: $e')),
      );
    }
  }

  Future<void> _makeHomeDataRequest(double lat, double lng) async {
    final savedFilters = await Initializer.getAppliedFilters();
    if (savedFilters != null) {
      // If we have filters, merge them with coordinates
      final requestData = <String, dynamic>{
        ...savedFilters,
        'latitude': lat,
        'longitude': lng,
      };
      context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    } else {
      // If no filters, just use coordinates
      context.read<HomeBloc>().add(GetHomeDataEvent(
            data: <String, dynamic>{
              'latitude': lat,
              'longitude': lng,
            },
          ));
    }
  }

  void _selectAddress(AddressData address) async {
    try {
      context.read<AccountBloc>().add(
            EditAddressEvent({
              "id": address.id ?? 0,
              "is_current": true,
            }),
          );
      _pendingLatitude = address.location?.coordinates?[1];
      _pendingLongitude = address.location?.coordinates?[0];
    } catch (e) {
      log('Error selecting address: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select address: $e')),
      );
    }
  }

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  void dispose() {
    _addressController.dispose();
    _closeSearchPopup();
    _mealplanBloc.close();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return MultiBlocProvider(
      providers: [
        BlocProvider<HomeBloc>.value(
          value: BlocProvider.of<HomeBloc>(context),
        ),
        BlocProvider<MealplanBloc>.value(
          value: _mealplanBloc,
        ),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ListAddressesSuccess) {
            setState(() {
              _loadingAddresses = false;
              _savedAddresses = state.data;
              _currentAddressData = _savedAddresses?.firstWhere(
                (address) => address.isCurrent == true,
                orElse: () => _savedAddresses?.isNotEmpty == true
                    ? _savedAddresses!.first
                    : AddressData(),
              );
            });
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // _showLocationModalIfNeeded();
            });
            if (_currentAddressData?.location?.coordinates != null) {
              final lat = _currentAddressData!.location!.coordinates![1];
              final lng = _currentAddressData!.location!.coordinates![0];

              setState(() {
                _currentLatitude = lat;
                _currentLongitude = lng;
              });

              Initializer().setCoordinates(lat, lng);
              _makeHomeDataRequest(lat, lng);
            }
          } else if (state is EditAddressSuccess) {
            if (_pendingLatitude != null && _pendingLongitude != null) {
              setState(() {
                _currentLatitude = _pendingLatitude;
                _currentLongitude = _pendingLongitude;
                _showLocationMenu = false;
                _pendingLatitude = null;
                _pendingLongitude = null;
              });

              Initializer()
                  .setCoordinates(_currentLatitude!, _currentLongitude!);
              _makeHomeDataRequest(_currentLatitude!, _currentLongitude!);
            }
            _loadAddresses();
          } else if (state is AddAddressSuccess) {
            context.read<AccountBloc>().add(ListAddressesEvent());
          }
        },
        child: BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              setState(() {
                _availableTimings = state.data.data?.timings;
              });
            } else if (state is GettingAddedTimePreferencesSuccess) {
              if (state.data != null) {
                setState(() {
                  _selectedDeliveryTime = state.data.isDeliverNow == true
                      ? 'ASAP'
                      : state.data.timePreference != null
                          ? '${_formatTimeToAMPM(state.data.timePreference!.startTime)} - ${_formatTimeToAMPM(state.data.timePreference!.endTime)}'
                          : 'ASAP';
                });
              }
            } else if (state is AddTimePreferencesSuccess) {
              // Fetch updated time preferences after successfully adding them
              _mealplanBloc.add(GetAddedTimePreferences());
              setState(() {
                _showTimeOptions = false;
                _showLocationMenu = false;
              });
            }
          },
          child: Scaffold(
            backgroundColor: const Color.fromRGBO(246, 243, 236, 1),
            body: SafeArea(
              child: Stack(
                children: [
                  Column(
                    children: [
                      _buildLocationHeader(context, screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.01),
                      Expanded(
                        child: BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if (state is HomeDataSuccess) {
                              setState(() {});
                            }
                          },
                          builder: (context, state) {
                            if (_loadingAddresses) {
                              return _buildShimmerLoading(
                                  context, screenWidth, screenHeight);
                            }
                            if (_loadingAddresses || state is HomeDataLoading) {
                              return _buildShimmerLoading(
                                  context, screenWidth, screenHeight);
                            }
                            if (!_loadingAddresses &&
                                (_savedAddresses == null ||
                                    _savedAddresses!.isEmpty)) {
                              return const ChefLocationModal();
                            }

                            if (state is HomeDataSuccess) {
                              final data = state.data.data;

                              return SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildFilterButton(context, screenWidth),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(
                                        context, screenWidth, 'Explore Deals'),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildPromoCards(context, screenWidth,
                                        screenHeight, data?.deals ?? []),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(context, screenWidth,
                                        'Top-Rated Chefs'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.topRatedChefs?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No top rated chefs available')
                                    else
                                      _buildTopRatedChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.topRatedChefs ?? []),
                                    SizedBox(height: screenHeight * 0.03),
                                    _buildSectionHeader(context, screenWidth,
                                        'Recommended For You'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.recommendedChefs?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No recommended chefs available')
                                    else
                                      _buildRecommendedChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.recommendedChefs ?? []),
                                    SizedBox(height: screenHeight * 0.03),
                                    _buildMealPlanCard(
                                        context, screenWidth, screenHeight),
                                    _buildMonthlySaverCard(
                                        context, screenWidth, screenHeight),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(context, screenWidth,
                                        'Most Popular Near You'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.popularChefsNear?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No popular chefs in your area')
                                    else
                                      _buildPopularChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.popularChefsNear ?? []),
                                    SizedBox(height: screenHeight * 0.03),
                                  ],
                                ),
                              );
                            }

                            return _buildShimmerLoading(
                                context, screenWidth, screenHeight);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            floatingActionButton: CartFloatingActionButton(
              itemCount: Initializer.cartCount ?? 0,
              onPressed: _openCart,
            ),
          ),
        ),
      ),
    );
  }

  void _openCart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CartPage(),
      ),
    );
  }

  Widget _buildLocationHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showLocationMenu = !_showLocationMenu;
                    _showTimeOptions = false;
                  });
                },
                child: Icon(Icons.menu,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1),
                    semanticLabel: 'Open location menu'),
              ),
              SizedBox(width: screenWidth * 0.03),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showLocationMenu = !_showLocationMenu;
                          _showTimeOptions = false;
                        });
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.location_on_outlined,
                                  size: screenWidth * 0.035,
                                  color: const Color.fromRGBO(31, 33, 34, 1)),
                              SizedBox(width: screenWidth * 0.01),
                              Expanded(
                                child: Text(
                                  _currentAddressData?.addressText ??
                                      'Loading address...',
                                  style: TextStyle(
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.005),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  size: screenWidth * 0.03,
                                  color: const Color.fromRGBO(31, 33, 34, 1)),
                              SizedBox(width: screenWidth * 0.015),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    _showLocationMenu = true;
                                    _showTimeOptions = true;
                                  });
                                },
                                child: Row(
                                  children: [
                                    Text(
                                      _selectedDeliveryTime,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              GestureDetector(
                onTap: _toggleSearchPopup,
                child: Icon(Icons.search,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1),
                    semanticLabel: 'Search'),
              ),
            ],
          ),
        ),
        if (_showTimeOptions) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(Icons.arrow_back,
                              color: const Color(0xFF1F2122),
                              size: screenWidth * 0.05),
                          onPressed: () {
                            setState(() {
                              _showTimeOptions = false;
                            });
                          },
                        ),
                        Expanded(
                          child: Center(
                            child: Text(
                              'Schedule Delivery',
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.15),
                      ],
                    ),
                  ),
                  _buildScheduleTimeOptionWidget(
                      context, screenWidth, screenHeight),
                ],
              ),
            ),
          ),
        ] else if (_showLocationMenu) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.7,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter Your Street And House Number',
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.015),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: const Color(0xFFE1E3E6), width: 1),
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.09),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.045, vertical: 0),
                              height: screenHeight * 0.055,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _addressController,
                                      onChanged: searchPlaces,
                                      decoration: InputDecoration(
                                        hintText: 'Street, Postal code',
                                        hintStyle: TextStyle(
                                          color: const Color(0xFF66696D),
                                          fontSize: screenWidth * 0.035,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                        ),
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.zero,
                                        isDense: true,
                                      ),
                                    ),
                                  ),
                                  TextButton.icon(
                                    onPressed: () async {
                                      final position =
                                          await _getCurrentLocation();
                                      if (position != null) {
                                        final List<Placemark> placemarks =
                                            await placemarkFromCoordinates(
                                          position.latitude,
                                          position.longitude,
                                        );

                                        if (placemarks.isNotEmpty) {
                                          final Placemark place = placemarks[0];
                                          final String address =
                                              '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}';

                                          context.read<AccountBloc>().add(
                                                AddAddressEvent({
                                                  "latitude": position.latitude,
                                                  "longitude":
                                                      position.longitude,
                                                  "address_text": address,
                                                  "is_current": true,
                                                }),
                                              );

                                          setState(() {
                                            _currentLatitude =
                                                position.latitude;
                                            _currentLongitude =
                                                position.longitude;
                                            _showLocationMenu = false;
                                          });

                                          Initializer().setCoordinates(
                                              position.latitude,
                                              position.longitude);
                                          _makeHomeDataRequest(
                                              position.latitude,
                                              position.longitude);
                                        }
                                      }
                                    },
                                    icon: Icon(Icons.my_location,
                                        size: screenWidth *
                                            0.05, // Reduced icon size
                                        color: const Color(0xFF1F2122)),
                                    label: Text(
                                      'Locate me',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.030,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: Size.zero,
                                      tapTargetSize: MaterialTapTargetSize
                                          .shrinkWrap, // Helps reduce button height
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (_isSearching)
                              Padding(
                                padding:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                child: const Center(
                                    child: CircularProgressIndicator()),
                              )
                            else if (_showPredictions &&
                                _searchResults.isNotEmpty)
                              Container(
                                height: screenHeight * 0.15,
                                margin:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                  border: Border.all(
                                      color: const Color(0xFFE1E3E6), width: 1),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.15),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: ListView.separated(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    itemCount: _searchResults.length,
                                    separatorBuilder: (_, __) => const Divider(
                                        height: 1, color: Color(0xFFE1E3E6)),
                                    itemBuilder: (_, index) {
                                      final prediction = _searchResults[index];
                                      return ListTile(
                                        dense: true,
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.04,
                                            vertical: screenHeight * 0.005),
                                        title: Text(
                                          prediction.description ?? '',
                                          style: TextStyle(
                                            fontSize: screenWidth * 0.035,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                        onTap: () => selectPlace(prediction),
                                      );
                                    },
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.03),
                        Text(
                          'Saved Addresses',
                          style: TextStyle(
                            fontSize: screenWidth * 0.04,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.02),
                        if (_loadingAddresses)
                          const Center(child: CircularProgressIndicator())
                        else if (_savedAddresses == null ||
                            _savedAddresses!.isEmpty)
                          const Center(child: Text('No saved addresses'))
                        else
                          ...(_savedAddresses!
                              .where((addr) => addr.isCurrent != true)
                              .take(2)
                              .map((address) => _buildAddressListItem(
                                  context, screenWidth, screenHeight, address))
                              .toList()),
                        SizedBox(height: screenHeight * 0.02),
                        InkWell(
                          onTap: () {
                            setState(() {
                              _showTimeOptions = true;
                            });
                          },
                          child: Row(
                            children: [
                              Icon(Icons.schedule, size: screenWidth * 0.06),
                              SizedBox(width: screenWidth * 0.04),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Time preference',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.035,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    Text(
                                      _selectedDeliveryTime,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        color: Colors.grey,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_showTimeOptions) ...[
                    const Divider(),
                    Container(
                      color: const Color(0xFFF6F3EC),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildScheduleTimeOptionWidget(
                              context, screenWidth, screenHeight),
                          Padding(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedDeliveryTime =
                                            _currentlySelectedTimeRange;
                                        _showTimeOptions = false;
                                      });
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.black,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.06),
                                      ),
                                    ),
                                    child: Text('Schedule',
                                        style: TextStyle(
                                            fontSize: screenWidth * 0.035)),
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                Expanded(
                                  child: OutlinedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedDeliveryTime = 'ASAP';
                                        _showTimeOptions = false;
                                      });
                                    },
                                    style: OutlinedButton.styleFrom(
                                      side: BorderSide(
                                          color: Colors.grey.shade300),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.06),
                                      ),
                                    ),
                                    child: Text('Deliver Now',
                                        style: TextStyle(
                                            fontSize: screenWidth * 0.035)),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
        const Divider(height: 2, color: Color.fromARGB(255, 219, 212, 212)),
      ],
    );
  }

  Widget _buildAddressListItem(BuildContext context, double screenWidth,
      double screenHeight, AddressData address) {
    return InkWell(
      onTap: () => _selectAddress(address),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
        child: Row(
          children: [
            Icon(Icons.location_on_outlined, size: screenWidth * 0.06),
            SizedBox(width: screenWidth * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressText ?? 'No address text',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                  if (address.isCurrent == true)
                    Text(
                      'Current address',
                      style: TextStyle(
                        fontSize: screenWidth * 0.03,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: screenWidth * 0.03,
              color: const Color(0xFF1F2122),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSearchPopup() {
    if (_isSearchPopupVisible) {
      _closeSearchPopup();
    } else {
      _showSearchPopup();
    }
  }

  void _showSearchPopup() {
    if (_isSearchPopupVisible) return;

    // Trigger event to load data immediately when popup opens
    context.read<HomeBloc>().add(
          GetRecentPopularSearchEvent(
            data: {
              'page': 1,
              'limit': 3,
            },
          ),
        );

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    _overlayEntry = OverlayEntry(
        builder: (context) => MultiBlocProvider(
                // Wrap with MultiBlocProvider to preserve state
                providers: [
                  BlocProvider.value(value: BlocProvider.of<HomeBloc>(context)),
                  BlocProvider.value(
                      value: BlocProvider.of<AccountBloc>(context)),
                  BlocProvider.value(
                      value: BlocProvider.of<MealplanBloc>(context)),
                  BlocProvider.value(
                      value: BlocProvider.of<OrderBloc>(context)),
                ],
                child: Material(
                  color: Colors.black.withOpacity(0.5),
                  child: Stack(
                    children: [
                      // Close overlay when tapping outside
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: _closeSearchPopup,
                          child: Container(color: Colors.transparent),
                        ),
                      ),
                      // Modal content
                      Positioned(
                        top:
                            MediaQuery.of(context).padding.top + kToolbarHeight,
                        left: screenWidth * 0.04,
                        right: screenWidth * 0.04,
                        child: Material(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          elevation: 8,
                          child: Container(
                            constraints: BoxConstraints(
                              maxHeight: screenHeight * 0.45,
                            ),
                            child: BlocConsumer<HomeBloc, HomeState>(
                              listener: (context, state) {
                                if (state is RecentPopularSearchSuccess) {
                                  setState(
                                      () {}); // Force UI update when data arrives
                                }
                              },
                              builder: (context, state) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Search bar
                                    Padding(
                                      padding:
                                          EdgeInsets.all(screenWidth * 0.04),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Row(
                                              children: [
                                                Icon(Icons.search,
                                                    color: const Color(
                                                        0xFF414346)),
                                                SizedBox(
                                                    width: screenWidth * 0.02),
                                                Expanded(
                                                  child: TextField(
                                                    controller:
                                                        _addressController,
                                                    decoration: InputDecoration(
                                                      hintText:
                                                          'Search Dishes, Chefs, cuisines...',
                                                      hintStyle: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize:
                                                            screenWidth * 0.035,
                                                        color: const Color(
                                                            0xFF909090),
                                                      ),
                                                      border: InputBorder.none,
                                                    ),
                                                    autofocus: true,
                                                    textInputAction:
                                                        TextInputAction.search,
                                                    onChanged: searchPlaces,
                                                    onSubmitted: (value) {
                                                      if (value.isNotEmpty) {
                                                        context
                                                            .read<HomeBloc>()
                                                            .add(
                                                              GetHomeDataEvent(
                                                                data: {
                                                                  'latitude':
                                                                      _currentLatitude ??
                                                                          0,
                                                                  'longitude':
                                                                      _currentLongitude ??
                                                                          0,
                                                                  'search_keyword':
                                                                      value,
                                                                },
                                                              ),
                                                            );
                                                        _closeSearchPopup();
                                                      }
                                                    },
                                                    onEditingComplete: () {
                                                      final value =
                                                          _addressController
                                                              .text;
                                                      if (value.isNotEmpty) {
                                                        context
                                                            .read<HomeBloc>()
                                                            .add(
                                                              GetHomeDataEvent(
                                                                data: {
                                                                  'latitude':
                                                                      _currentLatitude ??
                                                                          0,
                                                                  'longitude':
                                                                      _currentLongitude ??
                                                                          0,
                                                                  'search_keyword':
                                                                      value,
                                                                },
                                                              ),
                                                            );
                                                        _closeSearchPopup();
                                                      }
                                                    },
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.close),
                                            onPressed: _closeSearchPopup,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Divider(height: 1),

                                    Expanded(
                                      child: SingleChildScrollView(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // Search tabs
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      screenWidth * 0.04),
                                              child: Row(
                                                children: [
                                                  _buildSearchTab(context,
                                                      screenWidth, 'All', true),
                                                  _buildSearchTab(
                                                      context,
                                                      screenWidth,
                                                      'Dishes',
                                                      false),
                                                  _buildSearchTab(
                                                      context,
                                                      screenWidth,
                                                      'Chefs',
                                                      false),
                                                ],
                                              ),
                                            ),

                                            // Recent searches section
                                            if (state
                                                    is RecentPopularSearchSuccess &&
                                                state.data.data?.data
                                                        ?.isNotEmpty ==
                                                    true)
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  ListTile(
                                                    leading: Image.asset(
                                                      'assets/icons/recent.png',
                                                      width: 24,
                                                      height: 24,
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                    title: Text(
                                                      'Recent',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize:
                                                            screenWidth * 0.04,
                                                        fontFamily: 'Inter',
                                                      ),
                                                    ),
                                                    dense: true,
                                                    horizontalTitleGap: 6,
                                                  ),
                                                  ...state.data.data!.data!.map(
                                                    (item) => _buildSearchItem(
                                                      context,
                                                      screenWidth,
                                                      item.searchQuery ?? '',
                                                    ),
                                                  ),
                                                ],
                                              ),

                                            // Show shimmer when loading
                                            if (state
                                                is RecentPopularSearchLoading)
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Recent searches shimmer
                                                  Padding(
                                                    padding: EdgeInsets.all(
                                                        screenWidth * 0.04),
                                                    child: Row(
                                                      children: [
                                                        Shimmer.fromColors(
                                                          baseColor:
                                                              Colors.grey[300]!,
                                                          highlightColor:
                                                              Colors.grey[100]!,
                                                          child: Container(
                                                            width: 24,
                                                            height: 24,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                        SizedBox(
                                                            width: screenWidth *
                                                                0.02),
                                                        Shimmer.fromColors(
                                                          baseColor:
                                                              Colors.grey[300]!,
                                                          highlightColor:
                                                              Colors.grey[100]!,
                                                          child: Container(
                                                            width: screenWidth *
                                                                0.2,
                                                            height:
                                                                screenWidth *
                                                                    0.04,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  // Recent search items shimmer
                                                  ...List.generate(
                                                      3,
                                                      (index) => Padding(
                                                            padding: EdgeInsets.symmetric(
                                                                horizontal:
                                                                    screenWidth *
                                                                        0.04,
                                                                vertical:
                                                                    screenWidth *
                                                                        0.02),
                                                            child: Shimmer
                                                                .fromColors(
                                                              baseColor: Colors
                                                                  .grey[300]!,
                                                              highlightColor:
                                                                  Colors.grey[
                                                                      100]!,
                                                              child: Container(
                                                                width: double
                                                                    .infinity,
                                                                height:
                                                                    screenWidth *
                                                                        0.035,
                                                                color: Colors
                                                                    .white,
                                                              ),
                                                            ),
                                                          )),

                                                  // Popular searches shimmer
                                                  Padding(
                                                    padding: EdgeInsets.all(
                                                        screenWidth * 0.04),
                                                    child: Shimmer.fromColors(
                                                      baseColor:
                                                          Colors.grey[300]!,
                                                      highlightColor:
                                                          Colors.grey[100]!,
                                                      child: Container(
                                                        width:
                                                            screenWidth * 0.3,
                                                        height:
                                                            screenWidth * 0.04,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                  // Popular search tags shimmer
                                                  Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                screenWidth *
                                                                    0.04),
                                                    child: Wrap(
                                                      spacing:
                                                          screenWidth * 0.02,
                                                      runSpacing:
                                                          screenWidth * 0.02,
                                                      children: List.generate(
                                                          4,
                                                          (index) => Shimmer
                                                                  .fromColors(
                                                                baseColor:
                                                                    Colors.grey[
                                                                        300]!,
                                                                highlightColor:
                                                                    Colors.grey[
                                                                        100]!,
                                                                child:
                                                                    Container(
                                                                  width:
                                                                      screenWidth *
                                                                          0.2,
                                                                  height:
                                                                      screenWidth *
                                                                          0.06,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .white,
                                                                    borderRadius:
                                                                        BorderRadius.circular(screenWidth *
                                                                            0.04),
                                                                  ),
                                                                ),
                                                              )),
                                                    ),
                                                  ),
                                                ],
                                              ),

                                            // Popular searches section
                                            if (state
                                                    is RecentPopularSearchSuccess &&
                                                state.data.data?.popularSearches
                                                        ?.isNotEmpty ==
                                                    true)
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsets.all(
                                                        screenWidth * 0.04),
                                                    child: Text(
                                                      'Popular searches',
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize:
                                                            screenWidth * 0.04,
                                                      ),
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                screenWidth *
                                                                    0.04),
                                                    child: Wrap(
                                                      spacing:
                                                          screenWidth * 0.02,
                                                      runSpacing:
                                                          screenWidth * 0.02,
                                                      children: state.data.data!
                                                          .popularSearches!
                                                          .map((item) =>
                                                              _buildSearchTag(
                                                                context,
                                                                screenWidth,
                                                                item.searchQuery ??
                                                                    '',
                                                              ))
                                                          .toList(),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                )));

    _overlay?.insert(_overlayEntry!);
    setState(() => _isSearchPopupVisible = true);
  }

  OverlayState? get _overlay => Overlay.of(context);

  void _closeSearchPopup() {
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;

      if (_addressController.text.isEmpty &&
          _currentLatitude != null &&
          _currentLongitude != null) {
        _makeHomeDataRequest(_currentLatitude!, _currentLongitude!);
      }

      setState(() {
        _isSearchPopupVisible = false;
        _searchResults = [];
        _showPredictions = false;
        _addressController.clear();
      });
    }
  }

  Widget _buildSearchTab(
      BuildContext context, double screenWidth, String title, bool isActive) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: screenWidth * 0.025, horizontal: screenWidth * 0.0175),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: screenWidth * 0.05,
            alignment: Alignment.center,
            child: Text(
              title,
              style: TextStyle(
                color: isActive
                    ? const Color(0xFF1F2122)
                    : const Color(0xFF66696D),
                fontWeight: FontWeight.w600,
                fontSize: screenWidth * 0.035,
              ),
            ),
          ),
          SizedBox(height: screenWidth * 0.0075),
          Container(
            height: 3,
            width: screenWidth * 0.05,
            color: isActive ? Colors.amber : Colors.transparent,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchItem(
      BuildContext context, double screenWidth, String title) {
    return InkWell(
      onTap: () {
        if (title.isNotEmpty) {
          _closeSearchPopup(); // First close the popup
          context.read<HomeBloc>().add(
                GetHomeDataEvent(
                  data: {
                    'latitude': _currentLatitude ?? 0,
                    'longitude': _currentLongitude ?? 0,
                    'search_keyword': title,
                  },
                ),
              );
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: screenWidth * 0.02, horizontal: screenWidth * 0.04),
        child: Text(
          title,
          style: TextStyle(
            fontSize: screenWidth * 0.035,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            color: const Color(0xFF1F2122),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchTag(
      BuildContext context, double screenWidth, String title) {
    return InkWell(
      onTap: () {
        if (title.isNotEmpty) {
          _closeSearchPopup(); // First close the popup
          context.read<HomeBloc>().add(
                GetHomeDataEvent(
                  data: {
                    'latitude': _currentLatitude ?? 0,
                    'longitude': _currentLongitude ?? 0,
                    'search_keyword': title,
                  },
                ),
              );
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.03, vertical: screenWidth * 0.0125),
        decoration: BoxDecoration(
          color: const Color(0xFFE1E3E6),
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: screenWidth * 0.035,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2122),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButton(BuildContext context, double screenWidth) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenWidth * 0.03),
      child: InkWell(
        onTap: () {
          // Show the bottom modal sheet when the filter button is tapped
          showModalBottomSheet(
            context: context,
            isScrollControlled: true, // Allows the modal to take more height
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            builder: (context) {
              // Pass the filter data directly to the FilterModal
              return FilterModal(
                // filterData: Initializer.filterdataModel.data,
                screenWidth: screenWidth,
              );
            },
          );
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: screenWidth * 0.035),
          decoration: BoxDecoration(
            border: Border.all(
                color: const Color.fromRGBO(31, 33, 34, 1), width: 1.5),
            borderRadius: BorderRadius.circular(screenWidth * 0.075),
            color: const Color.fromRGBO(246, 243, 236, 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.tune,
                  size: screenWidth * 0.045,
                  color: Colors.grey[800],
                  semanticLabel: 'Filters'),
              SizedBox(width: screenWidth * 0.02),
              Text(
                'View Filters',
                style: TextStyle(
                  fontSize: screenWidth * 0.03,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: const Color.fromRGBO(31, 33, 34, 1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
      BuildContext context, double screenWidth, String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenWidth * 0.03,
          screenWidth * 0.04, screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: screenWidth * 0.04584,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              height: 1,
            ),
          ),
          GestureDetector(
            onTap: () {
              if (title == 'Top-Rated Chefs') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const TopRatedChefsPage()));
              } else if (title == 'Recommended For You') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const RecommendedChefsPage()));
              } else if (title == 'Most Popular Near You') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PopularChefsNearPage()));
              } else if (title == 'Explore Deals') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const DealsOfTheDay()));
              }
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'See All',
                  style: TextStyle(
                    fontSize: screenWidth * 0.03,
                    fontWeight: FontWeight.w600,
                    height: 0.9,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: screenWidth * 0.0025),
                Container(
                  height: 1.2,
                  width: screenWidth * 0.09,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCards(BuildContext context, double screenWidth,
      double screenHeight, List<Deal> deals) {
    if (deals.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04,
          vertical: screenHeight * 0.02,
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.local_offer_outlined,
                size: screenWidth * 0.1,
                color: const Color(0xFF66696D),
              ),
              SizedBox(height: screenHeight * 0.01),
              Text(
                'No deals available',
                style: TextStyle(
                  fontSize: screenWidth * 0.035,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF66696D),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: screenHeight * 0.23,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        scrollDirection: Axis.horizontal,
        itemCount: deals.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final deal = deals[index];
          return Container(
            width: screenWidth * 0.45,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: screenWidth * 0.02,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(screenWidth * 0.03),
                    topRight: Radius.circular(screenWidth * 0.03),
                  ),
                  child: Image.network(
                    deal.chef?.profile?.coverPhoto != null
                        ? ServerHelper.imageUrl +
                            deal.chef!.profile!.coverPhoto!
                        : 'https://via.placeholder.com/180x100.png?text=Deal',
                    height: screenHeight * 0.12,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: screenHeight * 0.12,
                      width: double.infinity,
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.broken_image,
                        size: screenWidth * 0.1,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(screenWidth * 0.03),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          deal.title ?? 'Deal Title',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w500,
                            fontSize: screenWidth * 0.03565,
                            height: 1.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Row(
                          children: [
                            CircleAvatar(
                              radius: screenWidth * 0.025,
                              backgroundImage: NetworkImage(
                                deal.chef?.profile?.profilePhoto != null
                                    ? ServerHelper.imageUrl +
                                        deal.chef!.profile!.profilePhoto!
                                    : 'https://via.placeholder.com/40x40.png?text=Chef',
                              ),
                            ),
                            SizedBox(width: screenWidth * 0.015),
                            Expanded(
                              child: Text(
                                '${_capitalizeFirst(deal.chef?.firstName ?? '')} ${_capitalizeFirst(deal.chef?.lastName ?? '')}',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: screenWidth * 0.03,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'View Deal',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: screenWidth * 0.025,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                    // no decoration here
                                  ),
                                ),
                                const SizedBox(
                                    height:
                                        1), // space between text and underline
                                Container(
                                  height: 1.2,
                                  width: screenWidth *
                                      0.1223, // or use based on text length
                                  //1249
                                  color: const Color(0xFF1F2122),
                                ),
                              ],
                            ),
                            SizedBox(width: screenWidth * 0.01),
                            // Icon(
                            //   Icons.arrow_forward,
                            //   size: screenWidth * 0.035,
                            //   color: const Color(0xFF1F2122),
                            // ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Helper method for capitalizing first letter
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return '';
    return text[0].toUpperCase() + (text.length > 1 ? text.substring(1) : '');
  }

  Widget _buildMealPlanCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.asset(
                'assets/images/weekly_plan.png',
                width: double.infinity,
                height: screenHeight * 0.2,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/date_range.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        'Weekly Meal Plan',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: screenWidth * 0.04584,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    'Subscribe to curated dishes delivered weekly. Dishes starting at \$9, skip or cancel anytime.',
                    style: TextStyle(
                      color: const Color(0xFFAAADB1),
                      fontSize: screenWidth * 0.03,
                      height: 1.4,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Discounted Pricing',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Free Delivery',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Top-Rated Chefs',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                            builder: (context) => const ChoosePlan()),
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Meal Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                  ),
                  // SizedBox(height: screenHeight * 0.015),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySaverCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
        padding: EdgeInsets.all(screenWidth * 0.04),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(screenWidth * 0.04),
                  topRight: Radius.circular(screenWidth * 0.04),
                ),
                child: Image.asset(
                  'assets/images/monthly_saver.png',
                  width: double.infinity,
                  height: screenHeight * 0.19,
                  fit: BoxFit.cover,
                ),
              ),
              Padding(
                padding: EdgeInsets.all(screenWidth * 0.03),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFFFBE16),
                            shape: BoxShape.circle,
                          ),
                          width: screenWidth * 0.067,
                          height: screenWidth * 0.067,
                          child: Padding(
                            padding: EdgeInsets.all(screenWidth * 0.02),
                            child: Image.asset(
                              'assets/icons/percent.png',
                              width: screenWidth * 0.04,
                              height: screenWidth * 0.04,
                              fit: BoxFit.contain,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Text(
                          "Monthly Saver's Pass",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: screenWidth * 0.04584,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'The all-in-one plan you need to unlock exclusive benefits across DB.',
                      style: TextStyle(
                        color: const Color(0xFFAAADB1),
                        fontSize: screenWidth * 0.03,
                        fontWeight: FontWeight.w400,
                        height: 1.4,
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: screenWidth * 0.02),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Unlimited Free Delivery (Capped at \$2.0)',
                                  style: TextStyle(
                                      color: const Color(0xFFAAADB1),
                                      fontWeight: FontWeight.w400,
                                      fontSize: screenWidth * 0.03)),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.003),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Up to 30% Off Restaurants',
                                  style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w400,
                                  )),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.003),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Surprise Perks',
                                  style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w400,
                                  )),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.016),
                    Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Meal Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 2,
                    )
                    // SizedBox(height: screenHeight * 0.015),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildTopRatedChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating =
              '${chef.ratingPercentage}% (${chef.totalRatings})'; // Static rating value
          final prepTime = '30-45 mins'; // Static prep time value

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildChefCard({
    required BuildContext context,
    required double screenWidth,
    required double screenHeight,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
    required int id,
  }) {
    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ViewChef2(
                id: id,
                title: name,
                latitude: _currentLatitude ?? 0,
                longitude: _currentLongitude ?? 0,
                distance: distance,
              ),
            ),
          );
        },
        child: Container(
          width: ten * 23,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.01),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(screenWidth * 0.03),
                      topRight: Radius.circular(screenWidth * 0.03),
                    ),
                    child: Image.network(
                      dishImage,
                      width: double.infinity,
                      height: ten * 12,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: double.infinity,
                            height: ten * 12,
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: ten * 12,
                          color: Colors.grey[300],
                          child: Icon(Icons.image_not_supported,
                              color: Colors.grey[600]),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: screenWidth * 0.04,
                    left: screenWidth * 0.04,
                    child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.02,
                            vertical: screenWidth * 0.01),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(0, 0, 0, 0),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: SizedBox.shrink()

                        //  Row(
                        //   children: [
                        //     Icon(Icons.access_time,
                        //         size: screenWidth * 0.03, color: Colors.white),
                        //     SizedBox(width: screenWidth * 0.01),
                        //     Text(
                        //       prepTime,
                        //       style: TextStyle(
                        //         fontSize: screenWidth * 0.03,
                        //         color: Colors.white,
                        //         fontWeight: FontWeight.w500,
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        ),
                  ),
                  Positioned(
                    left: screenWidth * 0.04,
                    bottom: -screenWidth * 0.05,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                      child: CircleAvatar(
                        radius: ten + twelve,
                        backgroundImage: NetworkImage(image),
                        onBackgroundImageError: (exception, stackTrace) {
                          print('Error loading chef image: $exception');
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.03),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Text(
                      cuisines,
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color.fromRGBO(65, 67, 70, 1),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.015,
                              vertical: screenWidth * 0.005),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/thump.png',
                                width: screenWidth * 0.0275,
                                height: screenWidth * 0.025,
                                color: Colors.black54,
                              ),
                              SizedBox(width: screenWidth * 0.01),
                              Text(
                                rating,
                                style: TextStyle(
                                  fontSize: ten,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Row(
                          children: [
                            Icon(Icons.location_on_outlined,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122)),
                            SizedBox(width: screenWidth * 0.005),
                            Text(
                              distance,
                              style: TextStyle(
                                fontSize: ten,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Row(
                      children: [
                        Image.asset(
                          'assets/icons/calender_2.png',
                          width: screenWidth * 0.03,
                          height: screenWidth * 0.0325,
                          color: Colors.black54,
                        ),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          availability,
                          style: TextStyle(
                            fontSize: twelve,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildRecommendedChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating =
              '${chef.ratingPercentage}% (${chef.totalRatings})'; // Static rating value
          final prepTime = '30-45 mins'; // Static prep time value

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildPopularChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating =
              '${chef.ratingPercentage}% (${chef.totalRatings})'; // Static rating value
          final prepTime = '30-45 mins'; // Static prep time value

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildScheduleTimeOptionWidget(
      BuildContext context, double screenWidth, double screenHeight) {
    if (_availableTimings == null || _availableTimings!.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.04),
          child: Text(
            'No time slots available',
            style: TextStyle(
              fontSize: screenWidth * 0.035,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
      );
    }

    return BlocBuilder<MealplanBloc, MealPlanState>(
      builder: (context, state) {
        return Column(
          children: [
            Column(
              children: _availableTimings!.map((timing) {
                final timeSlot =
                    '${_formatTimeToAMPM(timing.startTime)} - ${_formatTimeToAMPM(timing.endTime)}';
                return InkWell(
                  onTap: () {
                    setState(() {
                      _currentlySelectedTimeRange = timeSlot;
                      _selectedTimeId = timing.id;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.06,
                        vertical: screenHeight * 0.015),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          timeSlot,
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Inter',
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        if (timeSlot == _currentlySelectedTimeRange)
                          Icon(Icons.check,
                              color: const Color(0xFF1F2122),
                              size: screenWidth * 0.05),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: state is AddTimePreferencesLoading
                          ? null
                          : () {
                              if (_selectedTimeId != null) {
                                context.read<MealplanBloc>().add(
                                      AddTimePreferences({
                                        "time_preference_id": _selectedTimeId
                                      }),
                                    );
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content:
                                          Text('Please select a time slot')),
                                );
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.06)),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: state is AddTimePreferencesLoading
                          ? SizedBox(
                              height: screenWidth * 0.05,
                              width: screenWidth * 0.05,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Schedule',
                              style: TextStyle(
                                fontSize: screenWidth * 0.035,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                fontFamily: 'Inter',
                              ),
                            ),
                    ),
                  ),
                  // SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        context.read<MealplanBloc>().add(
                              AddTimePreferences({}),
                            );
                        setState(() {
                          _selectedDeliveryTime = 'ASAP';
                          _showTimeOptions = false;
                          _showLocationMenu = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        backgroundColor: Colors.white,
                        side: const BorderSide(color: Color(0xFFE1E3E6)),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: Text(
                        'Deliver Now',
                        style: TextStyle(
                          fontSize: screenWidth * 0.035,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2122),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNoDataMessage(BuildContext context, double screenWidth,
      double screenHeight, String message) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
      child: Center(
        child: Text(
          message,
          style: TextStyle(
            fontSize: screenWidth * 0.04,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: const Color(0xFF66696D),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.06,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.075),
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                  width: screenWidth * 0.4,
                  height: screenHeight * 0.03,
                  color: Colors.white),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.25,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.45,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: screenWidth * 0.4,
                height: screenHeight * 0.03,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.32,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.6,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.4,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _showPredictions = false;
      });
      return;
    }

    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      setState(() => _isSearching = true);
      try {
        final url = Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$query&key=$kGoogleApiKey',
        );
        final response = await http.get(url);
        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          if (json['status'] == 'OK') {
            final predictions = json['predictions'] as List;
            setState(() {
              _searchResults = predictions
                  .map((p) => Prediction(
                        description: p['description'],
                        placeId: p['place_id'],
                      ))
                  .toList();
              _showPredictions = true;
            });
          } else {
            throw Exception('Places API error: ${json['status']}');
          }
        } else {
          throw Exception('Failed to fetch places: ${response.statusCode}');
        }
      } catch (e) {
        log('Error searching places: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to search places: $e')),
        );
      } finally {
        setState(() => _isSearching = false);
      }
    });
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isSearching = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry&key=$kGoogleApiKey',
      );
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        if (json['status'] == 'OK') {
          final location = json['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];

          context.read<AccountBloc>().add(
                AddAddressEvent({
                  "latitude": lat,
                  "longitude": lng,
                  "address_text": prediction.description,
                  "is_current": true,
                }),
              );

          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _addressController.text = prediction.description ?? '';
            _showPredictions = false;
            _searchResults = [];
            _showLocationMenu = false;
          });

          Initializer().setCoordinates(lat, lng);
          _makeHomeDataRequest(lat, lng);
        } else {
          throw Exception('Place details API error: ${json['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error selecting place: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select place: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }

  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location services are disabled')),
        );
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permission denied')),
          );
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Location permission permanently denied')),
        );
        return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      log('Error getting location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to get location: $e')),
      );
      return null;
    }
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';

    // Convert meters to kilometers
    final kilometers = distance / 1000;

    // Format to one decimal place
    return '${kilometers.toStringAsFixed(1)} km';
  }

  String _formatTimeToAMPM(String? time) {
    if (time == null || time.isEmpty) return '';
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      final minute = parts[1];
      final period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12 == 0 ? 12 : hour % 12;
      return '$hour:$minute $period';
    } catch (e) {
      return time;
    }
  }
}

class Prediction {
  final String? description;
  final String? placeId;

  Prediction({this.description, this.placeId});
}
