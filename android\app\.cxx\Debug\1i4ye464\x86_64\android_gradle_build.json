{"buildFiles": ["/home/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/usr/bin/ninja", "-C", "/home/<USER>/Documents/GitHub/db-eats/android/app/.cxx/Debug/1i4ye464/x86_64", "clean"]], "buildTargetsCommandComponents": ["/usr/bin/ninja", "-C", "/home/<USER>/Documents/GitHub/db-eats/android/app/.cxx/Debug/1i4ye464/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}