import 'dart:developer';
import 'dart:io';
import 'dart:convert';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChefBloc extends Bloc<ChefEvent, ChefState> {
  ChefBloc() : super(ChefInitial()) {
    on<AddChefRatingEvent>(_onAddChefRating);
  }

  Future<void> _onAddChefRating(
      AddChefRatingEvent event, Emitter<ChefState> emit) async {
    emit(ChefRatingLoading());
    try {
      // Convert the data to string values for form data
      final formData = Map<String, String>.from({
        'chef_id': event.data['chef_id'].toString(),
        'order_id': event.data['order_id'].toString(),
        'star_rating': event.data['star_rating'].toString(),
        'comment': event.data['comment'] ?? '',
      });

      log('Sending formData: $formData'); // Add this for debugging

      final response = await ServerHelper.uploadFiles(
        '/v1/customer/chef_rating/add',
        'dish_photo',
        event.images,
        additionalData: formData,
      );
      log('Chef Rating Response: $response');

      if (response['status'] == true) {
        emit(ChefRatingAdded(
            response['message'] ?? 'Rating and photos added successfully'));
      } else {
        emit(ChefRatingError(response['message'] ?? 'Failed to add rating'));
      }
    } catch (e) {
      log('Error adding chef rating: $e');
      emit(ChefRatingError('Error occurred while adding chef rating'));
    }
  }
}

abstract class ChefEvent {}

class AddChefRatingEvent extends ChefEvent {
  final Map<String, dynamic> data;
  final List<File> images;
  AddChefRatingEvent(this.data, this.images);
}

abstract class ChefState {}

class ChefInitial extends ChefState {}

class ChefRatingLoading extends ChefState {}

class ChefRatingAdded extends ChefState {
  final String message;
  ChefRatingAdded(this.message);
}

class ChefRatingError extends ChefState {
  final String message;
  ChefRatingError(this.message);
}
