{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-5a692fd60a8469a262aa.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e569348bd62e06eae415.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-47bb43e51fca6597f87a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e569348bd62e06eae415.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-47bb43e51fca6597f87a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5a692fd60a8469a262aa.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}