import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/ui/auth/login.dart';

class ConfirmNewPassword extends StatefulWidget {
  final String email;
  final String otp;

  const ConfirmNewPassword({
    Key? key,
    required this.email,
    required this.otp,
  }) : super(key: key);

  @override
  State<ConfirmNewPassword> createState() => _ConfirmNewPasswordState();
}

class _ConfirmNewPasswordState extends State<ConfirmNewPassword> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _passwordError;
  String? _confirmPasswordError;

  void _validatePasswords() {
    setState(() {
      if (_passwordController.text.isEmpty) {
        _passwordError = 'Password is required';
      } else if (_passwordController.text.length < 6) {
        _passwordError = 'Password must be at least 6 characters';
      } else {
        _passwordError = null;
      }

      if (_confirmPasswordController.text.isEmpty) {
        _confirmPasswordError = 'Please confirm your password';
      } else if (_confirmPasswordController.text != _passwordController.text) {
        _confirmPasswordError = 'Passwords do not match';
      } else {
        _confirmPasswordError = null;
      }
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is ResetPhoneOTPVerifySuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const Login()),
            (route) => false,
          );
        } else if (state is ResetPhoneOTPVerifyFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: size.height * 0.02),
                  // Back button
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back, size: baseTextSize * 1.5),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(
                          minWidth: baseTextSize * 2.5,
                          minHeight: baseTextSize * 2.5,
                        ),
                        onPressed: () => Navigator.pop(context),
                        style: IconButton.styleFrom(
                          padding: EdgeInsets.only(
                            left: 0, // Removed left padding
                            top: size.height * 0.005,
                          ),
                        ),
                      ),
                      // SizedBox(width: size.width * 0.01),
                      Text(
                        'Back',
                        style: TextStyle(
                          fontSize: baseTextSize * 1.2,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: size.height * 0.08),
                  Center(
                    child: Text(
                      'Create new password',
                      style: TextStyle(
                        fontSize: isLandscape
                            ? size.height * 0.04
                            : baseTextSize * 1.5,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: size.height * 0.01),
                  Center(
                    child: Text(
                      "Your new password must be different\nfrom previous password",
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF414346),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: size.height * 0.04),

                  // Password fields with responsive sizing
                  _buildPasswordField(
                    controller: _passwordController,
                    hintText: 'New Password',
                    errorText: _passwordError,
                    obscureText: _obscurePassword,
                    onToggleVisibility: () =>
                        setState(() => _obscurePassword = !_obscurePassword),
                    baseTextSize: baseTextSize,
                    size: size,
                  ),
                  SizedBox(height: size.height * 0.02),
                  _buildPasswordField(
                    controller: _confirmPasswordController,
                    hintText: 'Confirm Password',
                    errorText: _confirmPasswordError,
                    obscureText: _obscureConfirmPassword,
                    onToggleVisibility: () => setState(() =>
                        _obscureConfirmPassword = !_obscureConfirmPassword),
                    baseTextSize: baseTextSize,
                    size: size,
                  ),
                  SizedBox(height: size.height * 0.04),

                  // Submit button with responsive height
                  SizedBox(
                    width: double.infinity,
                    height:
                        isLandscape ? size.height * 0.08 : size.height * 0.06,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(size.width * 0.08),
                        ),
                      ),
                      onPressed: () {
                        _validatePasswords();
                        if (_passwordError == null &&
                            _confirmPasswordError == null) {
                          context.read<MainBloc>().add(
                                ResetPhoneOTPVerify(
                                  email: widget.email,
                                  newPassword: _passwordController.text,
                                  otp: widget.otp,
                                ),
                              );
                        }
                      },
                      child: Text(
                        "Reset Password",
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: baseTextSize,
                          color: Colors.white,
                          fontFamily: 'Inter',
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String hintText,
    required String? errorText,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required double baseTextSize,
    required Size size,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      onChanged: (value) => _validatePasswords(),
      style: TextStyle(fontSize: baseTextSize),
      decoration: InputDecoration(
        filled: true,
        fillColor: Colors.white,
        hintText: hintText,
        errorText: errorText,
        hintStyle: TextStyle(
          color: Color(0xFFB9B6AD),
          fontSize: baseTextSize,
          fontWeight: FontWeight.w400,
          fontFamily: 'Inter',
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: size.width * 0.04,
          vertical: size.height * 0.018,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(size.width * 0.02),
          borderSide: BorderSide(
            color: errorText != null ? Colors.red : Color(0xFFD2D4D7),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(size.width * 0.02),
          borderSide: BorderSide(
            color: errorText != null ? Colors.red : Colors.black,
            width: 1,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(size.width * 0.02),
          borderSide: BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(size.width * 0.02),
          borderSide: BorderSide(color: Colors.red, width: 1),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility_off : Icons.visibility,
            color: Color(0xFF414346),
            size: baseTextSize * 1.2,
          ),
          onPressed: onToggleVisibility,
        ),
      ),
    );
  }
}
