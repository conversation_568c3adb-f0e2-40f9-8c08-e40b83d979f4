{"buildFiles": ["/home/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/usr/bin/ninja", "-C", "/home/<USER>/Documents/GitHub/db-eats/android/app/.cxx/Debug/1i4ye464/x86", "clean"]], "buildTargetsCommandComponents": ["/usr/bin/ninja", "-C", "/home/<USER>/Documents/GitHub/db-eats/android/app/.cxx/Debug/1i4ye464/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}