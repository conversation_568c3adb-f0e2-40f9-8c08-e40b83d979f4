import 'dart:developer';

import 'package:db_eats/ui/meal_plan/curated.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectchef.dart';
import 'package:flutter/material.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/data/models/meal_plan/servinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/existingmealplanmodel.dart';

class ChoosePlan extends StatefulWidget {
  const ChoosePlan({super.key});

  @override
  State<ChoosePlan> createState() => _ChoosePlanState();
}

class _ChoosePlanState extends State<ChoosePlan> {
  final _dateController = TextEditingController();
  int _selectedServings = 0;
  int? _selectedServingId;
  String? _selectedTimeSlotId;
  int _numDishes = 0;
  bool _letChefChoose = false;
  bool _isLoading = false;
  int _selectedPlanDays = 5;

  late List<String> _dates;
  int _currentDay = 0;
  late Map<String, Map<String, dynamic>> _mealData;
  late final MealplanBloc _mealPlanBloc;
  List<Timings> timeSlots = [];
  List<ServingSizes> servingSizes = [];
  String? _dateError;
  String? _timeSlotError;
  String? _servingError;
  String? _dishError;
  String? _planError;
  String _selectedTimeSlotFormatted = '';
  int? _existingMealPlanId;
  String _endDate = '';
  int? _mealPlanDuration;
  String? _existingEndDate;
  Color kBlack = Color(0xFF1F2122);
  Color kSecondBlack = Color(0xFF414346);

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(CheckExistingMealPlanEvent());
    _mealPlanBloc.add(ListTimingEvent());
    _mealPlanBloc.add(ListServingEvent());

    _initializeDates();

    _dates = [];
    _mealData = {};
  }

  void _initializeDates() {
    _dates = List.generate(_selectedPlanDays, (index) {
      DateTime date = DateTime.now().add(Duration(days: index));
      while (_isWeekend(date)) {
        date = date.add(const Duration(days: 1));
      }
      return _formatStorageDate(date);
    });
  }

  void _updateDatesBasedOnPlan() {
    setState(() {
      _planError = null;
      if (_dateController.text.isNotEmpty) {
        DateTime startDate = DateTime.parse(_dates[0]);
        _dates = List.generate(_selectedPlanDays, (index) {
          DateTime date = startDate.add(Duration(days: index));
          while (_isWeekend(date)) {
            date = date.add(const Duration(days: 1));
          }
          return _formatStorageDate(date);
        });
        _dateController.text = _formatDisplayDate(startDate);
      } else {
        _initializeDates();
      }
      _mealData = {
        for (var date in _dates)
          date: {
            "chefDetails": {"name": null, "image": null},
            "selectedDishes": <Map<String, dynamic>>[],
          }
      };
      _updateEndDate();
    });
  }

  void _populateExistingMealPlan(ExistingMealplandata? mealPlanData) {
    if (mealPlanData == null) {
      // If no existing meal plan, initialize with default dates
      setState(() {
        _selectedPlanDays = 5; // Default to 5-day plan
        _initializeDates();
        _mealData = {
          for (var date in _dates)
            date: {
              "chefDetails": {"name": null, "image": null},
              "selectedDishes": <Map<String, dynamic>>[],
            }
        };
      });
      return;
    }

    setState(() {
      _existingMealPlanId = mealPlanData.id;
      _mealPlanDuration = mealPlanData.mealPlanDuration;
      _existingEndDate = mealPlanData.endDate;
      _selectedPlanDays =
          _mealPlanDuration ?? mealPlanData.personalizedDays?.length ?? 5;

      // Initialize _dates based on existing meal plan's startDate
      if (mealPlanData.startDate != null) {
        final startDate = DateTime.parse(mealPlanData.startDate!);
        _dateController.text = _formatDisplayDate(startDate);
        _dates = List.generate(_selectedPlanDays, (index) {
          DateTime date = startDate.add(Duration(days: index));
          while (_isWeekend(date)) {
            date = date.add(const Duration(days: 1));
          }
          return _formatStorageDate(date);
        });
        _endDate =
            _existingEndDate ?? _formatStorageDate(DateTime.parse(_dates.last));
      } else {
        // Fallback if startDate is null
        _initializeDates();
      }

      // Populate other fields
      if (mealPlanData.timeSlotId != null) {
        _selectedTimeSlotId = mealPlanData.timeSlotId.toString();
        if (mealPlanData.timeSlot != null) {
          _selectedTimeSlotFormatted =
              '${_formatTimeToAmPm(mealPlanData.timeSlot!.startTime)} - ${_formatTimeToAmPm(mealPlanData.timeSlot!.endTime)}';
        }
      }
      if (mealPlanData.servingSizeId != null) {
        _selectedServingId = mealPlanData.servingSizeId;
        if (mealPlanData.servingSize != null) {
          _selectedServings = mealPlanData.servingSize!.serves ?? 0;
        }
      }
      if (mealPlanData.dishesPerDay != null) {
        _numDishes = mealPlanData.dishesPerDay!;
      }
      _letChefChoose = mealPlanData.mealSelectionType == "CURATED";

      // Populate _mealData
      _mealData = {
        for (var date in _dates)
          date: {
            "chefDetails": {"name": null, "image": null},
            "selectedDishes": <Map<String, dynamic>>[],
          }
      };
      if (mealPlanData.personalizedDays != null &&
          mealPlanData.personalizedDays!.isNotEmpty) {
        for (var day in mealPlanData.personalizedDays!) {
          if (day.date != null && day.chef != null) {
            _mealData[day.date!] = {
              "chefDetails": {
                "name": "${day.chef!.firstName} ${day.chef!.lastName}",
                "image": day.chef!.profilePhoto,
              },
              "selectedDishes": day.items
                      ?.map((item) => {
                            "id": item.id,
                            "name": item.menuItem?.name,
                          })
                      .toList() ??
                  [],
            };
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  bool _isWeekend(DateTime date) {
    return date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
  }

  String _formatDisplayDate(DateTime date) {
    return "${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}";
  }

  String _formatStorageDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final screenWidth = MediaQuery.of(context).size.width;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dateController.text.isNotEmpty
          ? DateTime.parse(_dates[0]).isBefore(DateTime.now())
              ? DateTime.now()
              : DateTime.parse(_dates[0])
          : DateTime.now(),
      //    initialDate: _dateController.text.isNotEmpty
      // ? DateTime.parse(_dates[0])
      // : DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      selectableDayPredicate: (DateTime date) => !_isWeekend(date),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
            textTheme: TextTheme(
              bodyMedium: TextStyle(fontSize: screenWidth * 0.04),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _dateError = null;
        _dateController.text = _formatDisplayDate(picked);
        _dates = List.generate(_selectedPlanDays, (index) {
          DateTime date = picked.add(Duration(days: index));
          while (_isWeekend(date)) {
            date = date.add(const Duration(days: 1));
          }
          return _formatStorageDate(date);
        });
        _mealData = {
          for (var date in _dates)
            date: {
              "chefDetails": {"name": null, "image": null},
              "selectedDishes": <Map<String, dynamic>>[],
            }
        };
        _updateEndDate();
      });
    }
  }

  void _clearErrors() {
    setState(() {
      _dateError = null;
      _timeSlotError = null;
      _servingError = null;
      _dishError = null;
      _planError = null;
    });
  }

  bool _validateFields() {
    _clearErrors();
    bool isValid = true;
    setState(() {
      if (_dateController.text.isEmpty) {
        _dateError = 'Please select a start date';
        isValid = false;
      } else if (_selectedTimeSlotId == null) {
        _timeSlotError = 'Please select a delivery time slot';
        isValid = false;
      } else if (_selectedServingId == null || _selectedServings == 0) {
        _servingError = 'Please select number of servings';
        isValid = false;
      } else if (_numDishes == 0) {
        _dishError = 'Please select number of dishes';
        isValid = false;
      } else if (_selectedPlanDays == 0) {
        _planError = 'Please select a meal plan duration';
        isValid = false;
      }
    });
    return isValid;
  }

  void _updateEndDate() {
    if (_dateController.text.isEmpty) return;

    DateTime startDate = DateTime.parse(_dates[0]);
    DateTime endDate = startDate;
    int daysToAdd =
        _selectedPlanDays - 1; // -1 because start date counts as day 1

    while (daysToAdd > 0) {
      endDate = endDate.add(const Duration(days: 1));
      if (!_isWeekend(endDate)) {
        daysToAdd--;
      }
    }

    setState(() {
      _endDate = _formatStorageDate(endDate);
    });
  }

  String get _endDateDisplay {
    if (_endDate.isEmpty) return '';
    try {
      DateTime endDate = DateTime.parse(_endDate);
      return _formatDisplayDate(endDate);
    } catch (e) {
      return _endDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

// Named constants
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109

    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is CheckExistingMealPlanSuccess) {
          _populateExistingMealPlan(state.data);
        }
        if (state is ListTimingSuccess) {
          final timingData = state.data as TimingListModel;
          setState(() {
            timeSlots = timingData.data?.timings ?? [];
          });
        }
        if (state is ListServingSuccess) {
          final servingData = state.data as ServingsListModel;
          setState(() {
            servingSizes = servingData.data?.servingSizes ?? [];
          });
        }
        if (state is Step1MealPlanLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is Step1MealPlanSuccess) {
          setState(() {
            _isLoading = false;
          });
          final mealPlanData = state.data;
          if (mealPlanData != null && mealPlanData['id'] != null) {
            if (_letChefChoose) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      ChooseCuisines(mealPlanId: mealPlanData['id']),
                ),
              );
            } else {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PersonailizedSelectchef(
                    mealPlanId: mealPlanData['id'],
                    dates: _dates,
                    currentday: _currentDay,
                    mealdata: _mealData,
                    dataresponce: state.data! as Map<String, dynamic>,
                    timeSlots: _selectedTimeSlotFormatted,
                  ),
                ),
              );
            }
          }
        } else if (state is Step1MealPlanFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Image.asset(
            'assets/logo.png',
            width: 112,
            height: 29,
            fit: BoxFit.contain,
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(screenHeight * 0.002),
            child: Divider(
              color: Colors.grey[300],
              height: screenHeight * 0.002,
            ),
          ),
        ),
        body: Column(
          children: [
            SizedBox(height: screenHeight * 0.02),
            Padding(
              padding: EdgeInsets.fromLTRB(screenWidth * 0.04, 0,
                  screenWidth * 0.04, screenHeight * 0.03),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(Icons.close, size: screenWidth * 0.06),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    children: [
                      Container(
                        height: screenHeight * 0.01,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1DDD5),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.01),
                        ),
                      ),
                      FractionallySizedBox(
                        widthFactor: 0.25,
                        child: Container(
                          height: screenHeight * 0.01,
                          decoration: BoxDecoration(
                            color: const Color(0xFF007A4D),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.01),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.025),
                  Text(
                    '1 of 4',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF414346),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(screenWidth * 0.04),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDatePicker(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.025),
                      _buildPlanSelection(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.025),
                      _buildDeliveryTimeSlot(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.035),
                      _buildServingsSelection(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.025),
                      _buildDishesPerDaySelector(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.025),
                      _buildMealSelectionType(screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.03),
                      _buildNavigationButtons(screenWidth, screenHeight),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatePicker(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose when to start your\nmeal plan',
          style: TextStyle(
            fontSize: twentyFour,
            fontWeight: FontWeight.w600,
            fontFamily: 'Inter',
            letterSpacing: 1,
            height: 1.28,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.03),
        Container(
          padding: EdgeInsets.all(screenWidth * 0.03),
          decoration: BoxDecoration(
            color: const Color(0xFFE1DDD5),
            borderRadius: BorderRadius.circular(screenWidth * 0.015),
          ),
          child: Text(
            'Meal plans run on weekdays only. (ex. If you start on a Friday, your next meal will be delivered the following Monday.)',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: forteen,
              height: 1.4,
              color: kBlack,
            ),
          ),
        ),
        SizedBox(height: screenHeight * 0.03),
        Text(
          'Select Start Date',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            color: kBlack,
            letterSpacing: 0.02 * screenWidth * 0.035,
          ),
        ),
        SizedBox(height: screenHeight * 0.01),
        GestureDetector(
          onTap: () => _selectDate(context),
          child: Container(
            height: screenHeight * 0.05,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.075),
              border: Border.all(
                color: const Color(0xFFD2D4D7),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Text(
                    _dateController.text.isEmpty
                        ? 'Select date'
                        : _endDate.isNotEmpty
                            ? '${_dateController.text} - $_endDateDisplay'
                            : _dateController.text,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF66696D),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Image.asset(
                  'assets/icons/calender.png',
                  width: screenWidth * 0.04,
                  height: screenWidth * 0.04,
                ),
              ],
            ),
          ),
        ),
        if (_dateError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _dateError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlanSelection(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Meal Plan Duration',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildPlanOption(2, '2-Day Plan', screenWidth, screenHeight),
            SizedBox(width: screenWidth * 0.02),
            _buildPlanOption(3, '3-Day Plan', screenWidth, screenHeight),
            SizedBox(width: screenWidth * 0.02),
            _buildPlanOption(5, '5-Day Plan', screenWidth, screenHeight),
          ],
        ),
        if (_planError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _planError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlanOption(
      int days, String title, double screenWidth, double screenHeight) {
    final bool isSelected = _selectedPlanDays == days;
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return InkWell(
      onTap: () {
        setState(() {
          _selectedPlanDays = days;
          _mealPlanDuration =
              days; // Reset meal plan duration when user selects new plan
          _updateDatesBasedOnPlan();
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.035, vertical: screenHeight * 0.015),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF1F2122) : const Color(0xFFB9B6AD),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
            fontSize: forteen,
            color: const Color(0xFF1F2122),
          ),
        ),
      ),
    );
  }

  Widget _buildDeliveryTimeSlot(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Time Slot',
          style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: forteen,
              letterSpacing: 0.02 * screenWidth * 0.035,
              color: kBlack),
        ),
        SizedBox(height: screenHeight * 0.01),
        Container(
          height: screenHeight * 0.05,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.075),
            border: Border.all(
              color: const Color(0xFFD2D4D7),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: ButtonTheme(
              alignedDropdown: true,
              child: DropdownButton<String>(
                value: _selectedTimeSlotId,
                icon: Padding(
                  padding: EdgeInsets.only(right: screenWidth * 0.03),
                  child: Image.asset(
                    'assets/icons/chevron-down.png',
                    width: screenWidth * 0.04,
                    height: screenWidth * 0.04,
                  ),
                ),
                hint: Padding(
                  padding: EdgeInsets.only(left: screenWidth * 0.04),
                  child: Text(
                    _selectedTimeSlotFormatted.isNotEmpty
                        ? _selectedTimeSlotFormatted
                        : 'Select...',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF66696D),
                    ),
                  ),
                ),
                isExpanded: true,
                dropdownColor: Colors.white,
                items: timeSlots.map<DropdownMenuItem<String>>((slot) {
                  final formattedTime =
                      '${_formatTimeToAmPm(slot.startTime)} - ${_formatTimeToAmPm(slot.endTime)}';
                  return DropdownMenuItem<String>(
                    value: slot.id.toString(),
                    child: Text(
                      formattedTime,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: screenWidth * 0.035,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF66696D),
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _timeSlotError = null;
                    _selectedTimeSlotId = value;
                    if (value != null) {
                      final selectedSlot = timeSlots
                          .firstWhere((slot) => slot.id.toString() == value);
                      _selectedTimeSlotFormatted =
                          '${_formatTimeToAmPm(selectedSlot.startTime)} - ${_formatTimeToAmPm(selectedSlot.endTime)}';
                    }
                  });
                },
              ),
            ),
          ),
        ),
        if (_timeSlotError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _timeSlotError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        SizedBox(height: screenHeight * 0.01),
        Text(
          'This is the time to expect your delivery each day.',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: twelve,
            color: kSecondBlack,
          ),
        ),
      ],
    );
  }

  Widget _buildServingsSelection(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Select Number Of Servings',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
                fontSize: forteen,
                letterSpacing: 0.02 * screenWidth * 0.035,
                color: kBlack,
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.02),
        Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How much food is 1 serving?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: twelve,
                    letterSpacing: 0.02 * screenWidth * 0.03,
                    color: kSecondBlack,
                  ),
                ),
                Container(
                  height: 1.2,
                  width: screenWidth * 0.35,
                  color: Colors.black54,
                ),
              ],
            ),
            SizedBox(width: screenWidth * 0.01),
            Container(
              width: screenWidth * 0.035,
              height: screenWidth * 0.035,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFF414346)),
              ),
              child: Center(
                child: Text(
                  '?',
                  style: TextStyle(
                    fontSize: screenWidth * 0.025,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF414346),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.025),
        ...servingSizes
            .map((serving) => Padding(
                  padding: EdgeInsets.only(bottom: screenHeight * 0.015),
                  child: _buildServingOption(
                    serving.serves ?? 1,
                    serving.title ?? '12oz',
                    screenWidth,
                    screenHeight,
                  ),
                ))
            .toList(),
        if (_servingError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _servingError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildServingOption(
      int servings, String weight, double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    final serving = servingSizes.firstWhere((s) => s.serves == servings);
    final bool isSelected = _selectedServings == servings;
    return InkWell(
      onTap: () {
        setState(() {
          _servingError = null;
          _selectedServings = servings;
          _selectedServingId = serving.id;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.035, vertical: screenHeight * 0.015),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF1F2122) : const Color(0xFFB9B6AD),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
        ),
        child: Row(
          children: [
            SizedBox(
              width: screenWidth * 0.1,
              height: screenHeight * 0.03,
              child: Stack(
                children: List.generate(
                  servings,
                  (index) => Positioned(
                    left: index * screenWidth * 0.015,
                    child: Image.asset(
                      'assets/icons/men.png',
                      width: screenWidth * 0.065,
                      height: screenWidth * 0.065,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
            //  SizedBox(width: screenWidth * 0.03),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$servings ${servings == 1 ? 'Serving' : 'Servings'}',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: forteen,
                      letterSpacing: 0.02 * screenWidth * 0.035,
                      color: kBlack,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  if (servingSizes.any(
                      (s) => s.serves == servings && s.description != null))
                    Text(
                      servingSizes
                              .firstWhere((s) => s.serves == servings)
                              .description ??
                          '',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: ten,
                        color: kSecondBlack,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDishesPerDaySelector(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Number Of Dishes To Be Delivered Per Day',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildCircleButton(
              icon: Icons.remove,
              onPressed:
                  _numDishes > 1 ? () => setState(() => _numDishes--) : null,
              screenWidth: screenWidth,
              screenHeight: screenHeight,
            ),
            Container(
              width: screenWidth * 0.15,
              height: screenHeight * 0.05,
              margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.025),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(screenWidth * 0.075),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(color: const Color(0xFFE1DDD5)),
              ),
              child: Center(
                child: Text(
                  _numDishes.toString(),
                  style: TextStyle(
                    fontSize: sixteen,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF66696D),
                  ),
                ),
              ),
            ),
            _buildCircleButton(
              icon: Icons.add,
              onPressed:
                  _numDishes < 5 ? () => setState(() => _numDishes++) : null,
              screenWidth: screenWidth,
              screenHeight: screenHeight,
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.01),
        if (_dishError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _dishError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              'Each Dish is Good For 2 Servings',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
                fontSize: ten,
                color: kBlack,
              ),
            ),
            SizedBox(width: screenWidth * 0.01),
            Container(
              width: screenWidth * 0.025,
              height: screenWidth * 0.025,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFF414346)),
              ),
              child: Center(
                child: Text(
                  '?',
                  style: TextStyle(
                    fontSize: screenWidth * 0.02,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF414346),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required double screenWidth,
    required double screenHeight,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        shape: CircleBorder(
          side: BorderSide(color: Colors.grey[300]!),
        ),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Container(
            width: screenWidth * 0.1,
            height: screenWidth * 0.1,
            alignment: Alignment.center,
            child: Icon(icon,
                size: screenWidth * 0.05, color: const Color(0xFF1F2122)),
          ),
        ),
      ),
    );
  }

  Widget _buildMealSelectionType(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Meal Selection Type',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        _buildMealSelectionOption(
          icon: Icons.restaurant,
          title: 'Let The Chef Choose For Me',
          subtitle: 'The chef chooses meals based on your\npreferences',
          isSelected: _letChefChoose,
          onTap: () => setState(() => _letChefChoose = true),
          screenWidth: screenWidth,
          screenHeight: screenHeight,
        ),
        SizedBox(height: screenHeight * 0.015),
        _buildMealSelectionOption(
          icon: Icons.restaurant_menu,
          title: 'I Want To Choose My Meals',
          subtitle: 'You choose your meals',
          isSelected: !_letChefChoose,
          onTap: () => setState(() => _letChefChoose = false),
          screenWidth: screenWidth,
          screenHeight: screenHeight,
        ),
      ],
    );
  }

  Widget _buildMealSelectionOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
    required double screenWidth,
    required double screenHeight,
  }) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(screenWidth * 0.04),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF1F2122) : const Color(0xFFE1DDD5),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: screenHeight * 0.005),
              child: Image.asset(
                'assets/icons/meal.png',
                width: screenWidth * 0.045,
                height: screenWidth * 0.045,
                color: const Color(0xFF1F2122),
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(width: screenWidth * 0.035),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: sixteen,
                      letterSpacing: 0.02 * screenWidth * 0.035,
                      color: kBlack,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: twelve,
                      color: kSecondBlack,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035); // ≈ 0.02545
    final double twelve = screenWidth * (12 / 13.751 * 0.035); // ≈ 0.03054
    final double forteen = screenWidth * 0.035; // 13.751
    final double sixteen = screenWidth * (16 / 13.751 * 0.035); // ≈ 0.04073
    final double eighteen = screenWidth * (18 / 13.751 * 0.035); // ≈ 0.04582
    final double twenty = screenWidth * (20 / 13.751 * 0.035); // ≈ 0.05091
    final double twentyFour = screenWidth * (24 / 13.751 * 0.035); // ≈ 0.06109
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF6F3EC),
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenWidth * 0.07),
              side: const BorderSide(color: Color(0xFF1F2122)),
            ),
            padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
            //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          ),
          child: Text(
            'Back',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: sixteen,
              letterSpacing: 0.32,
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading
              ? null
              : () {
                  if (!_validateFields()) return;
                  final mealPlanData = {
                    if (_existingMealPlanId != null)
                      "meal_plan_id": _existingMealPlanId,
                    "start_date": _dates[0],
                    "end_date": _endDate,
                    "meal_plan_duration": _selectedPlanDays,
                    "time_slot_id": int.parse(_selectedTimeSlotId!),
                    "serving_size_id": _selectedServingId,
                    "dishes_per_day": _numDishes,
                    "meal_selection_type":
                        _letChefChoose ? "CURATED" : "PERSONALIZED",
                    "plan_days": _selectedPlanDays,
                  };
                  _mealPlanBloc.add(Step1MealPlanEvent(mealPlanData));
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenWidth * 0.07),
            ),
            padding: EdgeInsets.symmetric(
                horizontal:
                    _isLoading ? screenWidth * 0.04 : screenWidth * 0.05,
                vertical: screenHeight * 0.02),
            //  horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
            //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          ),
          child: _isLoading
              ? SizedBox(
                  width: screenWidth * 0.05,
                  height: screenWidth * 0.05,
                  child: const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Text(
                  'Next',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: screenWidth * 0.04,
                    letterSpacing: 0.32,
                    color: Colors.white,
                  ),
                ),
        ),
      ],
    );
  }
}
