# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\db_eats" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.9+10" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 9 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 10 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "PROJECT_DIR=D:\\db_eats"
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\db_eats\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\db_eats"
  "FLUTTER_TARGET=D:\\db_eats\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\db_eats\\.dart_tool\\package_config.json"
)
