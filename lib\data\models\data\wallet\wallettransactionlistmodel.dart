class WalletTransactionListModel {
  bool? status;
  int? statusCode;
  Data? data;

  WalletTransactionListModel({this.status, this.statusCode, this.data});

  WalletTransactionListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Transactions>? transactions;
  double? balance;
  Pagination? pagination;

  Data({this.transactions, this.balance, this.pagination});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['transactions'] != null) {
      transactions = <Transactions>[];
      json['transactions'].forEach((v) {
        transactions!.add(Transactions.fromJson(v));
      });
    }
    balance = json['balance'];
    pagination = json['pagination'] != null
        ? Pagination.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (transactions != null) {
      data['transactions'] = transactions!.map((v) => v.toJson()).toList();
    }
    data['balance'] = balance;
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }
}

class Transactions {
  int? id;
  int? customerWalletId;
  String? transactionType;
  int? type;
  Null targetId;
  double? amount;
  String? title;
  Null description;
  String? status;
  String? createdAt;
  String? updatedAt;

  Transactions(
      {this.id,
      this.customerWalletId,
      this.transactionType,
      this.type,
      this.targetId,
      this.amount,
      this.title,
      this.description,
      this.status,
      this.createdAt,
      this.updatedAt});

  Transactions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerWalletId = json['customer_wallet_id'];
    transactionType = json['transaction_type'];
    type = json['type'];
    targetId = json['target_id'];
    amount = json['amount'] != null ? (json['amount'] as num).toDouble() : null;
    title = json['title'];
    description = json['description'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['customer_wallet_id'] = customerWalletId;
    data['transaction_type'] = transactionType;
    data['type'] = type;
    data['target_id'] = targetId;
    data['amount'] = amount;
    data['title'] = title;
    data['description'] = description;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Pagination {
  int? totalRecords;
  int? totalPages;
  int? currentPage;
  int? perPage;

  Pagination(
      {this.totalRecords, this.totalPages, this.currentPage, this.perPage});

  Pagination.fromJson(Map<String, dynamic> json) {
    totalRecords = json['total_records'];
    totalPages = json['total_pages'];
    currentPage = json['current_page'];
    perPage = json['per_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_records'] = totalRecords;
    data['total_pages'] = totalPages;
    data['current_page'] = currentPage;
    data['per_page'] = perPage;
    return data;
  }
}
