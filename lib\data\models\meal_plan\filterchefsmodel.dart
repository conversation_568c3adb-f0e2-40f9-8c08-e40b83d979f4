class FilterChefModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  FilterChefModel({this.status, this.message, this.statusCode, this.data});

  FilterChefModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? total;
  int? page;
  int? limit;
  List<Chefs>? chefs;

  Data({this.total, this.page, this.limit, this.chefs});

  Data.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['chefs'] != null) {
      chefs = <Chefs>[];
      json['chefs'].forEach((v) {
        chefs!.add(new Chefs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.chefs != null) {
      data['chefs'] = this.chefs!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chefs {
  int? chefId;
  String? profilePhoto;
  String? coverPhoto;
  Location? location;
  List<String>? searchTags;
  double? distance;
  Chef? chef;

  Chefs(
      {this.chefId,
      this.profilePhoto,
      this.coverPhoto,
      this.location,
      this.searchTags,
      this.distance,
      this.chef});

  Chefs.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    // Handle null search_tags
    searchTags = json['search_tags'] != null
        ? List<String>.from(json['search_tags'])
        : [];
    distance = json['distance'];
    chef = json['chef'] != null ? new Chef.fromJson(json['chef']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chef_id'] = this.chefId;
    data['profile_photo'] = this.profilePhoto;
    data['cover_photo'] = this.coverPhoto;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['search_tags'] = this.searchTags;
    data['distance'] = this.distance;
    if (this.chef != null) {
      data['chef'] = this.chef!.toJson();
    }
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}

class Chef {
  String? firstName;
  String? lastName;
  List<OperationDays>? operationDays;
  List<OperationTimes>? operationTimes;
  List<Dishes>? dishes;

  Chef(
      {this.firstName,
      this.lastName,
      this.operationDays,
      this.operationTimes,
      this.dishes});

  Chef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    if (json['operation_days'] != null) {
      operationDays = <OperationDays>[];
      json['operation_days'].forEach((v) {
        operationDays!.add(new OperationDays.fromJson(v));
      });
    }
    if (json['operation_times'] != null) {
      operationTimes = <OperationTimes>[];
      json['operation_times'].forEach((v) {
        operationTimes!.add(new OperationTimes.fromJson(v));
      });
    }
    if (json['dishes'] != null) {
      dishes = <Dishes>[];
      json['dishes'].forEach((v) {
        dishes!.add(new Dishes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    if (this.operationDays != null) {
      data['operation_days'] =
          this.operationDays!.map((v) => v.toJson()).toList();
    }
    if (this.operationTimes != null) {
      data['operation_times'] =
          this.operationTimes!.map((v) => v.toJson()).toList();
    }
    if (this.dishes != null) {
      data['dishes'] = this.dishes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OperationDays {
  int? dayId;
  Properties? day;

  OperationDays({this.dayId, this.day});

  OperationDays.fromJson(Map<String, dynamic> json) {
    dayId = json['day_id'];
    day = json['day'] != null ? new Properties.fromJson(json['day']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['day_id'] = this.dayId;
    if (this.day != null) {
      data['day'] = this.day!.toJson();
    }
    return data;
  }
}

class OperationTimes {
  int? timingId;

  OperationTimes({this.timingId});

  OperationTimes.fromJson(Map<String, dynamic> json) {
    timingId = json['timing_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['timing_id'] = this.timingId;
    return data;
  }
}

class Dishes {
  int? id;
  String? name;
  String? photo;
  int? listingOrder;
  List<ServingSizePrices>? servingSizePrices;

  Dishes(
      {this.id,
      this.name,
      this.photo,
      this.listingOrder,
      this.servingSizePrices});

  Dishes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    listingOrder = json['listing_order'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(new ServingSizePrices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    data['listing_order'] = this.listingOrder;
    if (this.servingSizePrices != null) {
      data['serving_size_prices'] =
          this.servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;

  ServingSizePrices({this.id, this.servingSizeId, this.price});

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['serving_size_id'] = this.servingSizeId;
    data['price'] = this.price;
    return data;
  }
}
