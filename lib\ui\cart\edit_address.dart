import 'package:db_eats/bloc/account_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';

class EditAddressPage extends StatefulWidget {
  String? currentAddress;
  EditAddressPage({super.key, required this.currentAddress});

  @override
  _EditAddressPageState createState() => _EditAddressPageState();
}

class _EditAddressPageState extends State<EditAddressPage> {
  final Completer<GoogleMapController> _controller = Completer();
  LatLng currentLatLng = const LatLng(9.5260001, 76.8144292);

  String buildingType = 'House';
  String houseNumber = '';
  String landmark = '';
  late String currentAddress;
  String selectedCoordinates = '';

  bool isLoading = false;

  final List<String> buildingTypes = [
    'House',
    'Apartment',
    'Office',
    'Condo',
    'Other',
  ];
  final TextEditingController houseNumberController = TextEditingController();
  final TextEditingController landmarkController = TextEditingController();

  final Set<Marker> _markers = {};
  LatLng? tappedPosition;
  bool adjustPinMode = false;

  late final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers;

  @override
  void initState() {
    super.initState();
    currentAddress = widget.currentAddress ?? '';
    gestureRecognizers = {
      Factory<PanGestureRecognizer>(() => PanGestureRecognizer()),
      Factory<ScaleGestureRecognizer>(() => ScaleGestureRecognizer()),
      Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
      Factory<VerticalDragGestureRecognizer>(
          () => VerticalDragGestureRecognizer()),
    };
    _markers.add(
      Marker(
        markerId: const MarkerId('current'),
        position: currentLatLng,
      ),
    );
  }

  @override
  void dispose() {
    houseNumberController.dispose();
    landmarkController.dispose();
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    developer.log('Map controller being created');
    _controller.complete(controller);
    _testApiKey();
  }

  Future<void> _testApiKey() async {
    try {
      final GoogleMapController controller = await _controller.future;
      developer.log('Map controller ready - API key working');
    } catch (e) {
      developer.log('Map initialization error: $e');
    }
  }

  void _onMapTap(LatLng position) {
    setState(() {
      currentLatLng = position;
      _updateAddress(position);
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('tapped'),
          position: position,
        ),
      );
    });
  }

  Future<void> _updateAddress(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        setState(() {
          selectedCoordinates = '${position.latitude}, ${position.longitude}';
          currentAddress =
              '${place.street}, ${place.subLocality}, ${place.locality}';
          if (currentAddress.startsWith(',')) {
            currentAddress = currentAddress.replaceFirst(',', '').trim();
          }
        });
      }
    } catch (e) {
      setState(() {
        selectedCoordinates = '${position.latitude}, ${position.longitude}';
        currentAddress = 'Location not found';
      });
    }
  }

  void _onCameraMove(CameraPosition position) {
    if (adjustPinMode) {
      setState(() {
        _markers.clear();
        _markers.add(
          Marker(
            markerId: const MarkerId('adjustable'),
            position: position.target,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              color: const Color(0xFFF6F3EC),
              padding: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.04,
                vertical: screenSize.height * 0.015,
              ),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(
                      Icons.close,
                      size: isLargeScreen ? 28 : screenSize.width * 0.06,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Edit Address',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(width: screenSize.width * 0.06),
                ],
              ),
            ),

            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(screenSize.width * 0.04),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Map Card
                        Padding(
                          padding: EdgeInsets.all(screenSize.width * 0.02),
                          child: Card(
                            elevation: 0,
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Container(
                              height:
                                  isLargeScreen ? 500 : screenSize.height * 0.5,
                              decoration: const BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12)),
                              ),
                              child: ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(12)),
                                child: Stack(
                                  children: [
                                    GoogleMap(
                                      onMapCreated: _onMapCreated,
                                      markers: _markers,
                                      onCameraMove: _onCameraMove,
                                      onTap: adjustPinMode ? null : _onMapTap,
                                      scrollGesturesEnabled: true,
                                      zoomGesturesEnabled: true,
                                      mapType: MapType.normal,
                                      myLocationEnabled: true,
                                      initialCameraPosition: CameraPosition(
                                        target: currentLatLng,
                                        zoom: 15,
                                        tilt: 20.0,
                                      ),
                                      gestureRecognizers: gestureRecognizers,
                                      zoomControlsEnabled: false,
                                      myLocationButtonEnabled: false,
                                      mapToolbarEnabled: false,
                                      compassEnabled: false,
                                      buildingsEnabled: false,
                                      minMaxZoomPreference:
                                          const MinMaxZoomPreference(3, 20),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: screenSize.width * 0.06,
                                        vertical: screenSize.height * 0.01,
                                      ),
                                      child: Text(
                                        adjustPinMode
                                            ? 'Fix Pin'
                                            : 'Adjust Pin',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w500,
                                          fontSize: isLargeScreen
                                              ? 16
                                              : screenSize.width * 0.035,
                                        ),
                                      ),
                                    ),
                                    // Adjust Pin button
                                    Positioned(
                                      bottom: screenSize.height * 0.06,
                                      left: 0,
                                      right: 0,
                                      child: Center(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: adjustPinMode
                                                ? Colors.black
                                                : Colors.black,
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          child: Material(
                                            color: Colors.transparent,
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(25),
                                              onTap: () {
                                                setState(() {
                                                  adjustPinMode =
                                                      !adjustPinMode;
                                                  if (adjustPinMode) {
                                                    _markers.clear();
                                                    _markers.add(
                                                      Marker(
                                                        markerId:
                                                            const MarkerId(
                                                                'adjustable'),
                                                        position: currentLatLng,
                                                        icon: BitmapDescriptor
                                                            .defaultMarkerWithHue(
                                                                BitmapDescriptor
                                                                    .hueBlue),
                                                      ),
                                                    );
                                                  } else {
                                                    _updateAddress(_markers
                                                        .first.position);
                                                    _markers.clear();
                                                    _markers.add(
                                                      Marker(
                                                        markerId:
                                                            const MarkerId(
                                                                'fixed'),
                                                        position: currentLatLng,
                                                      ),
                                                    );
                                                  }
                                                });
                                              },
                                              child: Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      screenSize.width * 0.04,
                                                  vertical:
                                                      screenSize.height * 0.01,
                                                ),
                                                child: Text(
                                                  adjustPinMode
                                                      ? 'Fix Pin'
                                                      : 'Adjust Pin',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontFamily:
                                                        'Inter-Semibold',
                                                    fontSize: isLargeScreen
                                                        ? 14
                                                        : screenSize.width *
                                                            0.03,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: screenSize.height * 0.015),

                        // Form fields
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: screenSize.width * 0.04),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Address display
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/icons/location.png',
                                          width: isLargeScreen
                                              ? 18
                                              : screenSize.width * 0.04,
                                          height: isLargeScreen
                                              ? 18
                                              : screenSize.width * 0.04,
                                          color: const Color(0xFF1F2122),
                                        ),
                                        SizedBox(
                                            width: screenSize.width * 0.01),
                                        Flexible(
                                          child: Text(
                                            currentAddress,
                                            style: TextStyle(
                                              fontSize: isLargeScreen
                                                  ? 16
                                                  : screenSize.width * 0.035,
                                              fontWeight: FontWeight.w700,
                                              fontFamily: 'Inter-Bold',
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: screenSize.height * 0.018),
                              const Divider(
                                color: Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              SizedBox(height: screenSize.height * 0.018),

                              // Building Type
                              Text(
                                'Building Type',
                                style: TextStyle(
                                    fontSize: isLargeScreen
                                        ? 16
                                        : screenSize.width * 0.035,
                                    fontFamily: 'Inter-medium',
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(height: screenSize.height * 0.01),
                              Container(
                                width: double.infinity,
                                height: isLargeScreen
                                    ? 52
                                    : screenSize.height * 0.045,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: const Color(0xFFE1E3E6),
                                    width: 1,
                                  ),
                                  borderRadius: BorderRadius.circular(24),
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: screenSize.width * 0.04),
                                  child: DropdownButton<String>(
                                    value: buildingType,
                                    isExpanded: true,
                                    icon: Image.asset(
                                      'assets/icons/chevron-down.png',
                                      width: isLargeScreen
                                          ? 24
                                          : screenSize.width * 0.05,
                                      height: isLargeScreen
                                          ? 24
                                          : screenSize.width * 0.05,
                                    ),
                                    underline: const SizedBox(),
                                    style: TextStyle(
                                      color: const Color(0xFF1F2122),
                                      fontSize: isLargeScreen
                                          ? 16
                                          : screenSize.width * 0.035,
                                      fontFamily: 'Inter',
                                    ),
                                    dropdownColor: Colors.white,
                                    items: buildingTypes
                                        .map<DropdownMenuItem<String>>(
                                            (String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(
                                          value,
                                          style: TextStyle(
                                            color: const Color(0xFF66696D),
                                            fontSize: isLargeScreen
                                                ? 16
                                                : screenSize.width * 0.035,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) {
                                      setState(() {
                                        buildingType = newValue!;
                                      });
                                    },
                                  ),
                                ),
                              ),

                              SizedBox(height: screenSize.height * 0.02),

                              // House Number
                              Text(
                                'House / Apt / Unit / Floor Number',
                                style: TextStyle(
                                  fontSize: isLargeScreen
                                      ? 16
                                      : screenSize.width * 0.035,
                                  fontFamily: 'Inter-medium',
                                ),
                              ),
                              SizedBox(height: screenSize.height * 0.01),
                              TextField(
                                controller: houseNumberController,
                                style: TextStyle(
                                  color: const Color(0xFF1F2937),
                                  fontSize: isLargeScreen
                                      ? 18
                                      : screenSize.width * 0.04,
                                ),
                                decoration: InputDecoration(
                                  hintText: 'Example: 143',
                                  hintStyle: TextStyle(
                                    color: const Color(0xFF9CA3AF),
                                    fontSize: isLargeScreen
                                        ? 18
                                        : screenSize.width * 0.04,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 1,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 2,
                                    ),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: screenSize.width * 0.04,
                                    vertical: screenSize.height * 0.005,
                                  ),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    houseNumber = value;
                                  });
                                },
                              ),

                              SizedBox(height: screenSize.height * 0.02),

                              // Landmark
                              Text(
                                'Landmark Or Other Details',
                                style: TextStyle(
                                  fontSize: isLargeScreen
                                      ? 16
                                      : screenSize.width * 0.035,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenSize.height * 0.01),
                              TextField(
                                controller: landmarkController,
                                style: TextStyle(
                                  color: const Color(0xFF1F2937),
                                  fontSize: isLargeScreen
                                      ? 18
                                      : screenSize.width * 0.04,
                                ),
                                decoration: InputDecoration(
                                  hintText: 'Example: Near a fountain',
                                  hintStyle: TextStyle(
                                    color: const Color(0xFF9CA3AF),
                                    fontSize: isLargeScreen
                                        ? 18
                                        : screenSize.width * 0.04,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 1,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFE1E3E6),
                                      width: 2,
                                    ),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: screenSize.width * 0.04,
                                  vertical: screenSize.height * 0.005,
                                  ),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    landmark = value;
                                  });
                                },
                              ),

                              SizedBox(height: screenSize.height * 0.03),

                              // Confirm Button
                              SizedBox(
                                width: double.infinity,
                                height: isLargeScreen
                                    ? 52
                                    : screenSize.height * 0.06,
                                child: BlocListener<AccountBloc, AccountState>(
                                  listener: (context, state) {
                                    if (state is AddAddressSuccess) {
                                      context
                                          .read<AccountBloc>()
                                          .add(ListAddressesEvent());
                                      Navigator.pop(context);
                                    } else if (state is AddAddressLoading) {
                                      setState(() {
                                        isLoading = true;
                                      });
                                    } else if (state is AddAddressFailed) {
                                      setState(() {
                                        isLoading = false;
                                      });
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(state.message),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }
                                  },
                                  child: ElevatedButton(
                                    onPressed: isLoading
                                        ? null
                                        : () {
                                            final addressData = {
                                              "latitude": _markers
                                                  .first.position.latitude,
                                              "longitude": _markers
                                                  .first.position.longitude,
                                              "address_text": currentAddress,
                                              "building_type": buildingType,
                                              "house_number":
                                                  houseNumberController.text,
                                              "landmark":
                                                  landmarkController.text,
                                              "is_current": true,
                                            };

                                            setState(() => isLoading = true);
                                            context.read<AccountBloc>().add(
                                                AddAddressEvent(addressData));
                                          },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.black,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(24),
                                      ),
                                    ),
                                    child: isLoading
                                        ? SizedBox(
                                            height: isLargeScreen
                                                ? 24
                                                : screenSize.width * 0.05,
                                            width: isLargeScreen
                                                ? 24
                                                : screenSize.width * 0.05,
                                            child:
                                                const CircularProgressIndicator(
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white),
                                              strokeWidth: 2,
                                            ),
                                          )
                                        : Text(
                                            'Confirm',
                                            style: TextStyle(
                                              fontSize: isLargeScreen
                                                  ? 18
                                                  : screenSize.width * 0.04,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
