import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// FilterModal widget
class FilterModal extends StatefulWidget {
  final double screenWidth;

  const FilterModal({
    super.key,
    required this.screenWidth,
  });

  @override
  State<FilterModal> createState() => _FilterModalState();
}

class _FilterModalState extends State<FilterModal> {
  // State for selected filters
  String selectedShowType = '';
  String selectedSortBy = '';
  final Set<String> selectedCuisines = {};
  final Set<String> selectedDietaries = {};
  double selectedRating = 0.0;
  double selectedRatingMin = 3.0;
  double selectedRatingMax = 5.0;
  String selectedPricing = '';
  final Set<String> selectedDishTypes = {};
  String selectedSpiceLevel = '';

  // State for cuisine IDs
  final Set<int> selectedCuisineIds = {};
  final Set<int> selectedSubCuisineIds = {};
  final Set<int> selectedLocalCuisineIds = {};

  // Expansion state for each section
  final Map<String, bool> _expandedSections = {
    'Show Type': false,
    'Sort By': false,
    'Cuisines': false,
    'Dietary': false,
    'Rating': false,
    'Pricing': false,
    'Dish Type': false,
    'Spice Level': false,
  };

  // Expansion state for cuisine subsections
  final Map<String, bool> _expandedCuisines = {};

  void _toggleSection(String sectionName) {
    setState(() {
      _expandedSections[sectionName] =
          !(_expandedSections[sectionName] ?? false);
    });
  }

  void _toggleCuisineSection(String cuisineName) {
    setState(() {
      _expandedCuisines[cuisineName] =
          !(_expandedCuisines[cuisineName] ?? false);
    });
  }

  void _handleCuisineSelection(String cuisineName, int cuisineId, bool value,
      {List<dynamic>? subCuisines}) {
    setState(() {
      if (value) {
        selectedCuisines.add(cuisineName);
        selectedCuisineIds.add(cuisineId);
        selectedCuisines.remove('All');
      } else {
        selectedCuisines.remove(cuisineName);
        selectedCuisineIds.remove(cuisineId);
      }
    });
  }

  void _handleSubCuisineSelection(
      String subCuisineName, int subCuisineId, bool value,
      {List<dynamic>? localCuisines,
      String? parentCuisineName,
      int? parentCuisineId,
      List<dynamic>? allSubCuisines}) {
    setState(() {
      if (value) {
        selectedCuisines.add(subCuisineName);
        selectedSubCuisineIds.add(subCuisineId);
        // Also select parent cuisine if it exists
        if (parentCuisineName != null && parentCuisineId != null) {
          selectedCuisines.add(parentCuisineName);
          selectedCuisineIds.add(parentCuisineId);
        }
        selectedCuisines.remove('All');
      } else {
        selectedCuisines.remove(subCuisineName);
        selectedSubCuisineIds.remove(subCuisineId);
        // Check if there are any other selected sub-cuisines under this parent
        if (allSubCuisines != null &&
            parentCuisineName != null &&
            parentCuisineId != null) {
          bool hasOtherSelectedSubCuisines = allSubCuisines.any((sub) =>
              sub.name != subCuisineName &&
              selectedSubCuisineIds.contains(sub.id));
          if (!hasOtherSelectedSubCuisines) {
            selectedCuisines.remove(parentCuisineName);
            selectedCuisineIds.remove(parentCuisineId);
          }
        }
      }
    });
  }

  void _handleLocalCuisineSelection(
      String localCuisineName, int localCuisineId, bool value,
      {String? parentSubCuisineName,
      int? parentSubCuisineId,
      List<dynamic>? allLocalCuisines,
      String? grandParentCuisineName,
      int? grandParentCuisineId,
      List<dynamic>? allSubCuisines}) {
    setState(() {
      if (value) {
        selectedCuisines.add(localCuisineName);
        selectedLocalCuisineIds.add(localCuisineId);
        // Select parent sub-cuisine
        if (parentSubCuisineName != null && parentSubCuisineId != null) {
          selectedCuisines.add(parentSubCuisineName);
          selectedSubCuisineIds.add(parentSubCuisineId);
        }
        // Select grand-parent cuisine
        if (grandParentCuisineName != null && grandParentCuisineId != null) {
          selectedCuisines.add(grandParentCuisineName);
          selectedCuisineIds.add(grandParentCuisineId);
        }
        selectedCuisines.remove('All');
      } else {
        selectedCuisines.remove(localCuisineName);
        selectedLocalCuisineIds.remove(localCuisineId);
        // Check if there are any other selected local cuisines under this parent
        if (allLocalCuisines != null &&
            parentSubCuisineName != null &&
            parentSubCuisineId != null) {
          bool hasOtherSelectedLocalCuisines = allLocalCuisines.any((local) =>
              local.name != localCuisineName &&
              selectedLocalCuisineIds.contains(local.id));
          if (!hasOtherSelectedLocalCuisines) {
            selectedCuisines.remove(parentSubCuisineName);
            selectedSubCuisineIds.remove(parentSubCuisineId);
            // Check if there are any other selected sub-cuisines under the grand-parent
            if (allSubCuisines != null &&
                grandParentCuisineName != null &&
                grandParentCuisineId != null) {
              bool hasOtherSelectedSubCuisines = allSubCuisines.any((sub) =>
                  sub.name != parentSubCuisineName &&
                  selectedSubCuisineIds.contains(sub.id));
              if (!hasOtherSelectedSubCuisines) {
                selectedCuisines.remove(grandParentCuisineName);
                selectedCuisineIds.remove(grandParentCuisineId);
              }
            }
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final filterData = Initializer.filterdataModel.data;
    if (filterData == null) {
      return const Center(child: Text('No filter data available'));
    }

    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: const Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Inter',
                  ),
                ),
              ),
              Row(
                children: [
                  TextButton(
                    onPressed: () {
                      context.read<HomeBloc>().add(GetHomeDataEvent(
                            data: <String, dynamic>{
                              'latitude': Initializer.latitude ?? 0.0,
                              'longitude': Initializer.longitude ?? 0.0,
                            },
                          ));
                      Navigator.pop(context);
                      setState(() {
                        selectedShowType = 'All';
                        selectedSortBy = 'Recommended';
                        selectedCuisines.clear();
                        selectedDietaries.clear();
                        selectedRating = 3.0;
                        selectedPricing = '\$\$';
                        selectedDishTypes.clear();
                        selectedSpiceLevel = 'No Spice';
                        selectedCuisineIds.clear();
                        selectedSubCuisineIds.clear();
                        selectedLocalCuisineIds.clear();

                        // Clear stored filters
                        Initializer.setAppliedFilters(null);
                      });
                    },
                    child: const Text(
                      'Clear',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontFamily: 'Inter',
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      final lat = Initializer.latitude ?? 0.0;
                      final lng = Initializer.longitude ?? 0.0;
                      final filterData = Initializer.filterdataModel.data;
                      if (filterData == null) return;

                      int? spiceLevelId;
                      if (selectedSpiceLevel.isNotEmpty) {
                        try {
                          spiceLevelId = filterData.spiceLevels
                              ?.firstWhere((s) => s.name == selectedSpiceLevel)
                              .id;
                        } catch (_) {
                          spiceLevelId = null;
                        }
                      }

                      final Map<String, dynamic> selectedFilters = {
                        "latitude": lat,
                        "longitude": lng,
                        "cuisine_ids": selectedCuisineIds.toList(),
                        "sub_cuisine_ids": selectedSubCuisineIds.toList(),
                        "local_cuisine_ids": selectedLocalCuisineIds.toList(),
                        "dietary_ids": filterData.dietaries
                                ?.where(
                                    (d) => selectedDietaries.contains(d.name))
                                .map((d) => d.id)
                                .toList() ??
                            [],
                        "dishtype_ids": filterData.dishTypes
                                ?.where(
                                    (d) => selectedDishTypes.contains(d.name))
                                .map((d) => d.id)
                                .toList() ??
                            [],
                        // Only add spice_level_ids if a spice level is selected

                        "spice_level_ids":
                            spiceLevelId != null ? [spiceLevelId] : [],
                      };

                      // Store the applied filters
                      await Initializer.setAppliedFilters(selectedFilters);

                      context
                          .read<HomeBloc>()
                          .add(GetHomeDataEvent(data: selectedFilters));
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22),
                      ),
                    ),
                    child: const Text(
                      'Apply',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
              ),
            ],
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding:
                  EdgeInsets.symmetric(horizontal: widget.screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),

                  // // Show Type
                  // _buildExpandableSection(
                  //   'Show Type',
                  //   _expandedSections['Show Type']!,
                  //   Column(
                  //     children: [
                  //       _buildCustomRadioTile('All', selectedShowType, (value) {
                  //         setState(() => selectedShowType = value!);
                  //       }),
                  //       _buildCustomRadioTile('Chefs', selectedShowType,
                  //           (value) {
                  //         setState(() => selectedShowType = value!);
                  //       }),
                  //       _buildCustomRadioTile('Dishes', selectedShowType,
                  //           (value) {
                  //         setState(() => selectedShowType = value!);
                  //       }),
                  //     ],
                  //   ),
                  // ),

                  // // Sort By
                  // _buildExpandableSection(
                  //   'Sort By',
                  //   _expandedSections['Sort By']!,
                  //   Column(
                  //     children: [
                  //       _buildCustomRadioTile('Recommended', selectedSortBy,
                  //           (value) {
                  //         setState(() => selectedSortBy = value!);
                  //       }),
                  //       _buildCustomRadioTile('Rating', selectedSortBy,
                  //           (value) {
                  //         setState(() => selectedSortBy = value!);
                  //       }),
                  //       _buildCustomRadioTile('Delivery Time', selectedSortBy,
                  //           (value) {
                  //         setState(() => selectedSortBy = value!);
                  //       }),
                  //     ],
                  //   ),
                  // ),

                  // Cuisines
                  _buildExpandableSection(
                    'Cuisines',
                    _expandedSections['Cuisines']!,
                    Column(
                      children: [
                        _buildCustomCheckboxTile(
                          'All',
                          selectedCuisines.contains('All'),
                          (value) {
                            setState(() {
                              if (value!) {
                                // Clear existing selections
                                selectedCuisines.clear();
                                selectedCuisineIds.clear();
                                selectedSubCuisineIds.clear();
                                selectedLocalCuisineIds.clear();

                                // Add 'All' and select all cuisines
                                selectedCuisines.add('All');

                                // Select all main cuisines and their sub-cuisines
                                for (var cuisine in filterData.cuisines ?? []) {
                                  selectedCuisines.add(cuisine.name!);
                                  selectedCuisineIds.add(cuisine.id!);

                                  // Select all subcuisines
                                  if (cuisine.subCuisines != null) {
                                    for (var subCuisine
                                        in cuisine.subCuisines!) {
                                      selectedCuisines.add(subCuisine.name!);
                                      selectedSubCuisineIds.add(subCuisine.id!);

                                      // Select all local cuisines
                                      if (subCuisine.localCuisines != null) {
                                        for (var localCuisine
                                            in subCuisine.localCuisines!) {
                                          selectedCuisines
                                              .add(localCuisine.name!);
                                          selectedLocalCuisineIds
                                              .add(localCuisine.id!);
                                        }
                                      }
                                    }
                                  }
                                }
                              } else {
                                // Clear all selections
                                selectedCuisines.clear();
                                selectedCuisineIds.clear();
                                selectedSubCuisineIds.clear();
                                selectedLocalCuisineIds.clear();
                              }
                            });
                          },
                        ),
                        ...(filterData.cuisines ?? [])
                            .map((cuisine) => _buildCuisineHierarchy(cuisine)),
                      ],
                    ),
                  ),

                  // Dietary
                  _buildExpandableSection(
                    'Dietary',
                    _expandedSections['Dietary']!,
                    Column(
                      children: (filterData.dietaries ?? [])
                          .map((dietary) => _buildCustomCheckboxTile(
                                dietary.name!,
                                selectedDietaries.contains(dietary.name),
                                (value) {
                                  setState(() {
                                    if (value!) {
                                      selectedDietaries.add(dietary.name!);
                                    } else {
                                      selectedDietaries.remove(dietary.name!);
                                    }
                                  });
                                },
                              ))
                          .toList(),
                    ),
                  ),

                  // Rating
                  _buildExpandableSection(
                    'Rating',
                    _expandedSections['Rating']!,
                    Column(
                      children: [
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Text(
                              '${selectedRatingMin.toStringAsFixed(1)}+',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1A1A1A),
                                fontFamily: 'Inter',
                              ),
                            ),
                            const Spacer(),
                            Text(
                              selectedRatingMax.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF666666),
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: const Color(0xFF1A1A1A),
                            inactiveTrackColor: const Color(0xFFE5E5E5),
                            thumbColor: const Color(0xFF1A1A1A),
                            thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 4),
                            overlayShape: const RoundSliderOverlayShape(
                                overlayRadius: 16),
                            trackHeight: 1,
                          ),
                          child: RangeSlider(
                            values: RangeValues(
                                selectedRatingMin, selectedRatingMax),
                            min: 3.0,
                            max: 5.0,
                            divisions: 8,
                            labels: RangeLabels(
                              '${selectedRatingMin.toStringAsFixed(1)}+',
                              selectedRatingMax.toStringAsFixed(1),
                            ),
                            onChanged: (RangeValues values) {
                              setState(() {
                                selectedRatingMin = values.start;
                                selectedRatingMax = values.end;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  //                   // Rating
                  // _buildExpandableSection(
                  //   'Rating',
                  //   _expandedSections['Rating']!,
                  //   Column(
                  //     children: [
                  //       // Labels row
                  //       Row(
                  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //         children: [
                  //           _buildRatingLabel('3+', selectedRatingMin <= 3.0),
                  //           _buildRatingLabel(
                  //               '3.5+',
                  //               selectedRatingMin <= 3.5 &&
                  //                   selectedRatingMin > 3.0),
                  //           _buildRatingLabel(
                  //               '4+',
                  //               selectedRatingMin <= 4.0 &&
                  //                   selectedRatingMin > 3.5),
                  //           _buildRatingLabel(
                  //               '4.5+',
                  //               selectedRatingMin <= 4.5 &&
                  //                   selectedRatingMin > 4.0),
                  //           _buildRatingLabel('5', selectedRatingMax >= 5.0),
                  //         ],
                  //       ),
                  //       const SizedBox(height: 8),
                  //       // Custom slider with tick marks
                  //       Stack(
                  //         alignment: Alignment.center,
                  //         children: [
                  //           // Range slider
                  //           SliderTheme(
                  //             data: SliderTheme.of(context).copyWith(
                  //               activeTrackColor: const Color(0xFF1A1A1A),
                  //               inactiveTrackColor: const Color(0xFFE5E5E5),
                  //               thumbColor: const Color(0xFF1A1A1A),
                  //               thumbShape: const RoundSliderThumbShape(
                  //                   enabledThumbRadius: 8),
                  //               overlayShape: const RoundSliderOverlayShape(
                  //                   overlayRadius: 12),
                  //               trackHeight: 1,
                  //               rangeTrackShape:
                  //                   const RoundedRectRangeSliderTrackShape(),
                  //               rangeThumbShape:
                  //                   const RoundRangeSliderThumbShape(
                  //                 enabledThumbRadius: 5,
                  //                 pressedElevation: 2,
                  //                 elevation: 1,
                  //               ),
                  //               showValueIndicator: ShowValueIndicator.never,
                  //             ),
                  //             child: RangeSlider(
                  //               values: RangeValues(
                  //                   selectedRatingMin, selectedRatingMax),
                  //               min: 3.0,
                  //               max: 5.0,
                  //               divisions:
                  //                   8, // This gives us the precise increments (0.25 steps)
                  //               onChanged: (RangeValues values) {
                  //                 setState(() {
                  //                   selectedRatingMin = values.start;
                  //                   selectedRatingMax = values.end;
                  //                 });
                  //               },
                  //             ),
                  //           ),
                  //           // Tick marks layer positioned on the track
                  //           Positioned.fill(
                  //             child: Padding(
                  //               padding:
                  //                   const EdgeInsets.symmetric(horizontal: 24),
                  //               child: Row(
                  //                 mainAxisAlignment:
                  //                     MainAxisAlignment.spaceBetween,
                  //                 children: [
                  //                   _buildTickMark(3.0),
                  //                   _buildTickMark(3.5),
                  //                   _buildTickMark(4.0),
                  //                   _buildTickMark(4.5),
                  //                   _buildTickMark(5.0),
                  //                 ],
                  //               ),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // Pricing
                  // _buildExpandableSection(
                  //   'Pricing',
                  //   _expandedSections['Pricing']!,
                  //   Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //     children: [
                  //       _buildPriceButton('\$', '\$'),
                  //       _buildPriceButton('\$\$', '\$\$'),
                  //       _buildPriceButton('\$\$\$', '\$\$\$'),
                  //       _buildPriceButton('\$\$\$\$', '\$\$\$\$'),
                  //     ],
                  //   ),
                  // ),

                  // Dish Type
                  _buildExpandableSection(
                    'Dish Type',
                    _expandedSections['Dish Type']!,
                    Column(
                      children: (filterData.dishTypes ?? [])
                          .map((dishType) => _buildCustomCheckboxTile(
                                dishType.name!,
                                selectedDishTypes.contains(dishType.name),
                                (value) {
                                  setState(() {
                                    if (value!) {
                                      selectedDishTypes.add(dishType.name!);
                                    } else {
                                      selectedDishTypes.remove(dishType.name!);
                                    }
                                  });
                                },
                              ))
                          .toList(),
                    ),
                  ),

                  // Spice Level
                  _buildExpandableSection(
                    'Spice Level',
                    _expandedSections['Spice Level']!,
                    Column(
                      children: (filterData.spiceLevels ?? [])
                          .map((spiceLevel) => _buildCustomRadioTile(
                                spiceLevel.name!,
                                selectedSpiceLevel,
                                (value) {
                                  setState(() {
                                    selectedSpiceLevel = value!;
                                  });
                                },
                              ))
                          .toList(),
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCuisineHierarchy(dynamic cuisine) {
    final bool hasSubCuisines = cuisine.subCuisines?.isNotEmpty == true;
    final bool isExpanded = _expandedCuisines[cuisine.name] ?? false;
    final bool isSelected = selectedCuisines.contains(cuisine.name);

    return Column(
      children: [
        // Main cuisine with expand button
        Row(
          children: [
            InkWell(
              onTap: () {
                _handleCuisineSelection(
                  cuisine.name!,
                  cuisine.id!,
                  !isSelected,
                  subCuisines: cuisine.subCuisines,
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF1A1A1A)
                              : const Color(0xFFCCCCCC),
                          width: 2,
                        ),
                        color: isSelected
                            ? const Color(0xFF1A1A1A)
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 14,
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      cuisine.name!,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: isSelected
                            ? const Color(0xFF1A1A1A)
                            : const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            if (hasSubCuisines)
              InkWell(
                onTap: () => _toggleCuisineSection(cuisine.name!),
                child: AnimatedRotation(
                  turns: isExpanded ? 0.25 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: const Icon(
                    Icons.keyboard_arrow_right,
                    color: Color(0xFF666666),
                    size: 20,
                  ),
                ),
              ),
          ],
        ),

        // Subcuisines
        if (hasSubCuisines && isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Column(
              children: (cuisine.subCuisines ?? [])
                  .map<Widget>((subCuisine) => _buildSubCuisineHierarchy(
                        subCuisine,
                        parentCuisineName: cuisine.name,
                        parentCuisineId: cuisine.id,
                        allSubCuisines: cuisine.subCuisines,
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildSubCuisineHierarchy(dynamic subCuisine,
      {String? parentCuisineName,
      int? parentCuisineId,
      List<dynamic>? allSubCuisines}) {
    final bool hasLocalCuisines = subCuisine.localCuisines?.isNotEmpty == true;
    final bool isExpanded = _expandedCuisines[subCuisine.name] ?? false;
    final bool isSelected = selectedCuisines.contains(subCuisine.name);

    return Column(
      children: [
        // Subcuisine with expand button
        Row(
          children: [
            InkWell(
              onTap: () {
                _handleSubCuisineSelection(
                  subCuisine.name!,
                  subCuisine.id!,
                  !isSelected,
                  localCuisines: subCuisine.localCuisines,
                  parentCuisineName: parentCuisineName,
                  parentCuisineId: parentCuisineId,
                  allSubCuisines: allSubCuisines,
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF1A1A1A)
                              : const Color(0xFFCCCCCC),
                          width: 2,
                        ),
                        color: isSelected
                            ? const Color(0xFF1A1A1A)
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 14,
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      subCuisine.name!,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: isSelected
                            ? const Color(0xFF1A1A1A)
                            : const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            if (hasLocalCuisines)
              InkWell(
                onTap: () => _toggleCuisineSection(subCuisine.name!),
                child: AnimatedRotation(
                  turns: isExpanded ? 0.25 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: const Icon(
                    Icons.keyboard_arrow_right,
                    color: Color(0xFF666666),
                    size: 20,
                  ),
                ),
              ),
          ],
        ),

        // Local cuisines
        if (hasLocalCuisines && isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Column(
              children: (subCuisine.localCuisines ?? [])
                  .map<Widget>((localCuisine) => _buildCustomCheckboxTile(
                        localCuisine.name!,
                        selectedCuisines.contains(localCuisine.name),
                        (value) {
                          _handleLocalCuisineSelection(
                            localCuisine.name!,
                            localCuisine.id!,
                            value!,
                            parentSubCuisineName: subCuisine.name,
                            parentSubCuisineId: subCuisine.id,
                            allLocalCuisines: subCuisine.localCuisines,
                            grandParentCuisineName: parentCuisineName,
                            grandParentCuisineId: parentCuisineId,
                            allSubCuisines: allSubCuisines,
                          );
                        },
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildExpandableSection(
      String title, bool isExpanded, Widget content) {
    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () => _toggleSection(title),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                      color: Color(0xFF1A1A1A),
                    ),
                  ),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Color(0xFF666666),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: isExpanded ? null : 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: isExpanded ? 1.0 : 0.0,
              child: isExpanded
                  ? Container(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: content,
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomRadioTile(
      String title, String groupValue, ValueChanged<String?> onChanged) {
    final isSelected = title == groupValue;
    return InkWell(
      onTap: () => onChanged(title),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF1A1A1A)
                      : const Color(0xFFCCCCCC),
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(0xFF1A1A1A),
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: isSelected
                    ? const Color(0xFF1A1A1A)
                    : const Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCheckboxTile(
      String title, bool value, ValueChanged<bool?> onChanged) {
    return InkWell(
      onTap: () => onChanged(!value),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                border: Border.all(
                  color:
                      value ? const Color(0xFF1A1A1A) : const Color(0xFFCCCCCC),
                  width: 2,
                ),
                color: value ? const Color(0xFF1A1A1A) : Colors.transparent,
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 14,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color:
                      value ? const Color(0xFF1A1A1A) : const Color(0xFF666666),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //   // Helper method for building rating labels
  // Widget _buildRatingLabel(String text, bool isActive) {
  //   return Text(
  //     text,
  //     style: TextStyle(
  //       fontSize: 12,
  //       fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
  //       color: isActive ? const Color(0xFF1A1A1A) : const Color(0xFF999999),
  //       fontFamily: 'Inter',
  //     ),
  //   );
  // }

  // // Helper method to build tick marks
  // Widget _buildTickMark(double value) {
  //   bool isInRange = value >= selectedRatingMin && value <= selectedRatingMax;
  //   return Container(
  //     width: 8,
  //     height: 8,
  //     decoration: BoxDecoration(
  //       shape: BoxShape.circle,
  //       color: isInRange ? const Color(0xFF1A1A1A) : const Color(0xFFE5E5E5),
  //       border: Border.all(
  //         color: const Color(0xFF1A1A1A),
  //         width: 1,
  //       ),
  //     ),
  //   );
  // }

  Widget _buildPriceButton(String label, String value) {
    final isSelected = selectedPricing == value;
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton(
          onPressed: () {
            setState(() {
              selectedPricing = value;
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isSelected ? const Color(0xFFE5E5E5) : Colors.white,
            foregroundColor: const Color(0xFF1A1A1A),
            side: BorderSide(
              color: isSelected
                  ? const Color(0xFF1A1A1A)
                  : const Color(0xFFE5E5E5),
              width: 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            label,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  int _getShowTypeValue(String showType) {
    switch (showType) {
      case 'All':
        return 1;
      case 'Chefs':
        return 2;
      case 'Dishes':
        return 3;
      default:
        return 1;
    }
  }

  int _getSortByValue(String sortBy) {
    switch (sortBy) {
      case 'Recommended':
        return 1;
      case 'Rating':
        return 2;
      case 'Delivery Time':
        return 3;
      default:
        return 1;
    }
  }

  void clearFilters() async {
    setState(() {
      selectedShowType = 'All';
      selectedSortBy = 'Recommended';
      selectedCuisines.clear();
      selectedDietaries.clear();
      selectedRating = 3.0;
      selectedPricing = '\$\$';
      selectedDishTypes.clear();
      selectedSpiceLevel = 'No Spice';
      selectedCuisineIds.clear();
      selectedSubCuisineIds.clear();
      selectedLocalCuisineIds.clear();
    });
    await Initializer.setAppliedFilters(null);
    context.read<HomeBloc>().add(GetHomeDataEvent(
          data: <String, dynamic>{
            'latitude': Initializer.latitude ?? 0.0,
            'longitude': Initializer.longitude ?? 0.0,
          },
        ));
  }

  @override
  void initState() {
    super.initState();
    _loadSavedFilters();
  }

  Future<void> _loadSavedFilters() async {
    final savedFilters = await Initializer.getAppliedFilters();
    if (savedFilters != null && mounted) {
      setState(() {
        selectedCuisineIds
            .addAll(Set<int>.from(savedFilters['cuisine_ids'] ?? []));
        selectedSubCuisineIds
            .addAll(Set<int>.from(savedFilters['sub_cuisine_ids'] ?? []));
        selectedLocalCuisineIds
            .addAll(Set<int>.from(savedFilters['local_cuisine_ids'] ?? []));

        final filterData = Initializer.filterdataModel.data;
        if (filterData != null) {
          _restoreCuisineSelections(filterData.cuisines ?? []);

          final dietaryIds = Set<int>.from(savedFilters['dietary_ids'] ?? []);
          selectedDietaries.addAll(filterData.dietaries
                  ?.where((d) => dietaryIds.contains(d.id))
                  .map((d) => d.name!)
                  .toSet() ??
              {});

          final dishTypeIds = Set<int>.from(savedFilters['dishtype_ids'] ?? []);
          selectedDishTypes.addAll(filterData.dishTypes
                  ?.where((d) => dishTypeIds.contains(d.id))
                  .map((d) => d.name!)
                  .toSet() ??
              {});

          if (savedFilters['spice_level_ids']?.isNotEmpty == true) {
            final spiceLevelId = savedFilters['spice_level_ids'][0];
            try {
              final spiceLevel = filterData.spiceLevels
                  ?.firstWhere((s) => s.id == spiceLevelId);
              if (spiceLevel != null) {
                selectedSpiceLevel = spiceLevel.name!;
              }
            } catch (_) {}
          }
        }
      });
    }
  }

  void _restoreCuisineSelections(List<dynamic> cuisines) {
    for (var cuisine in cuisines) {
      if (selectedCuisineIds.contains(cuisine.id)) {
        selectedCuisines.add(cuisine.name!);

        // Check subcuisines
        if (cuisine.subCuisines != null) {
          for (var subCuisine in cuisine.subCuisines) {
            if (selectedSubCuisineIds.contains(subCuisine.id)) {
              selectedCuisines.add(subCuisine.name!);

              // Check local cuisines
              if (subCuisine.localCuisines != null) {
                for (var localCuisine in subCuisine.localCuisines) {
                  if (selectedLocalCuisineIds.contains(localCuisine.id)) {
                    selectedCuisines.add(localCuisine.name!);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
