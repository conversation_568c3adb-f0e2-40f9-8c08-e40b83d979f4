import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/deals/dealslistmodel.dart';
import 'package:db_eats/data/models/maskeddatamodel.dart';
import 'package:db_eats/data/models/settings/settinginfomodel.dart';
import 'package:db_eats/data/models/settings/settingschangemodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class MainBloc extends Bloc<MainEvent, MainState> {
  MainBloc() : super(MainState()) {
    on<CheckEmailEvent>(_checkEmail);
    on<SignUpEvent>(_signUp);
    on<VerifyPhoneEvent>(_verifyPhone);
    on<VerifyPhoneOTPEvent>(_verifyPhoneOTP);
    on<VerifyEmailEvent>(_verifyEmail);
    on<VerifyEmailOTPEvent>(_verifyEmailOTP);
    on<EmailSignInEvent>(_emailSignIn);
    on<MaskedPhoneEmailEvent>(_maskedPhoneEmail);
    on<ResetEmailOTPEvent>(_resetEmailOTP);
    on<ResetEmailOTPVerify>(_resetEmailOTPVerify);
    on<ResetPhoneOTPEvent>(_resetPhoneOTP);
    on<ResetPhoneOTPVerify>(_resetPhoneOTPVerify);
    on<SocialSignInEvent>(_googleSignIn);
    on<GoogleLoginEvenet>(_googleloginIn);
    on<DealsListEvent>(_dealsList);
    on<GetSettingsInfoEvent>(_getSettingsInfo);
    on<SettingsChangeEvent>(_settingsChange);
  }

  Future<void> _checkEmail(
      CheckEmailEvent event, Emitter<MainState> emit) async {
    Map<String, dynamic> data = event.issignin
        ? {
            'email': event.email,
            'is_signin': true,
          }
        : {'email': event.email};
    emit(CheckingEmail());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/check-email',
        data,
      );
      log('Response: ${response.toString()}');

      if (response['status'] == true) {
        final isEmailExists = response['data']['is_email_exists'] ?? false;
        if (isEmailExists) {
          emit(CheckingEmailSuccess('Email is registered'));
        } else {
          emit(
              CheckingEmailFailed(response['message'] ?? 'Email check failed'));
        }
      } else {
        emit(CheckingEmailFailed(response['message'] ?? 'Email check failed'));
      }
    } catch (e) {
      log('Error checking email: $e');
      emit(CheckingEmailFailed('Error occurred while checking email'));
    }
  }

  Future<void> _signUp(SignUpEvent event, Emitter<MainState> emit) async {
    emit(SigningUp());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/signup',
        {
          'signup_method': 'EMAIL',
          'email': event.email,
          'phone': event.phone,
          'first_name': event.firstName,
          'last_name': event.lastName,
          'password': event.password,
        },
      );

      if (response['status'] == true) {
        emit(SignUpSuccess(
          response['message'] ?? 'Signup successful',
          response['country_code'] ?? '',
        ));
      } else {
        emit(SignUpFailed(response['message'] ?? 'Signup failed'));
      }
    } catch (e) {
      log('Error during signup: $e');
      emit(SignUpFailed('Error occurred during signup'));
    }
  }

  Future<void> _verifyPhone(
      VerifyPhoneEvent event, Emitter<MainState> emit) async {
    emit(VerfyingPhone());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/get-phone-otp',
        {
          'phone': event.phone,
          'is_resend': event.isResend,
        },
      );

      if (response['status'] == true) {
        emit(
            VerifyPhoneSuccess(response['message'] ?? 'OTP sent successfully'));
      } else {
        emit(VerifyPhoneFailed(response['message'] ?? 'Failed to send OTP'));
      }
    } catch (e) {
      log('Error sending OTP: $e');
      emit(VerifyPhoneFailed('Error occurred while sending OTP'));
    }
  }

  Future<void> _verifyPhoneOTP(
      VerifyPhoneOTPEvent event, Emitter<MainState> emit) async {
    emit(VerifyingPhoneOTP());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/verify-phone-otp',
        {
          'phone': event.phone,
          'otp': event.otp,
        },
      );

      if (response['status'] == true) {
        await LocalStorage.setAccessToken(response['data']['access_token']);
        await LocalStorage.setRefreshToken(response['data']['refresh_token']);
        emit(VerifyPhoneOTPSuccess(
            response['message'] ?? 'OTP verified successfully'));
      } else {
        emit(VerifyPhoneOTPFailed(
            response['message'] ?? 'Failed to verify OTP'));
      }
    } catch (e) {
      log('Error verifying OTP: $e');
      emit(VerifyPhoneOTPFailed('Error occurred while verifying OTP'));
    }
  }

  Future<void> _verifyEmail(
      VerifyEmailEvent event, Emitter<MainState> emit) async {
    emit(VerifyingEmail());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/get-email-otp',
        {
          'email': event.email,
          'is_resend': event.isResend,
        },
      );

      if (response['status'] == true) {
        emit(
            VerifyEmailSuccess(response['message'] ?? 'OTP sent successfully'));
      } else {
        emit(VerifyEmailFailed(response['message'] ?? 'Failed to send OTP'));
      }
    } catch (e) {
      log('Error sending email OTP: $e');
      emit(VerifyEmailFailed('Error occurred while sending OTP'));
    }
  }

  Future<void> _verifyEmailOTP(
      VerifyEmailOTPEvent event, Emitter<MainState> emit) async {
    emit(VerifyingEmailOTP());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/verify-email-otp',
        {
          'email': event.email,
          'otp': event.otp,
        },
      );

      if (response['status'] == true) {
        await LocalStorage.setAccessToken(response['data']['access_token']);
        await LocalStorage.setRefreshToken(response['data']['refresh_token']);
        emit(VerifyEmailOTPSuccess(
            response['message'] ?? 'Email verified successfully'));
      } else {
        emit(VerifyEmailOTPFailed(
            response['message'] ?? 'Failed to verify email'));
      }
    } catch (e) {
      log('Error verifying email OTP: $e');
      emit(VerifyEmailOTPFailed('Error occurred while verifying email'));
    }
  }

  Future<void> _emailSignIn(
      EmailSignInEvent event, Emitter<MainState> emit) async {
    emit(EmailSigningIn());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/email-signin',
        {
          'email': event.email,
          'password': event.password,
        },
      );

      if (response['status'] == true) {
        if (response['data'] != null) {
          // Store tokens
          await LocalStorage.setAccessToken(response['data']['access_token']);
          await LocalStorage.setRefreshToken(response['data']['refresh_token']);
          emit(EmailSignInSuccess(response['message'] ?? 'Login successful'));
        }
      } else {
        if (response['is_account_verified'] == false) {
          emit(EmailSignInVerificationRequired(
            email: response['email'] ?? '',
            phone: response['phone'] ?? '',
            countryCode: response['country_code'] ?? '',
            message: response['message'] ?? 'Account verification required',
          ));
        } else {
          emit(EmailSignInFailed(response['message'] ?? 'Login failed'));
        }
      }
    } catch (e) {
      log('Error during email signin: $e');
      emit(EmailSignInFailed('Error occurred during signin'));
    }
  }

  Future<void> _maskedPhoneEmail(
      MaskedPhoneEmailEvent event, Emitter<MainState> emit) async {
    emit(MaskedPhoneEmailLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/forgot_password/email-phone-mask',
        {'email': event.email},
      );

      final maskedData = MaskedPhoneEmailModel.fromJson(response);

      if (maskedData.status == true) {
        if (maskedData.data != null) {
          emit(MaskedPhoneEmailSuccess(
            maskedPhone: maskedData.data?.maskedPhone ?? '',
            maskedEmail: maskedData.data?.maskedEmail ?? '',
          ));
        } else {
          emit(MaskedPhoneEmailFailed('No data received'));
        }
      } else {
        emit(MaskedPhoneEmailFailed(maskedData.message ?? 'Request failed'));
      }
    } catch (e) {
      log('Error getting masked data: $e');
      emit(MaskedPhoneEmailFailed('Error occurred while getting masked data'));
    }
  }

  Future<void> _resetEmailOTP(
      ResetEmailOTPEvent event, Emitter<MainState> emit) async {
    emit(ResetEmailOTPSending());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/forgot_password/send-email-otp',
        {'email': event.email},
      );

      if (response['status'] == true) {
        emit(ResetEmailOTPSuccess(
            response['message'] ?? 'OTP sent successfully'));
      } else {
        emit(ResetEmailOTPFailed(response['message'] ?? 'Failed to send OTP'));
      }
    } catch (e) {
      log('Error sending reset email OTP: $e');
      emit(ResetEmailOTPFailed('Error occurred while sending OTP'));
    }
  }

  Future<void> _resetEmailOTPVerify(
      ResetEmailOTPVerify event, Emitter<MainState> emit) async {
    emit(ResetEmailOTPVerifying());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/forgot_password/verify-email-otp',
        {
          'email': event.email,
          'new_password': event.newPassword,
          'otp': event.otp,
        },
      );

      if (response['status'] == true) {
        emit(ResetEmailOTPVerifySuccess(
            response['message'] ?? 'Password reset successful'));
      } else {
        emit(ResetEmailOTPVerifyFailed(
            response['message'] ?? 'Failed to reset password'));
      }
    } catch (e) {
      log('Error verifying reset email OTP: $e');
      emit(
          ResetEmailOTPVerifyFailed('Error occurred while resetting password'));
    }
  }

  Future<void> _resetPhoneOTP(
      ResetPhoneOTPEvent event, Emitter<MainState> emit) async {
    emit(ResetPhoneOTPSending());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/forgot_password/send-phone-otp',
        {'email': event.email},
      );

      if (response['status'] == true) {
        emit(ResetPhoneOTPSuccess(
            response['message'] ?? 'OTP sent successfully'));
      } else {
        emit(ResetPhoneOTPFailed(response['message'] ?? 'Failed to send OTP'));
      }
    } catch (e) {
      log('Error sending reset phone OTP: $e');
      emit(ResetPhoneOTPFailed('Error occurred while sending OTP'));
    }
  }

  Future<void> _resetPhoneOTPVerify(
      ResetPhoneOTPVerify event, Emitter<MainState> emit) async {
    emit(ResetPhoneOTPVerifying());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/forgot_password/verify-phone-otp',
        {
          'email': event.email,
          'new_password': event.newPassword,
          'otp': event.otp,
        },
      );

      if (response['status'] == true) {
        emit(ResetPhoneOTPVerifySuccess(
            response['message'] ?? 'Password reset successful'));
      } else {
        emit(ResetPhoneOTPVerifyFailed(
            response['message'] ?? 'Failed to reset password'));
      }
    } catch (e) {
      log('Error verifying reset phone OTP: $e');
      emit(
          ResetPhoneOTPVerifyFailed('Error occurred while resetting password'));
    }
  }

  FutureOr<void> _googleSignIn(
      SocialSignInEvent event, Emitter<MainState> emit) async {
    emit(SigningUp());
    Map<String, dynamic> data = {
      'signup_method': event.signintype,
      'id_token': event.idToken,
      'first_name': event.firstName,
      'last_name': event.lastName,
    };
    try {
      final response =
          await ServerHelper.post1('/v1/customer/auth/signup', data);
      log('Google SignIn Response: ${response.toString()}');

      if (response['status'] == true) {
        await LocalStorage.setAccessToken(
            response['data']['access_token'] ?? "");
        await LocalStorage.setRefreshToken(
            response['data']['refresh_token'] ?? "");
        emit(SocialSignUpSuccess(
          response['message'] ?? 'Signup successful',
          response['country_code'] ?? '',
        ));
      } else {
        emit(SocialSignUpFailed(response['message'] ?? 'Signup failed'));
      }
    } catch (e) {
      log('Error during signup: $e');
      emit(SocialSignUpFailed('Error occurred during signup'));
    }
  }

  FutureOr<void> _googleloginIn(
      GoogleLoginEvenet event, Emitter<MainState> emit) async {
    emit(EmailSigningIn());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/auth/third-party-signin',
        {
          'signup_method': event.signmethod,
          'id_token': event.idToken,
        },
      );

      if (response['status'] == true) {
        // if (response['data'] != null) {
        // Store tokens
        await LocalStorage.setAccessToken(response['data']['access_token']);
        await LocalStorage.setRefreshToken(response['data']['refresh_token']);
        log(response['data']['access_token']);
        log(response['data']['refresh_token']);
        emit(EmailSignInSuccess(response['message'] ?? 'Login successful'));
        // }
      } else {
        emit(EmailSignInFailed(response['message'] ?? 'Login failed'));
      }
    } catch (e) {
      log('Error during email signin: $e');
      emit(EmailSignInFailed('Error occurred during signin'));
    }
  }

  FutureOr<void> _dealsList(
      DealsListEvent event, Emitter<MainState> emit) async {
    emit(DealsListLoading());
    Map<String, dynamic> data = {"latitude": event.lat, "longitude": event.lon};
    try {
      Initializer.dealslistmodel = DealslistModel.fromJson(
        await ServerHelper.post1('/v1/customer/home/<USER>', data),
      );
      log(Initializer.dealslistmodel.data.toString());

      if (Initializer.dealslistmodel.status!) {
        emit(DealsListSuccess());
      } else {
        emit(DealsListFailed());
      }
    } catch (e) {
      log('Error fetching deals: $e');
      emit(DealsListFailed());
    }
  }

  FutureOr<void> _getSettingsInfo(
      GetSettingsInfoEvent event, Emitter<MainState> emit) async {
    emit(GetSettingsInfoLoadingState());
    try {
      Initializer.settingsInfoModel = SettingsInfoModel.fromJson(
          await ServerHelper.get1('/v1/customer/settings/get'));

      if (Initializer.settingsInfoModel.statusCode == 200 &&
          Initializer.settingsInfoModel.status == true) {
        emit(GetSettingsInfoSuccess());
      } else {
        emit(GetSettingsInfoFailed());
      }
    } catch (e) {
      log('Error fetching settings info: $e');
      emit(GetSettingsInfoFailed());
    }
  }

  FutureOr<void> _settingsChange(
      SettingsChangeEvent event, Emitter<MainState> emit) async {
    emit(SettingsChangeLoadingState());
    try {
      Initializer.settingsChangeModel = SettingsChangeModel.fromJson(
        await ServerHelper.post1('/v1/customer/settings/update', event.data),
      );
      if (Initializer.settingsChangeModel.statusCode == 200 &&
          Initializer.settingsChangeModel.status == true) {
        add(GetSettingsInfoEvent());
        emit(SettingsChangeSuccess());
      } else {
        emit(SettingsChangeFailed());
      }
    } catch (e) {
      log('Error changing settings: $e');
      emit(SettingsChangeFailed());
    }
  }
}

class MainEvent {}

class MainState {}

// // Events
class CheckEmailEvent extends MainEvent {
  final String email;
  final bool issignin;
  CheckEmailEvent({required this.email, required this.issignin});
}

class SignUpEvent extends MainEvent {
  final String email;
  final String phone;
  final String firstName;
  final String lastName;
  final String password;

  SignUpEvent({
    required this.email,
    required this.phone,
    required this.firstName,
    required this.lastName,
    required this.password,
  });
}

class VerifyPhoneEvent extends MainEvent {
  final String phone;
  final bool isResend;

  VerifyPhoneEvent({
    required this.phone,
    this.isResend = false,
  });
}

class VerifyPhoneOTPEvent extends MainEvent {
  final String phone;
  final String otp;

  VerifyPhoneOTPEvent({
    required this.phone,
    required this.otp,
  });
}

class VerifyEmailEvent extends MainEvent {
  final String email;
  final bool isResend;

  VerifyEmailEvent({
    required this.email,
    this.isResend = false,
  });
}

class VerifyEmailOTPEvent extends MainEvent {
  final String email;
  final String otp;

  VerifyEmailOTPEvent({
    required this.email,
    required this.otp,
  });
}

class EmailSignInEvent extends MainEvent {
  final String email;
  final String password;

  EmailSignInEvent({
    required this.email,
    required this.password,
  });
}

class MaskedPhoneEmailEvent extends MainEvent {
  final String email;
  MaskedPhoneEmailEvent({required this.email});
}

class ResetEmailOTPEvent extends MainEvent {
  final String email;
  ResetEmailOTPEvent({required this.email});
}

class ResetEmailOTPVerify extends MainEvent {
  final String email;
  final String newPassword;
  final String otp;

  ResetEmailOTPVerify({
    required this.email,
    required this.newPassword,
    required this.otp,
  });
}

class ResetPhoneOTPEvent extends MainEvent {
  final String email;

  ResetPhoneOTPEvent({
    required this.email,
  });
}

class ResetPhoneOTPVerify extends MainEvent {
  final String email;
  final String newPassword;
  final String otp;

  ResetPhoneOTPVerify({
    required this.email,
    required this.newPassword,
    required this.otp,
  });
}

// States
class CheckingEmail extends MainState {}

class CheckingEmailSuccess extends MainState {
  final String message;
  CheckingEmailSuccess(this.message);
}

class CheckingEmailFailed extends MainState {
  final String message;
  CheckingEmailFailed(this.message);
}

class SigningUp extends MainState {}

class SignUpSuccess extends MainState {
  final String message;
  final String countryCode;

  SignUpSuccess(this.message, this.countryCode);
}

class SignUpFailed extends MainState {
  final String message;
  SignUpFailed(this.message);
}

class SocialSignUpSuccess extends MainState {
  final String message;
  final String countryCode;

  SocialSignUpSuccess(this.message, this.countryCode);
}

class SocialSignUpFailed extends MainState {
  final String message;
  SocialSignUpFailed(this.message);
}

class VerfyingPhone extends MainState {}

class VerifyPhoneSuccess extends MainState {
  final String message;
  VerifyPhoneSuccess(this.message);
}

class VerifyPhoneFailed extends MainState {
  final String message;
  VerifyPhoneFailed(this.message);
}

class VerifyingPhoneOTP extends MainState {}

class VerifyPhoneOTPSuccess extends MainState {
  final String message;
  VerifyPhoneOTPSuccess(this.message);
}

class VerifyPhoneOTPFailed extends MainState {
  final String message;
  VerifyPhoneOTPFailed(this.message);
}

class VerifyingEmail extends MainState {}

class VerifyEmailSuccess extends MainState {
  final String message;
  VerifyEmailSuccess(this.message);
}

class VerifyEmailFailed extends MainState {
  final String message;
  VerifyEmailFailed(this.message);
}

class VerifyingEmailOTP extends MainState {}

class VerifyEmailOTPSuccess extends MainState {
  final String message;
  VerifyEmailOTPSuccess(this.message);
}

class VerifyEmailOTPFailed extends MainState {
  final String message;
  VerifyEmailOTPFailed(this.message);
}

class EmailSigningIn extends MainState {}

class EmailSignInSuccess extends MainState {
  final String message;
  EmailSignInSuccess(this.message);
}

class EmailSignInFailed extends MainState {
  final String message;
  EmailSignInFailed(this.message);
}

class EmailSignInVerificationRequired extends MainState {
  final String email;
  final String phone;
  final String countryCode;
  final String message;

  EmailSignInVerificationRequired({
    required this.email,
    required this.phone,
    required this.countryCode,
    required this.message,
  });
}

class MaskedPhoneEmailLoading extends MainState {}

class MaskedPhoneEmailSuccess extends MainState {
  final String maskedPhone;
  final String maskedEmail;

  MaskedPhoneEmailSuccess({
    required this.maskedPhone,
    required this.maskedEmail,
  });
}

class MaskedPhoneEmailFailed extends MainState {
  final String message;
  MaskedPhoneEmailFailed(this.message);
}

class ResetEmailOTPSending extends MainState {}

class ResetEmailOTPSuccess extends MainState {
  final String message;
  ResetEmailOTPSuccess(this.message);
}

class ResetEmailOTPFailed extends MainState {
  final String message;
  ResetEmailOTPFailed(this.message);
}

class ResetEmailOTPVerifying extends MainState {}

class ResetEmailOTPVerifySuccess extends MainState {
  final String message;
  ResetEmailOTPVerifySuccess(this.message);
}

class ResetEmailOTPVerifyFailed extends MainState {
  final String message;
  ResetEmailOTPVerifyFailed(this.message);
}

class ResetPhoneOTPSending extends MainState {}

class ResetPhoneOTPSuccess extends MainState {
  final String message;
  ResetPhoneOTPSuccess(this.message);
}

class ResetPhoneOTPFailed extends MainState {
  final String message;
  ResetPhoneOTPFailed(this.message);
}

class ResetPhoneOTPVerifying extends MainState {}

class ResetPhoneOTPVerifySuccess extends MainState {
  final String message;
  ResetPhoneOTPVerifySuccess(this.message);
}

class ResetPhoneOTPVerifyFailed extends MainState {
  final String message;
  ResetPhoneOTPVerifyFailed(this.message);
}

class GoogleSignInEvent extends MainEvent {
  final String signintype;
  final String idToken;
  final String firstName;
  final String lastName;

  GoogleSignInEvent({
    required this.signintype,
    required this.idToken,
    required this.firstName,
    required this.lastName,
  });
}

class SocialSignInEvent extends MainEvent {
  final String signintype;
  final String idToken;
  final String firstName;
  final String lastName;

  SocialSignInEvent({
    required this.signintype,
    required this.idToken,
    required this.firstName,
    required this.lastName,
  });
}

class GoogleLoginEvenet extends MainEvent {
  final String signmethod;
  final String idToken;

  GoogleLoginEvenet({
    required this.signmethod,
    required this.idToken,
  });
}

//Deals List
class DealsListEvent extends MainEvent {
  final int page;
  final int limit;
  final double lat;
  final double lon;

  DealsListEvent({
    required this.page,
    required this.limit,
    required this.lat,
    required this.lon,
  });
}

class DealsListLoading extends MainState {}

class DealsListSuccess extends MainState {}

class DealsListFailed extends MainState {}

//get Settings info

class GetSettingsInfoEvent extends MainEvent {}

class GetSettingsInfoLoadingState extends MainState {}

class GetSettingsInfoSuccess extends MainState {}

class GetSettingsInfoFailed extends MainState {}

// Settings  Change
class SettingsChangeEvent extends MainEvent {
  final Map<String, dynamic> data;

  SettingsChangeEvent({required this.data});
}

class SettingsChangeLoadingState extends MainState {}

class SettingsChangeSuccess extends MainState {}

class SettingsChangeFailed extends MainState {}
