class GetOrderSummaryModel {
  bool? status;
  OrderData? data;
  int? statusCode;

  GetOrderSummaryModel({this.status, this.data, this.statusCode});

  GetOrderSummaryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new OrderData.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = this.statusCode;
    return data;
  }
}

class OrderData {
  Chef? chef;
  List<Items>? items;
  int? walletBalance; // NEW FIELD
  int? totalPrice;
  int? walletCredits;
  int? taxPercentage; // NEW FIELD
  num? taxesAndFees;
  double? discount;
  double? deliveryFee; // NEW FIELD
  double? serviceFee; // NEW FIELD
  double? finalTotal;
  ChefOperationTime? customerTimePreference;
  CurrentAddress? currentAddress;

  OrderData(
      {this.chef,
      this.items,
      this.walletBalance, // NEW FIELD
      this.totalPrice,
      this.walletCredits,
      this.taxPercentage, // NEW FIELD
      this.taxesAndFees,
      this.discount,
      this.deliveryFee, // NEW FIELD
      this.serviceFee, // NEW FIELD
      this.finalTotal,
      this.customerTimePreference,
      this.currentAddress});

  OrderData.fromJson(Map<String, dynamic> json) {
    chef = json['chef'] != null ? new Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    walletBalance = json['wallet_balance']; // NEW FIELD
    totalPrice = json['total_price'];
    walletCredits = json['wallet_credits'];
    taxPercentage = json['tax_percentage']; // NEW FIELD
    taxesAndFees = json['taxes_and_fees'];
    discount = json['discount'] != null ? json['discount'].toDouble() : null;
    deliveryFee = json['delivery_fee'] != null
        ? json['delivery_fee'].toDouble()
        : null; // NEW FIELD
    serviceFee = json['service_fee'] != null
        ? json['service_fee'].toDouble()
        : null; // NEW FIELD
    finalTotal =
        json['final_total'] != null ? json['final_total'].toDouble() : null;
    customerTimePreference = json['customer_time_preference'] != null
        ? new ChefOperationTime.fromJson(json['customer_time_preference'])
        : null;
    currentAddress = json['current_address'] != null
        ? new CurrentAddress.fromJson(json['current_address'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.chef != null) {
      data['chef'] = this.chef!.toJson();
    }
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['wallet_balance'] = this.walletBalance; // NEW FIELD
    data['total_price'] = this.totalPrice;
    data['wallet_credits'] = this.walletCredits;
    data['tax_percentage'] = this.taxPercentage; // NEW FIELD
    data['taxes_and_fees'] = this.taxesAndFees;
    data['discount'] = this.discount;
    data['delivery_fee'] = this.deliveryFee; // NEW FIELD
    data['service_fee'] = this.serviceFee; // NEW FIELD
    data['final_total'] = this.finalTotal;
    if (this.customerTimePreference != null) {
      data['customer_time_preference'] = this.customerTimePreference!.toJson();
    }
    if (this.currentAddress != null) {
      data['current_address'] = this.currentAddress!.toJson();
    }
    return data;
  }
}

class Chef {
  int? chefId;
  String? chefName;
  String? chefPhoto;
  List<String>? chefOperationDays;
  ChefOperationTime? chefOperationTime;

  Chef(
      {this.chefId,
      this.chefName,
      this.chefPhoto,
      this.chefOperationDays,
      this.chefOperationTime});

  Chef.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefPhoto = json['chef_photo'];
    chefOperationDays = json['chef_operation_days'].cast<String>();
    chefOperationTime = json['chef_operation_time'] != null
        ? new ChefOperationTime.fromJson(json['chef_operation_time'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chef_id'] = this.chefId;
    data['chef_name'] = this.chefName;
    data['chef_photo'] = this.chefPhoto;
    data['chef_operation_days'] = this.chefOperationDays;
    if (this.chefOperationTime != null) {
      data['chef_operation_time'] = this.chefOperationTime!.toJson();
    }
    return data;
  }
}

class ChefOperationTime {
  int? id;
  String? startTime;
  String? endTime;

  ChefOperationTime({this.id, this.startTime, this.endTime});

  ChefOperationTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}

class Items {
  int? cartItemId;
  int? dishId;
  String? dishName;
  String? dishPhoto;
  int? quantity;
  int? servingSizeId;
  int? price;
  int? totalPrice;
  String? notes; // Changed from Null? to String?

  Items(
      {this.cartItemId,
      this.dishId,
      this.dishName,
      this.dishPhoto,
      this.quantity,
      this.servingSizeId,
      this.price,
      this.totalPrice,
      this.notes});

  Items.fromJson(Map<String, dynamic> json) {
    cartItemId = json['cart_item_id'];
    dishId = json['dish_id'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    quantity = json['quantity'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    totalPrice = json['total_price'];
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cart_item_id'] = this.cartItemId;
    data['dish_id'] = this.dishId;
    data['dish_name'] = this.dishName;
    data['dish_photo'] = this.dishPhoto;
    data['quantity'] = this.quantity;
    data['serving_size_id'] = this.servingSizeId;
    data['price'] = this.price;
    data['total_price'] = this.totalPrice;
    data['notes'] = this.notes;
    return data;
  }
}

class CurrentAddress {
  int? id;
  String? addressText;
  Location? location;
  bool? isCurrent;

  CurrentAddress({this.id, this.addressText, this.location, this.isCurrent});

  CurrentAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    addressText = json['address_text'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    isCurrent = json['is_current'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['address_text'] = this.addressText;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['is_current'] = this.isCurrent;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}
