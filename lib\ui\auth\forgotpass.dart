import 'package:db_eats/ui/auth/forgotpassverify.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Forgot Password',
      theme: ThemeData(
        primarySwatch: Colors.grey,
        fontFamily: 'Inter',
      ),
      home: const Forgotpass(),
    );
  }
}

class Forgotpass extends StatefulWidget {
  final String email;
  final String phone;

  const Forgotpass({Key? key, this.email = "", this.phone = ""})
      : super(key: key);

  @override
  State<Forgotpass> createState() => _ForgotpassState();
}

class _ForgotpassState extends State<Forgotpass> {
  int _selectedVerificationMethod = -1;
  String? maskedEmail;
  String? maskedPhone;

  @override
  void initState() {
    super.initState();
    if (widget.email.isNotEmpty) {
      context.read<MainBloc>().add(MaskedPhoneEmailEvent(email: widget.email));
    }
  }

  void _handleVerificationSelection(int index) {
    setState(() {
      _selectedVerificationMethod = index;
    });

    if (index == 0) {
      // Email verification
      context.read<MainBloc>().add(ResetEmailOTPEvent(email: widget.email));
    } else if (index == 1) {
      // Phone verification
      context.read<MainBloc>().add(ResetPhoneOTPEvent(email: widget.email));
    }
  }

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  EdgeInsets getResponsivePadding(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;

    if (size.width < 360) {
      return EdgeInsets.all(size.width * 0.03);
    } else if (size.width < 600) {
      return EdgeInsets.all(size.width * 0.04);
    } else {
      return EdgeInsets.all(
          isLandscape ? size.width * 0.03 : size.width * 0.05);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is MaskedPhoneEmailSuccess) {
          setState(() {
            maskedEmail = state.maskedEmail;
            maskedPhone = state.maskedPhone;
          });
        } else if (state is ResetEmailOTPSuccess ||
            state is ResetPhoneOTPSuccess) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ForgotPassVerify(
                email: widget.email,
                maskedEmail: maskedEmail ?? "",
                phone: maskedPhone ?? "",
                isPhone: _selectedVerificationMethod == 1,
              ),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    left: size.width * 0.04, top: size.height * 0.02),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                    width: size.width * 0.1,
                    height: size.width * 0.1,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.close,
                        color: Colors.black,
                        size: baseTextSize * 1.2,
                      ),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.all(size.width * 0.02),
                      constraints: const BoxConstraints(),
                    ),
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.06),
              Center(
                child: Text(
                  "Password request",
                  style: TextStyle(
                    fontSize:
                        isLandscape ? size.height * 0.04 :    ResponsiveSizes.wp(context, 6), // gives 23.8

                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF1F2122),
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.01),
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
                  child: Text(
                    "Chooose how you want to change your\npassword:",
                    style: TextStyle(
                      fontSize: baseTextSize * 1.15  // = 16.1 (very close to 16)
,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF414346),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.04),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: _buildSimpleRadioOption(
                  title: 'Verify with email',
                  value: maskedEmail ?? "",
                  index: 0,
                ),
              ),
              SizedBox(height: size.height * 0.015),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: _buildSimpleRadioOption(
                  title: 'Verify with phone number',
                  value: maskedPhone ?? "",
                  index: 1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleRadioOption({
    required String title,
    required String value,
    required int index,
  }) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);

    return Container(
      height: size.height * 0.07,
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFB9B6AD),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => _handleVerificationSelection(index),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.03),
              child: Radio<int>(
                value: index,
                groupValue: _selectedVerificationMethod,
                onChanged: (int? value) => _handleVerificationSelection(value!),
                activeColor: Colors.black,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '$title ',
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ],
                ),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
class ResponsiveSizes {
  static double wp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.width * (percentage / 100);

  static double hp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.height * (percentage / 100);
}
